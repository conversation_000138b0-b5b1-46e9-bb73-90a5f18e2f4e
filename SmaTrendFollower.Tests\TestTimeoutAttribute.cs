using System;
using Xunit;

namespace SmaTrendFollower.Tests;

/// <summary>
/// Attribute to specify test timeout in milliseconds
/// </summary>
[AttributeUsage(AttributeTargets.Method | AttributeTargets.Class)]
public class TestTimeoutAttribute : Attribute
{
    public int TimeoutMs { get; }

    public TestTimeoutAttribute(int timeoutMs)
    {
        TimeoutMs = timeoutMs;
    }
}

/// <summary>
/// Predefined timeout constants for different test categories
/// </summary>
public static class TestTimeouts
{
    /// <summary>
    /// Fast unit tests - 5 seconds
    /// </summary>
    public const int Unit = 5_000;

    /// <summary>
    /// Integration tests - 30 seconds
    /// </summary>
    public const int Integration = 30_000;

    /// <summary>
    /// Performance tests - 60 seconds
    /// </summary>
    public const int Performance = 60_000;

    /// <summary>
    /// Database tests - 15 seconds
    /// </summary>
    public const int Database = 15_000;

    /// <summary>
    /// Network/API tests - 45 seconds
    /// </summary>
    public const int Network = 45_000;

    /// <summary>
    /// Streaming tests - 90 seconds
    /// </summary>
    public const int Streaming = 90_000;
}
