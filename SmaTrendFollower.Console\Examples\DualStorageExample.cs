using Microsoft.Extensions.Logging;
using SmaTrendFollower.Services;
using Alpaca.Markets;

namespace SmaTrendFollower.Examples;

/// <summary>
/// Example demonstrating the dual storage system (Redis + SQLite).
/// Shows how to use live state store for real-time data and historical bar store for persistent data.
/// </summary>
public class DualStorageExample
{
    private readonly ILiveStateStore _liveStateStore;
    private readonly IBarStore _historicalStore;
    private readonly ILogger<DualStorageExample> _logger;

    public DualStorageExample(
        ILiveStateStore liveStateStore,
        IBarStore historicalStore,
        ILogger<DualStorageExample> logger)
    {
        _liveStateStore = liveStateStore;
        _historicalStore = historicalStore;
        _logger = logger;
    }

    /// <summary>
    /// Demonstrates basic dual storage operations
    /// </summary>
    public async Task RunExampleAsync()
    {
        _logger.LogInformation("=== Dual Storage System Example ===");

        try
        {
            await DemonstrateRedisOperationsAsync();
            await DemonstrateSQLiteOperationsAsync();
            await DemonstrateIntegratedWorkflowAsync();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error during dual storage example");
        }
    }

    /// <summary>
    /// Demonstrates Redis live state operations
    /// </summary>
    private async Task DemonstrateRedisOperationsAsync()
    {
        _logger.LogInformation("--- Redis Live State Operations ---");

        // Trailing stop management
        await _liveStateStore.SetTrailingStopAsync("AAPL", 150.25m);
        await _liveStateStore.SetTrailingStopAsync("MSFT", 300.50m);
        
        var aaplStop = await _liveStateStore.GetTrailingStopAsync("AAPL");
        _logger.LogInformation("AAPL trailing stop: {Stop:F2}", aaplStop);

        var allStops = await _liveStateStore.GetAllTrailingStopsAsync();
        _logger.LogInformation("All trailing stops: {Count}", allStops.Count);

        // Signal deduplication
        var today = DateTime.UtcNow.Date;
        await _liveStateStore.FlagSignalAsync("AAPL", today);
        
        var wasSignaled = await _liveStateStore.WasSignaledAsync("AAPL", today);
        _logger.LogInformation("AAPL was signaled today: {WasSignaled}", wasSignaled);

        // Position state tracking
        var positionState = new PositionState(
            "AAPL",
            150.00m,
            152.50m,
            148.00m,
            100,
            DateTime.UtcNow.AddHours(-2),
            DateTime.UtcNow
        );

        await _liveStateStore.SetPositionStateAsync("AAPL", positionState);
        var retrievedState = await _liveStateStore.GetPositionStateAsync("AAPL");
        _logger.LogInformation("Position state for AAPL: Entry={Entry:F2}, Current={Current:F2}, Stop={Stop:F2}",
            retrievedState?.EntryPrice, retrievedState?.CurrentPrice, retrievedState?.StopPrice);

        // Retry queue
        var retryItem = new RetryItem(
            Guid.NewGuid().ToString(),
            "PlaceOrder",
            "{\"symbol\":\"AAPL\",\"quantity\":100}",
            1,
            DateTime.UtcNow.AddMinutes(5),
            DateTime.UtcNow
        );

        await _liveStateStore.EnqueueRetryAsync(retryItem);
        var queueLength = await _liveStateStore.GetRetryQueueLengthAsync();
        _logger.LogInformation("Retry queue length: {Length}", queueLength);

        // Market state caching
        var marketData = new { VIX = 18.5, SPYTrend = "Bullish", LastUpdate = DateTime.UtcNow };
        await _liveStateStore.SetMarketStateAsync("market_conditions", marketData, TimeSpan.FromMinutes(15));
        
        var cachedMarketData = await _liveStateStore.GetMarketStateAsync<object>("market_conditions");
        _logger.LogInformation("Cached market data retrieved: {HasData}", cachedMarketData != null);

        // Health check
        var isHealthy = await _liveStateStore.IsHealthyAsync();
        _logger.LogInformation("Redis health status: {IsHealthy}", isHealthy);

        // Statistics
        var stats = await _liveStateStore.GetStatsAsync();
        _logger.LogInformation("Live state stats: Stops={Stops}, Signals={Signals}, Positions={Positions}, Retry={Retry}",
            stats.TrailingStopCount, stats.SignalFlagCount, stats.PositionStateCount, stats.RetryQueueLength);
    }

    /// <summary>
    /// Demonstrates SQLite historical storage operations
    /// </summary>
    private async Task DemonstrateSQLiteOperationsAsync()
    {
        _logger.LogInformation("--- SQLite Historical Storage Operations ---");

        // Create sample bar data
        var sampleBars = CreateSampleBars("AAPL", DateTime.UtcNow.Date.AddDays(-10), 10);
        
        // Save bars
        await _historicalStore.SaveBarsAsync("AAPL", "Day", sampleBars);
        _logger.LogInformation("Saved {Count} sample bars for AAPL", sampleBars.Count);

        // Load bars
        var fromDate = DateTime.UtcNow.Date.AddDays(-10);
        var toDate = DateTime.UtcNow.Date;
        var loadedBars = await _historicalStore.LoadBarsAsync("AAPL", "Day", fromDate, toDate);
        _logger.LogInformation("Loaded {Count} bars for AAPL from {From:yyyy-MM-dd} to {To:yyyy-MM-dd}",
            loadedBars.Count, fromDate, toDate);

        // Check cache dates
        var latestDate = await _historicalStore.GetLatestCachedDateAsync("AAPL", "Day");
        var earliestDate = await _historicalStore.GetEarliestCachedDateAsync("AAPL", "Day");
        _logger.LogInformation("AAPL cache range: {Earliest:yyyy-MM-dd} to {Latest:yyyy-MM-dd}",
            earliestDate, latestDate);

        // Check if bars exist
        var hasBars = await _historicalStore.HasBarsAsync("AAPL", "Day", fromDate, toDate);
        _logger.LogInformation("Has complete bar data for range: {HasBars}", hasBars);

        // Get bar count
        var barCount = await _historicalStore.GetBarCountAsync("AAPL", "Day");
        _logger.LogInformation("Total bars for AAPL: {Count}", barCount);

        // Get storage statistics
        var storageStats = await _historicalStore.GetStatsAsync();
        _logger.LogInformation("Storage stats: Symbols={Symbols}, Bars={Bars:N0}, Size={Size:N0} bytes",
            storageStats.TotalSymbols, storageStats.TotalBars, storageStats.StorageSizeBytes);
    }

    /// <summary>
    /// Demonstrates integrated workflow using both storage systems
    /// </summary>
    private async Task DemonstrateIntegratedWorkflowAsync()
    {
        _logger.LogInformation("--- Integrated Dual Storage Workflow ---");

        var symbol = "MSFT";
        var currentPrice = 305.75m;
        var stopPrice = 300.00m;

        // 1. Check if we already signaled this symbol today (Redis)
        var today = DateTime.UtcNow.Date;
        var alreadySignaled = await _liveStateStore.WasSignaledAsync(symbol, today);
        
        if (alreadySignaled)
        {
            _logger.LogInformation("{Symbol} already signaled today, skipping", symbol);
            return;
        }

        // 2. Load historical data to validate signal (SQLite)
        var fromDate = DateTime.UtcNow.Date.AddDays(-50);
        var toDate = DateTime.UtcNow.Date;
        var historicalBars = await _historicalStore.LoadBarsAsync(symbol, "Day", fromDate, toDate);
        
        if (historicalBars.Count < 20)
        {
            _logger.LogWarning("Insufficient historical data for {Symbol}, need at least 20 bars", symbol);
            return;
        }

        // 3. Simulate signal validation using historical data
        var recentBars = historicalBars.TakeLast(20).ToList();
        var avgVolume = recentBars.Average(b => (double)b.Volume);
        var currentVolume = recentBars.Last().Volume;
        
        var isValidSignal = (double)currentVolume > avgVolume * 1.5; // Volume spike
        
        if (!isValidSignal)
        {
            _logger.LogInformation("Signal validation failed for {Symbol} - insufficient volume", symbol);
            return;
        }

        // 4. Flag the signal to prevent duplicates (Redis)
        await _liveStateStore.FlagSignalAsync(symbol, today);
        
        // 5. Set trailing stop (Redis)
        await _liveStateStore.SetTrailingStopAsync(symbol, stopPrice);
        
        // 6. Track position state (Redis)
        var positionState = new PositionState(
            symbol,
            currentPrice,
            currentPrice,
            stopPrice,
            100,
            DateTime.UtcNow,
            DateTime.UtcNow
        );
        await _liveStateStore.SetPositionStateAsync(symbol, positionState);

        _logger.LogInformation("Integrated workflow completed for {Symbol}: Signal flagged, stop set at {Stop:F2}, position tracked",
            symbol, stopPrice);

        // 7. Demonstrate error handling with retry queue
        try
        {
            // Simulate a failed operation
            throw new InvalidOperationException("Simulated order placement failure");
        }
        catch (Exception ex)
        {
            var retryItem = new RetryItem(
                Guid.NewGuid().ToString(),
                "PlaceOrder",
                $"{{\"symbol\":\"{symbol}\",\"price\":{currentPrice},\"quantity\":100}}",
                1,
                DateTime.UtcNow.AddMinutes(2),
                DateTime.UtcNow
            );

            await _liveStateStore.EnqueueRetryAsync(retryItem);
            _logger.LogWarning("Order placement failed for {Symbol}, added to retry queue: {Error}",
                symbol, ex.Message);
        }
    }

    /// <summary>
    /// Creates sample bar data for testing
    /// </summary>
    private List<IBar> CreateSampleBars(string symbol, DateTime startDate, int count)
    {
        var bars = new List<IBar>();
        var random = new Random();
        var basePrice = 150.0;

        for (int i = 0; i < count; i++)
        {
            var date = startDate.AddDays(i);
            var open = basePrice + random.NextDouble() * 10 - 5;
            var close = open + random.NextDouble() * 6 - 3;
            var high = Math.Max(open, close) + random.NextDouble() * 2;
            var low = Math.Min(open, close) - random.NextDouble() * 2;
            var volume = 1000000 + random.Next(500000);

            bars.Add(new SampleBar(symbol, date, (decimal)open, (decimal)high, (decimal)low, (decimal)close, volume));
        }

        return bars;
    }

    /// <summary>
    /// Simple IBar implementation for testing
    /// </summary>
    private record SampleBar(string Symbol, DateTime TimeUtc, decimal Open, decimal High, decimal Low, decimal Close, decimal Volume) : IBar
    {
        public decimal Vwap => (High + Low + Close) / 3;
        public ulong TradeCount => (ulong)(Volume / 100);
    }
}
