namespace SmaTrendFollower.Services.ErrorHandling;

/// <summary>
/// Service for managing error recovery and graceful degradation
/// </summary>
public interface IRecoveryService
{
    /// <summary>
    /// Execute an operation with automatic recovery mechanisms
    /// </summary>
    /// <typeparam name="T">Return type</typeparam>
    /// <param name="operation">Primary operation to execute</param>
    /// <param name="recoveryOptions">Recovery configuration</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Result of the operation or recovery</returns>
    Task<T> ExecuteWithRecoveryAsync<T>(
        Func<Task<T>> operation,
        RecoveryOptions recoveryOptions,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Register a fallback strategy for a specific operation type
    /// </summary>
    /// <typeparam name="T">Return type</typeparam>
    /// <param name="operationType">Type of operation</param>
    /// <param name="fallbackStrategy">Fallback function</param>
    void RegisterFallback<T>(string operationType, Func<Exception, Task<T>> fallbackStrategy);

    /// <summary>
    /// Register a degradation strategy for a service
    /// </summary>
    /// <param name="serviceName">Name of the service</param>
    /// <param name="degradationStrategy">Degradation function</param>
    void RegisterDegradationStrategy(string serviceName, Func<Exception, Task> degradationStrategy);

    /// <summary>
    /// Trigger graceful degradation for a service
    /// </summary>
    /// <param name="serviceName">Name of the service</param>
    /// <param name="reason">Reason for degradation</param>
    /// <param name="cancellationToken">Cancellation token</param>
    Task TriggerDegradationAsync(string serviceName, string reason, CancellationToken cancellationToken = default);

    /// <summary>
    /// Attempt to recover a degraded service
    /// </summary>
    /// <param name="serviceName">Name of the service</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>True if recovery was successful</returns>
    Task<bool> AttemptRecoveryAsync(string serviceName, CancellationToken cancellationToken = default);

    /// <summary>
    /// Get current degradation status
    /// </summary>
    /// <returns>Dictionary of service names and their degradation status</returns>
    Dictionary<string, DegradationStatus> GetDegradationStatus();

    /// <summary>
    /// Event fired when a service enters or exits degraded mode
    /// </summary>
    event EventHandler<DegradationEventArgs>? DegradationStatusChanged;
}

/// <summary>
/// Recovery options for operations
/// </summary>
public sealed class RecoveryOptions
{
    /// <summary>
    /// Operation type identifier for fallback lookup
    /// </summary>
    public string OperationType { get; set; } = string.Empty;

    /// <summary>
    /// Service name for degradation tracking
    /// </summary>
    public string? ServiceName { get; set; }

    /// <summary>
    /// Maximum number of recovery attempts
    /// </summary>
    public int MaxRecoveryAttempts { get; set; } = 3;

    /// <summary>
    /// Delay between recovery attempts
    /// </summary>
    public TimeSpan RecoveryDelay { get; set; } = TimeSpan.FromSeconds(5);

    /// <summary>
    /// Whether to enable fallback mechanisms
    /// </summary>
    public bool EnableFallback { get; set; } = true;

    /// <summary>
    /// Whether to enable graceful degradation
    /// </summary>
    public bool EnableDegradation { get; set; } = true;

    /// <summary>
    /// Custom recovery predicate
    /// </summary>
    public Func<Exception, bool>? ShouldAttemptRecovery { get; set; }

    /// <summary>
    /// Custom recovery action
    /// </summary>
    public Func<Exception, Task>? CustomRecoveryAction { get; set; }

    /// <summary>
    /// Timeout for recovery operations
    /// </summary>
    public TimeSpan RecoveryTimeout { get; set; } = TimeSpan.FromMinutes(1);

    /// <summary>
    /// Create default recovery options for API operations
    /// </summary>
    public static RecoveryOptions ForApi(string operationType, string? serviceName = null) => new()
    {
        OperationType = operationType,
        ServiceName = serviceName,
        MaxRecoveryAttempts = 3,
        RecoveryDelay = TimeSpan.FromSeconds(5),
        EnableFallback = true,
        EnableDegradation = true,
        RecoveryTimeout = TimeSpan.FromMinutes(1)
    };

    /// <summary>
    /// Create recovery options for critical operations
    /// </summary>
    public static RecoveryOptions ForCritical(string operationType, string? serviceName = null) => new()
    {
        OperationType = operationType,
        ServiceName = serviceName,
        MaxRecoveryAttempts = 5,
        RecoveryDelay = TimeSpan.FromSeconds(2),
        EnableFallback = true,
        EnableDegradation = false, // Don't degrade critical operations
        RecoveryTimeout = TimeSpan.FromMinutes(2)
    };

    /// <summary>
    /// Create recovery options for non-critical operations
    /// </summary>
    public static RecoveryOptions ForNonCritical(string operationType, string? serviceName = null) => new()
    {
        OperationType = operationType,
        ServiceName = serviceName,
        MaxRecoveryAttempts = 1,
        RecoveryDelay = TimeSpan.FromSeconds(10),
        EnableFallback = true,
        EnableDegradation = true,
        RecoveryTimeout = TimeSpan.FromSeconds(30)
    };
}

/// <summary>
/// Degradation status information
/// </summary>
public sealed class DegradationStatus
{
    public string ServiceName { get; init; } = string.Empty;
    public bool IsDegraded { get; init; }
    public string? Reason { get; init; }
    public DateTime DegradedSince { get; init; }
    public int RecoveryAttempts { get; init; }
    public DateTime? LastRecoveryAttempt { get; init; }
    public Exception? LastError { get; init; }
    public DegradationLevel Level { get; init; }
}

/// <summary>
/// Levels of service degradation
/// </summary>
public enum DegradationLevel
{
    /// <summary>
    /// Service is operating normally
    /// </summary>
    Normal,

    /// <summary>
    /// Service has minor degradation but is still functional
    /// </summary>
    Minor,

    /// <summary>
    /// Service has significant degradation with reduced functionality
    /// </summary>
    Major,

    /// <summary>
    /// Service is severely degraded or non-functional
    /// </summary>
    Severe
}

/// <summary>
/// Degradation event arguments
/// </summary>
public sealed class DegradationEventArgs : EventArgs
{
    public string ServiceName { get; init; } = string.Empty;
    public bool IsDegraded { get; init; }
    public DegradationLevel Level { get; init; }
    public string? Reason { get; init; }
    public DateTime Timestamp { get; init; } = DateTime.UtcNow;
    public Exception? Exception { get; init; }
}

/// <summary>
/// Recovery attempt result
/// </summary>
public sealed class RecoveryResult
{
    public bool IsSuccessful { get; init; }
    public string? Message { get; init; }
    public Exception? Exception { get; init; }
    public TimeSpan Duration { get; init; }
    public DateTime Timestamp { get; init; } = DateTime.UtcNow;

    public static RecoveryResult Success(string? message = null, TimeSpan? duration = null)
    {
        return new RecoveryResult
        {
            IsSuccessful = true,
            Message = message ?? "Recovery successful",
            Duration = duration ?? TimeSpan.Zero
        };
    }

    public static RecoveryResult Failure(string? message = null, Exception? exception = null, TimeSpan? duration = null)
    {
        return new RecoveryResult
        {
            IsSuccessful = false,
            Message = message ?? "Recovery failed",
            Exception = exception,
            Duration = duration ?? TimeSpan.Zero
        };
    }
}

/// <summary>
/// Service degradation context for tracking state
/// </summary>
internal sealed class ServiceDegradationContext
{
    public string ServiceName { get; set; } = string.Empty;
    public bool IsDegraded { get; set; }
    public DegradationLevel Level { get; set; } = DegradationLevel.Normal;
    public string? Reason { get; set; }
    public DateTime DegradedSince { get; set; }
    public int RecoveryAttempts { get; set; }
    public DateTime? LastRecoveryAttempt { get; set; }
    public Exception? LastError { get; set; }
    public Func<Exception, Task>? DegradationStrategy { get; set; }
    public object Lock { get; set; } = new();
}
