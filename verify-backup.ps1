# SmaTrendFollower Backup Verification Script
# Validates backup integrity and completeness

param(
    [Parameter(Mandatory=$true)]
    [string]$BackupPath,
    [switch]$Detailed,
    [switch]$TestRestore,
    [switch]$Verbose
)

# Color output functions
function Write-Info { param($Message) Write-Host $Message -ForegroundColor Cyan }
function Write-Success { param($Message) Write-Host $Message -ForegroundColor Green }
function Write-Warning { param($Message) Write-Host $Message -ForegroundColor Yellow }
function Write-Error { param($Message) Write-Host $Message -ForegroundColor Red }

$startTime = Get-Date
$verificationResults = @{
    Overall = $true
    Tests = @()
}

function Add-TestResult {
    param($TestName, $Passed, $Message, $Details = $null)
    
    $result = @{
        TestName = $TestName
        Passed = $Passed
        Message = $Message
        Details = $Details
        Timestamp = Get-Date
    }
    
    $verificationResults.Tests += $result
    if (-not $Passed) { $verificationResults.Overall = $false }
    
    if ($Passed) {
        Write-Success "✅ $TestName`: $Message"
    } else {
        Write-Error "❌ $TestName`: $Message"
    }
    
    if ($Details -and $Verbose) {
        Write-Info "   Details: $Details"
    }
}

Write-Success "🔍 Starting SmaTrendFollower Backup Verification"
Write-Info "Backup path: $BackupPath"

try {
    # 1. Basic Existence Check
    Write-Info "`n📁 Basic Existence Checks"
    
    if (Test-Path $BackupPath) {
        Add-TestResult "Backup Exists" $true "Backup path found"
    } else {
        Add-TestResult "Backup Exists" $false "Backup path not found"
        throw "Backup path does not exist: $BackupPath"
    }
    
    # Handle compressed backups
    $isCompressed = $BackupPath.EndsWith('.zip')
    $workingPath = $BackupPath
    $tempExtractPath = $null
    
    if ($isCompressed) {
        Write-Info "📦 Extracting compressed backup for verification..."
        $tempExtractPath = "$env:TEMP\SmaTrendFollower_Verify_$(Get-Date -Format 'yyyyMMdd_HHmmss')"
        
        try {
            Expand-Archive -Path $BackupPath -DestinationPath $tempExtractPath -Force
            $workingPath = $tempExtractPath
            Add-TestResult "Archive Extraction" $true "Successfully extracted compressed backup"
        } catch {
            Add-TestResult "Archive Extraction" $false "Failed to extract: $($_.Exception.Message)"
            throw
        }
    }

    # 2. Manifest Verification
    Write-Info "`n📋 Manifest Verification"
    
    $manifestPath = "$workingPath\backup_manifest.json"
    if (Test-Path $manifestPath) {
        try {
            $manifest = Get-Content $manifestPath | ConvertFrom-Json
            Add-TestResult "Manifest Exists" $true "Backup manifest found and parsed"
            
            # Verify manifest structure
            $requiredFields = @('BackupInfo', 'SystemInfo', 'Files', 'Verification')
            $missingFields = @()
            
            foreach ($field in $requiredFields) {
                if (-not $manifest.PSObject.Properties.Name.Contains($field)) {
                    $missingFields += $field
                }
            }
            
            if ($missingFields.Count -eq 0) {
                Add-TestResult "Manifest Structure" $true "All required fields present"
            } else {
                Add-TestResult "Manifest Structure" $false "Missing fields: $($missingFields -join ', ')"
            }
            
            # Display backup info
            if ($Verbose -and $manifest.BackupInfo) {
                Write-Info "   Backup Date: $($manifest.BackupInfo.BackupDate)"
                Write-Info "   Total Files: $($manifest.BackupInfo.TotalFiles)"
                Write-Info "   Total Size: $($manifest.BackupInfo.TotalSizeMB) MB"
                Write-Info "   Source Machine: $($manifest.SystemInfo.MachineName)"
            }
            
        } catch {
            Add-TestResult "Manifest Parse" $false "Failed to parse manifest: $($_.Exception.Message)"
        }
    } else {
        Add-TestResult "Manifest Exists" $false "Backup manifest not found"
    }

    # 3. File Structure Verification
    Write-Info "`n📂 File Structure Verification"
    
    $expectedDirs = @('config', 'database', 'state')
    foreach ($dir in $expectedDirs) {
        $dirPath = "$workingPath\$dir"
        if (Test-Path $dirPath) {
            $fileCount = (Get-ChildItem $dirPath -File).Count
            Add-TestResult "Directory $dir" $true "Found with $fileCount files"
        } else {
            Add-TestResult "Directory $dir" $false "Directory missing"
        }
    }

    # 4. Configuration File Verification
    Write-Info "`n⚙️ Configuration File Verification"
    
    $configFiles = @(
        @{Name = ".env.backup"; Path = "$workingPath\config\.env.backup"; Critical = $true},
        @{Name = "appsettings.backup.json"; Path = "$workingPath\config\appsettings.backup.json"; Critical = $false},
        @{Name = ".env.live.backup"; Path = "$workingPath\config\.env.live.backup"; Critical = $false}
    )
    
    foreach ($config in $configFiles) {
        if (Test-Path $config.Path) {
            $size = (Get-Item $config.Path).Length
            if ($size -gt 0) {
                Add-TestResult "Config $($config.Name)" $true "Found ($size bytes)"
                
                # Basic content validation for .env files
                if ($config.Name.Contains('.env') -and $Detailed) {
                    $content = Get-Content $config.Path -Raw
                    $hasApiKeys = $content -match 'APCA_API_KEY_ID|POLY_API_KEY'
                    if ($hasApiKeys) {
                        Add-TestResult "Config Content $($config.Name)" $true "Contains API configuration"
                    } else {
                        Add-TestResult "Config Content $($config.Name)" $false "Missing API configuration"
                    }
                }
            } else {
                Add-TestResult "Config $($config.Name)" $false "File is empty"
            }
        } else {
            $severity = if ($config.Critical) { $false } else { $true }
            $message = if ($config.Critical) { "Critical file missing" } else { "Optional file missing" }
            Add-TestResult "Config $($config.Name)" $severity $message
        }
    }

    # 5. Database File Verification
    Write-Info "`n🗄️ Database File Verification"
    
    $dbFiles = @(
        @{Name = "index_cache.backup.db"; Path = "$workingPath\database\index_cache.backup.db"},
        @{Name = "stock_cache.backup.db"; Path = "$workingPath\database\stock_cache.backup.db"}
    )
    
    foreach ($db in $dbFiles) {
        if (Test-Path $db.Path) {
            $size = (Get-Item $db.Path).Length
            Add-TestResult "Database $($db.Name)" $true "Found ($([math]::Round($size/1MB, 2)) MB)"
            
            # SQLite integrity check if detailed verification requested
            if ($Detailed) {
                try {
                    $integrityResult = sqlite3 $db.Path "PRAGMA integrity_check;" 2>$null
                    if ($integrityResult -eq "ok") {
                        Add-TestResult "DB Integrity $($db.Name)" $true "SQLite integrity check passed"
                    } else {
                        Add-TestResult "DB Integrity $($db.Name)" $false "SQLite integrity check failed"
                    }
                } catch {
                    Add-TestResult "DB Integrity $($db.Name)" $false "Could not perform integrity check"
                }
            }
        } else {
            Add-TestResult "Database $($db.Name)" $false "Database file missing"
        }
    }

    # 6. State File Verification
    Write-Info "`n📊 State File Verification"
    
    $stateFiles = @(
        @{Name = "redis.backup.rdb"; Path = "$workingPath\state\redis.backup.rdb"; Type = "Redis"},
        @{Name = "trading_state.backup.json"; Path = "$workingPath\state\trading_state.backup.json"; Type = "JSON"},
        @{Name = "state_backup.json"; Path = "$workingPath\state\state_backup.json"; Type = "JSON"}
    )
    
    foreach ($state in $stateFiles) {
        if (Test-Path $state.Path) {
            $size = (Get-Item $state.Path).Length
            Add-TestResult "State $($state.Name)" $true "Found ($([math]::Round($size/1KB, 2)) KB)"
            
            # JSON validation for JSON files
            if ($state.Type -eq "JSON" -and $Detailed) {
                try {
                    $content = Get-Content $state.Path -Raw | ConvertFrom-Json
                    Add-TestResult "State JSON $($state.Name)" $true "Valid JSON structure"
                } catch {
                    Add-TestResult "State JSON $($state.Name)" $false "Invalid JSON: $($_.Exception.Message)"
                }
            }
        } else {
            Add-TestResult "State $($state.Name)" $false "State file missing"
        }
    }

    # 7. File Count Verification
    Write-Info "`n📊 File Count Verification"
    
    $actualFiles = Get-ChildItem $workingPath -Recurse -File
    $actualCount = $actualFiles.Count
    
    if ($manifest -and $manifest.BackupInfo.TotalFiles) {
        $expectedCount = $manifest.BackupInfo.TotalFiles
        if ($actualCount -eq $expectedCount) {
            Add-TestResult "File Count" $true "Matches manifest ($actualCount files)"
        } else {
            Add-TestResult "File Count" $false "Mismatch - Expected: $expectedCount, Found: $actualCount"
        }
    } else {
        Add-TestResult "File Count" $true "Found $actualCount files (no manifest to compare)"
    }

    # 8. Size Verification
    Write-Info "`n💾 Size Verification"
    
    $actualSize = ($actualFiles | Measure-Object -Property Length -Sum).Sum
    $actualSizeMB = [math]::Round($actualSize / 1MB, 2)
    
    if ($manifest -and $manifest.BackupInfo.TotalSizeMB) {
        $expectedSizeMB = $manifest.BackupInfo.TotalSizeMB
        $sizeDifference = [math]::Abs($actualSizeMB - $expectedSizeMB)
        
        if ($sizeDifference -lt 1) { # Allow 1MB tolerance
            Add-TestResult "Size Verification" $true "Matches manifest ($actualSizeMB MB)"
        } else {
            Add-TestResult "Size Verification" $false "Size mismatch - Expected: $expectedSizeMB MB, Found: $actualSizeMB MB"
        }
    } else {
        Add-TestResult "Size Verification" $true "Total size: $actualSizeMB MB (no manifest to compare)"
    }

    # 9. Test Restore (if requested)
    if ($TestRestore) {
        Write-Info "`n🧪 Test Restore Verification"
        
        try {
            $testRestoreResult = & .\restore-system.ps1 -BackupPath $BackupPath -DryRun -Verbose:$false 2>&1
            if ($LASTEXITCODE -eq 0) {
                Add-TestResult "Test Restore" $true "Dry run restore completed successfully"
            } else {
                Add-TestResult "Test Restore" $false "Dry run restore failed"
                if ($Verbose) { Write-Warning $testRestoreResult }
            }
        } catch {
            Add-TestResult "Test Restore" $false "Test restore error: $($_.Exception.Message)"
        }
    }

    # 10. Generate Verification Report
    Write-Info "`n📋 Generating Verification Report"
    
    $report = @{
        VerificationInfo = @{
            VerificationDate = Get-Date -Format "yyyy-MM-dd HH:mm:ss"
            BackupPath = $BackupPath
            VerificationDuration = ((Get-Date) - $startTime).ToString()
            OverallResult = $verificationResults.Overall
            TotalTests = $verificationResults.Tests.Count
            PassedTests = ($verificationResults.Tests | Where-Object { $_.Passed }).Count
            FailedTests = ($verificationResults.Tests | Where-Object { -not $_.Passed }).Count
        }
        TestResults = $verificationResults.Tests
        Summary = @{
            ConfigurationFiles = ($verificationResults.Tests | Where-Object { $_.TestName -like "Config *" -and $_.Passed }).Count
            DatabaseFiles = ($verificationResults.Tests | Where-Object { $_.TestName -like "Database *" -and $_.Passed }).Count
            StateFiles = ($verificationResults.Tests | Where-Object { $_.TestName -like "State *" -and $_.Passed }).Count
            StructuralIntegrity = ($verificationResults.Tests | Where-Object { $_.TestName -in @("Manifest Exists", "File Count", "Size Verification") -and $_.Passed }).Count -eq 3
        }
    }
    
    $reportPath = "$workingPath\verification_report.json"
    $report | ConvertTo-Json -Depth 4 | Out-File $reportPath -Encoding UTF8
    Add-TestResult "Verification Report" $true "Report saved to verification_report.json"

} catch {
    Write-Error "❌ VERIFICATION FAILED: $($_.Exception.Message)"
    $verificationResults.Overall = $false
} finally {
    # Cleanup temporary extraction
    if ($tempExtractPath -and (Test-Path $tempExtractPath)) {
        Remove-Item $tempExtractPath -Recurse -Force -ErrorAction SilentlyContinue
    }
    
    # Final Summary
    $endTime = Get-Date
    $duration = $endTime - $startTime
    
    Write-Info "`n" + "="*60
    if ($verificationResults.Overall) {
        Write-Success "🎉 BACKUP VERIFICATION PASSED!"
    } else {
        Write-Error "❌ BACKUP VERIFICATION FAILED!"
    }
    
    Write-Info "📊 Verification Summary:"
    Write-Info "  Total Tests: $($verificationResults.Tests.Count)"
    Write-Info "  Passed: $(($verificationResults.Tests | Where-Object { $_.Passed }).Count)"
    Write-Info "  Failed: $(($verificationResults.Tests | Where-Object { -not $_.Passed }).Count)"
    Write-Info "  Duration: $($duration.ToString('mm\:ss'))"
    
    if (-not $verificationResults.Overall) {
        Write-Info "`n❌ Failed Tests:"
        $verificationResults.Tests | Where-Object { -not $_.Passed } | ForEach-Object {
            Write-Error "  - $($_.TestName): $($_.Message)"
        }
    }
    
    exit $(if ($verificationResults.Overall) { 0 } else { 1 })
}
