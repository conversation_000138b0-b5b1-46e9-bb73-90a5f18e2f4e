#!/usr/bin/env pwsh

# Polygon API Connectivity Test Script
# Tests various Polygon endpoints to identify connectivity issues

param(
    [string]$ApiKey = $env:POLY_API_KEY
)

if (-not $ApiKey) {
    Write-Host "❌ No Polygon API key found. Set POLY_API_KEY environment variable." -ForegroundColor Red
    exit 1
}

Write-Host "🔍 Testing Polygon API Connectivity..." -ForegroundColor Cyan
Write-Host "API Key: $($ApiKey.Substring(0, 8))..." -ForegroundColor Gray

# Test endpoints with different timeout and retry scenarios
$endpoints = @(
    @{
        Name = "Market Status"
        Url = "https://api.polygon.io/v1/marketstatus/now?apikey=$ApiKey"
        Timeout = 5
    },
    @{
        Name = "SPY Stock Bars (Last 5 days)"
        Url = "https://api.polygon.io/v2/aggs/ticker/SPY/range/1/day/2024-12-01/2024-12-20?adjusted=true`&sort=asc`&apikey=$ApiKey"
        Timeout = 10
    },
    @{
        Name = "SPX Index Bars (Last 5 days)"
        Url = "https://api.polygon.io/v2/aggs/ticker/I:SPX/range/1/day/2024-12-01/2024-12-20?adjusted=true`&sort=asc`&apikey=$ApiKey"
        Timeout = 10
    },
    @{
        Name = "VIX Index Current Value"
        Url = "https://api.polygon.io/v2/aggs/ticker/I:VIX/range/1/day/2024-12-20/2024-12-20?adjusted=true`&sort=asc`&apikey=$ApiKey"
        Timeout = 10
    },
    @{
        Name = "Minute Bars (High frequency)"
        Url = "https://api.polygon.io/v2/aggs/ticker/SPY/range/1/minute/2024-12-20/2024-12-20?adjusted=true`&sort=asc`&limit=100`&apikey=$ApiKey"
        Timeout = 15
    },
    @{
        Name = "Options Contracts"
        Url = "https://api.polygon.io/v3/reference/options/contracts?underlying_ticker=SPY`&limit=10`&apikey=$ApiKey"
        Timeout = 10
    },
    @{
        Name = "Tickers Reference"
        Url = "https://api.polygon.io/v3/reference/tickers?market=stocks`&active=true`&limit=10`&apikey=$ApiKey"
        Timeout = 10
    }
)

$results = @()
$totalTests = $endpoints.Count
$passedTests = 0

foreach ($endpoint in $endpoints) {
    Write-Host "`n📡 Testing: $($endpoint.Name)" -ForegroundColor Yellow
    
    try {
        $stopwatch = [System.Diagnostics.Stopwatch]::StartNew()
        
        # Create web request with timeout
        $request = [System.Net.WebRequest]::Create($endpoint.Url)
        $request.Timeout = $endpoint.Timeout * 1000  # Convert to milliseconds
        $request.Method = "GET"
        $request.UserAgent = "SmaTrendFollower/1.0"
        
        # Execute request
        $response = $request.GetResponse()
        $stopwatch.Stop()
        
        $statusCode = [int]$response.StatusCode
        $responseTime = $stopwatch.ElapsedMilliseconds
        
        # Read response content
        $stream = $response.GetResponseStream()
        $reader = New-Object System.IO.StreamReader($stream)
        $content = $reader.ReadToEnd()
        $reader.Close()
        $response.Close()
        
        # Parse JSON to check data quality
        $jsonData = $content | ConvertFrom-Json
        $dataPoints = 0
        
        if ($jsonData.results) {
            $dataPoints = $jsonData.results.Count
        } elseif ($jsonData.status) {
            $dataPoints = 1  # Status endpoint
        }
        
        Write-Host "  ✅ Success - Status: $statusCode, Time: ${responseTime}ms, Data Points: $dataPoints" -ForegroundColor Green
        
        $results += @{
            Endpoint = $endpoint.Name
            Status = "Success"
            StatusCode = $statusCode
            ResponseTime = $responseTime
            DataPoints = $dataPoints
            Error = $null
        }
        
        $passedTests++
        
    } catch [System.Net.WebException] {
        $stopwatch.Stop()
        $webEx = $_.Exception
        $statusCode = if ($webEx.Response) { [int]$webEx.Response.StatusCode } else { "N/A" }
        $responseTime = $stopwatch.ElapsedMilliseconds
        
        Write-Host "  ❌ Web Error - Status: $statusCode, Time: ${responseTime}ms" -ForegroundColor Red
        Write-Host "    Error: $($webEx.Message)" -ForegroundColor Red
        
        $results += @{
            Endpoint = $endpoint.Name
            Status = "WebError"
            StatusCode = $statusCode
            ResponseTime = $responseTime
            DataPoints = 0
            Error = $webEx.Message
        }
        
    } catch [System.TimeoutException] {
        $stopwatch.Stop()
        $responseTime = $stopwatch.ElapsedMilliseconds
        
        Write-Host "  ⏰ Timeout - Time: ${responseTime}ms (exceeded $($endpoint.Timeout)s)" -ForegroundColor Red
        
        $results += @{
            Endpoint = $endpoint.Name
            Status = "Timeout"
            StatusCode = "Timeout"
            ResponseTime = $responseTime
            DataPoints = 0
            Error = "Request timed out after $($endpoint.Timeout) seconds"
        }
        
    } catch {
        $stopwatch.Stop()
        $responseTime = $stopwatch.ElapsedMilliseconds
        
        Write-Host "  💥 General Error - Time: ${responseTime}ms" -ForegroundColor Red
        Write-Host "    Error: $($_.Exception.Message)" -ForegroundColor Red
        
        $results += @{
            Endpoint = $endpoint.Name
            Status = "Error"
            StatusCode = "Error"
            ResponseTime = $responseTime
            DataPoints = 0
            Error = $_.Exception.Message
        }
    }
    
    # Rate limiting delay
    Start-Sleep -Milliseconds 250
}

# Summary Report
Write-Host "`n" + "="*60 -ForegroundColor Cyan
Write-Host "📊 POLYGON CONNECTIVITY TEST SUMMARY" -ForegroundColor Cyan
Write-Host "="*60 -ForegroundColor Cyan

Write-Host "`n🎯 Overall Results:" -ForegroundColor White
$percentage = [math]::Round($passedTests/$totalTests*100, 1)
$color = if ($passedTests -eq $totalTests) { "Green" } else { "Yellow" }
Write-Host "  Passed: $passedTests/$totalTests ($percentage percent)" -ForegroundColor $color

Write-Host "`n📋 Detailed Results:" -ForegroundColor White
foreach ($result in $results) {
    $statusColor = switch ($result.Status) {
        "Success" { "Green" }
        "WebError" { "Red" }
        "Timeout" { "Yellow" }
        "Error" { "Red" }
    }
    
    Write-Host "  $($result.Endpoint):" -ForegroundColor White
    Write-Host "    Status: $($result.Status) ($($result.StatusCode))" -ForegroundColor $statusColor
    Write-Host "    Response Time: $($result.ResponseTime)ms" -ForegroundColor Gray
    Write-Host "    Data Points: $($result.DataPoints)" -ForegroundColor Gray
    
    if ($result.Error) {
        Write-Host "    Error: $($result.Error)" -ForegroundColor Red
    }
}

# Recommendations
Write-Host "`n💡 Recommendations:" -ForegroundColor White

$failedTests = $results | Where-Object { $_.Status -ne "Success" }
if ($failedTests.Count -eq 0) {
    Write-Host "  ✅ All endpoints are working correctly!" -ForegroundColor Green
} else {
    Write-Host "  ⚠️  $($failedTests.Count) endpoint(s) have issues:" -ForegroundColor Yellow
    
    $timeoutIssues = $failedTests | Where-Object { $_.Status -eq "Timeout" }
    if ($timeoutIssues.Count -gt 0) {
        Write-Host "    - Increase timeout values for slow endpoints" -ForegroundColor Yellow
        Write-Host "    - Consider implementing connection pooling" -ForegroundColor Yellow
    }
    
    $webErrors = $failedTests | Where-Object { $_.Status -eq "WebError" }
    if ($webErrors.Count -gt 0) {
        Write-Host "    - Implement exponential backoff retry logic" -ForegroundColor Yellow
        Write-Host "    - Add circuit breaker pattern for failing endpoints" -ForegroundColor Yellow
    }
    
    Write-Host "    - Enable Alpaca fallback mechanisms" -ForegroundColor Yellow
    Write-Host "    - Add health check monitoring" -ForegroundColor Yellow
}

Write-Host "`n🔧 Next Steps:" -ForegroundColor White
Write-Host "  1. Review failed endpoints and implement fixes" -ForegroundColor Gray
Write-Host "  2. Add monitoring and alerting for connectivity issues" -ForegroundColor Gray
Write-Host "  3. Test fallback mechanisms under load" -ForegroundColor Gray
Write-Host "  4. Consider implementing connection pooling" -ForegroundColor Gray

$exitCode = if ($passedTests -eq $totalTests) { 0 } else { 1 }
exit $exitCode
