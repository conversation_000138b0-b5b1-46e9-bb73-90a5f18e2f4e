namespace SmaTrendFollower.Services;

/// <summary>
/// Interface for monitoring API health and connectivity status
/// </summary>
public interface IApiHealthMonitor
{
    /// <summary>
    /// Gets the current health status of Alpaca API
    /// </summary>
    Task<ApiHealthStatus> GetAlpacaHealthAsync(CancellationToken cancellationToken = default);

    /// <summary>
    /// Gets the current health status of Polygon API
    /// </summary>
    Task<ApiHealthStatus> GetPolygonHealthAsync(CancellationToken cancellationToken = default);

    /// <summary>
    /// Performs a comprehensive health check of all APIs
    /// </summary>
    Task<OverallHealthStatus> GetOverallHealthAsync(CancellationToken cancellationToken = default);

    /// <summary>
    /// Event raised when API health status changes
    /// </summary>
    event EventHandler<ApiHealthChangedEventArgs>? HealthStatusChanged;
}

/// <summary>
/// Health status for a specific API
/// </summary>
public class ApiHealthStatus
{
    public string ApiName { get; set; } = string.Empty;
    public bool IsHealthy { get; set; }
    public TimeSpan ResponseTime { get; set; }
    public string? ErrorMessage { get; set; }
    public DateTime LastChecked { get; set; }
    public int ConsecutiveFailures { get; set; }
    public Dictionary<string, object> AdditionalMetrics { get; set; } = new();
}

/// <summary>
/// Overall health status across all APIs
/// </summary>
public class OverallHealthStatus
{
    public bool IsHealthy { get; set; }
    public List<ApiHealthStatus> ApiStatuses { get; set; } = new();
    public DateTime LastChecked { get; set; }
    public string Summary { get; set; } = string.Empty;
}

/// <summary>
/// Event arguments for health status changes
/// </summary>
public class ApiHealthChangedEventArgs : EventArgs
{
    public string ApiName { get; set; } = string.Empty;
    public bool WasHealthy { get; set; }
    public bool IsHealthy { get; set; }
    public string? ErrorMessage { get; set; }
    public DateTime Timestamp { get; set; }
}
