namespace SmaTrendFollower.Services;

/// <summary>
/// Service for tracking and reporting cache performance metrics
/// </summary>
public interface ICacheMetricsService
{
    /// <summary>
    /// Records a cache hit for performance tracking
    /// </summary>
    /// <param name="symbol">Symbol that was cached</param>
    /// <param name="timeFrame">Timeframe of the data</param>
    /// <param name="responseTimeMs">Response time in milliseconds</param>
    void RecordCacheHit(string symbol, string timeFrame, double responseTimeMs);

    /// <summary>
    /// Records a cache miss for performance tracking
    /// </summary>
    /// <param name="symbol">Symbol that was not cached</param>
    /// <param name="timeFrame">Timeframe of the data</param>
    /// <param name="responseTimeMs">Response time in milliseconds</param>
    /// <param name="apiCallTimeMs">Time spent calling external API</param>
    void RecordCacheMiss(string symbol, string timeFrame, double responseTimeMs, double apiCallTimeMs);

    /// <summary>
    /// Records API throttling event
    /// </summary>
    /// <param name="provider">API provider (Alpaca, Polygon)</param>
    /// <param name="symbol">Symbol being requested</param>
    void RecordApiThrottle(string provider, string symbol);

    /// <summary>
    /// Records cache operation timing
    /// </summary>
    /// <param name="operation">Operation type (read, write, compress, etc.)</param>
    /// <param name="durationMs">Duration in milliseconds</param>
    /// <param name="recordCount">Number of records processed</param>
    void RecordCacheOperation(string operation, double durationMs, int recordCount = 1);

    /// <summary>
    /// Gets current cache performance metrics
    /// </summary>
    /// <returns>Current performance metrics</returns>
    CachePerformanceMetrics GetCurrentMetrics();

    /// <summary>
    /// Gets cache performance metrics for a specific time period
    /// </summary>
    /// <param name="startTime">Start of time period</param>
    /// <param name="endTime">End of time period</param>
    /// <returns>Performance metrics for the period</returns>
    CachePerformanceMetrics GetMetricsForPeriod(DateTime startTime, DateTime endTime);

    /// <summary>
    /// Resets all metrics (useful for testing or periodic resets)
    /// </summary>
    void ResetMetrics();

    /// <summary>
    /// Gets detailed metrics by symbol
    /// </summary>
    /// <returns>Dictionary of symbol metrics</returns>
    IDictionary<string, SymbolMetrics> GetSymbolMetrics();

    /// <summary>
    /// Exports metrics to JSON for external monitoring systems
    /// </summary>
    /// <returns>JSON representation of metrics</returns>
    string ExportMetricsAsJson();
}

/// <summary>
/// Overall cache performance metrics
/// </summary>
public readonly record struct CachePerformanceMetrics(
    long TotalRequests,
    long CacheHits,
    long CacheMisses,
    double CacheHitRatio,
    double AverageCacheResponseTimeMs,
    double AverageApiResponseTimeMs,
    long ApiThrottleEvents,
    long TotalApiCalls,
    double ApiCallSavingsRatio,
    DateTime MetricsPeriodStart,
    DateTime MetricsPeriodEnd,
    IDictionary<string, double> OperationTimings
);

/// <summary>
/// Performance metrics for a specific symbol
/// </summary>
public readonly record struct SymbolMetrics(
    string Symbol,
    long TotalRequests,
    long CacheHits,
    long CacheMisses,
    double CacheHitRatio,
    double AverageResponseTimeMs,
    DateTime LastAccessed,
    long TotalBarsServed
);

/// <summary>
/// Real-time cache operation event
/// </summary>
public readonly record struct CacheOperationEvent(
    DateTime Timestamp,
    string EventType, // "hit", "miss", "write", "compress", "throttle"
    string Symbol,
    string TimeFrame,
    double DurationMs,
    int RecordCount,
    IDictionary<string, object> AdditionalData
);

/// <summary>
/// Configuration for cache metrics collection
/// </summary>
public record class CacheMetricsConfig(
    bool EnableDetailedMetrics,
    TimeSpan MetricsRetentionPeriod,
    int MaxSymbolMetrics,
    bool EnableRealTimeEvents,
    TimeSpan MetricsAggregationInterval
)
{
    public static CacheMetricsConfig Default => new(
        EnableDetailedMetrics: true,
        MetricsRetentionPeriod: TimeSpan.FromDays(7),
        MaxSymbolMetrics: 1000,
        EnableRealTimeEvents: true,
        MetricsAggregationInterval: TimeSpan.FromMinutes(5)
    );
}
