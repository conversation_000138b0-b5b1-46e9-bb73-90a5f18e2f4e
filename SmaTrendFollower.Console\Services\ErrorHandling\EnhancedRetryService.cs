using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using System.Diagnostics;
using System.Net;

namespace SmaTrendFollower.Services.ErrorHandling;

/// <summary>
/// Enhanced retry service implementation with circuit breaker integration
/// </summary>
public sealed class EnhancedRetryService : IEnhancedRetryService
{
    private readonly ILogger<EnhancedRetryService> _logger;
    private readonly ICircuitBreakerService _circuitBreakerService;
    private readonly IErrorHandler _errorHandler;
    private readonly RetryPolicy _defaultPolicy;
    private readonly RetryStatistics _statistics = new();
    private readonly Random _random = new();
    private readonly object _statsLock = new();

    public event EventHandler<RetryEventArgs>? RetryAttempted;

    public EnhancedRetryService(
        ILogger<EnhancedRetryService> logger,
        ICircuitBreakerService circuitBreakerService,
        IErrorHandler errorHandler,
        IOptions<RetryPolicy>? defaultPolicy = null)
    {
        _logger = logger;
        _circuitBreakerService = circuitBreakerService;
        _errorHandler = errorHandler;
        _defaultPolicy = defaultPolicy?.Value ?? RetryPolicy.ForApi();
    }

    public async Task<T> ExecuteAsync<T>(
        Func<Task<T>> operation,
        string operationName,
        string? serviceName = null,
        CancellationToken cancellationToken = default)
    {
        return await ExecuteWithPolicyAsync(operation, _defaultPolicy, operationName, serviceName, cancellationToken);
    }

    public async Task ExecuteAsync(
        Func<Task> operation,
        string operationName,
        string? serviceName = null,
        CancellationToken cancellationToken = default)
    {
        await ExecuteAsync(async () =>
        {
            await operation();
            return true; // Dummy return value
        }, operationName, serviceName, cancellationToken);
    }

    public async Task<T> ExecuteWithPolicyAsync<T>(
        Func<Task<T>> operation,
        RetryPolicy retryPolicy,
        string operationName,
        CancellationToken cancellationToken = default)
    {
        return await ExecuteWithPolicyAsync(operation, retryPolicy, operationName, null, cancellationToken);
    }

    public async Task<HttpResponseMessage> ExecuteHttpAsync(
        Func<Task<HttpResponseMessage>> httpOperation,
        string operationName,
        string? serviceName = null,
        CancellationToken cancellationToken = default)
    {
        var httpPolicy = CreateHttpRetryPolicy();
        return await ExecuteWithPolicyAsync(httpOperation, httpPolicy, operationName, serviceName, cancellationToken);
    }

    public RetryStatistics GetStatistics()
    {
        lock (_statsLock)
        {
            return new RetryStatistics
            {
                TotalOperations = _statistics.TotalOperations,
                SuccessfulOperations = _statistics.SuccessfulOperations,
                FailedOperations = _statistics.FailedOperations,
                TotalRetries = _statistics.TotalRetries,
                TotalRetryTime = _statistics.TotalRetryTime,
                AverageOperationTime = _statistics.AverageOperationTime,
                OperationCounts = new Dictionary<string, int>(_statistics.OperationCounts),
                ErrorCounts = new Dictionary<string, int>(_statistics.ErrorCounts),
                LastResetTime = _statistics.LastResetTime
            };
        }
    }

    public void ResetStatistics()
    {
        lock (_statsLock)
        {
            _statistics.TotalOperations = 0;
            _statistics.SuccessfulOperations = 0;
            _statistics.FailedOperations = 0;
            _statistics.TotalRetries = 0;
            _statistics.TotalRetryTime = TimeSpan.Zero;
            _statistics.AverageOperationTime = TimeSpan.Zero;
            _statistics.OperationCounts.Clear();
            _statistics.ErrorCounts.Clear();
            _statistics.LastResetTime = DateTime.UtcNow;
        }
    }

    private async Task<T> ExecuteWithPolicyAsync<T>(
        Func<Task<T>> operation,
        RetryPolicy retryPolicy,
        string operationName,
        string? serviceName,
        CancellationToken cancellationToken)
    {
        var totalStopwatch = Stopwatch.StartNew();
        var attemptCount = 0;
        var totalRetryTime = TimeSpan.Zero;
        Exception? lastException = null;

        RecordOperationStart(operationName);

        try
        {
            while (attemptCount < retryPolicy.MaxAttempts)
            {
                attemptCount++;
                var attemptStopwatch = Stopwatch.StartNew();

                try
                {
                    // Use circuit breaker if enabled and service name provided
                    if (retryPolicy.EnableCircuitBreaker && !string.IsNullOrEmpty(serviceName))
                    {
                        return await _circuitBreakerService.ExecuteAsync(serviceName, operation, cancellationToken: cancellationToken);
                    }

                    // Apply operation timeout if specified
                    if (retryPolicy.OperationTimeout.HasValue)
                    {
                        using var timeoutCts = CancellationTokenSource.CreateLinkedTokenSource(cancellationToken);
                        timeoutCts.CancelAfter(retryPolicy.OperationTimeout.Value);

                        var operationTask = operation();
                        var timeoutTask = Task.Delay(retryPolicy.OperationTimeout.Value, timeoutCts.Token);

                        var completedTask = await Task.WhenAny(operationTask, timeoutTask);

                        if (completedTask == timeoutTask)
                        {
                            throw new TaskCanceledException($"Operation timed out after {retryPolicy.OperationTimeout.Value.TotalMilliseconds}ms");
                        }

                        return await operationTask;
                    }

                    return await operation();
                }
                catch (Exception ex) when (attemptCount < retryPolicy.MaxAttempts)
                {
                    attemptStopwatch.Stop();
                    lastException = ex;

                    // Check if we should retry this exception
                    if (!ShouldRetryException(ex, retryPolicy))
                    {
                        _logger.LogWarning("Exception is not retriable for operation {OperationName}: {Exception}",
                            operationName, ex.Message);
                        break;
                    }

                    // Calculate delay
                    var delay = CalculateDelay(attemptCount, retryPolicy);
                    totalRetryTime = totalRetryTime.Add(delay);

                    // Log retry attempt
                    _logger.LogWarning("Retry attempt {AttemptCount}/{MaxAttempts} for operation {OperationName} after {Delay}ms. Exception: {Exception}",
                        attemptCount, retryPolicy.MaxAttempts, operationName, delay.TotalMilliseconds, ex.Message);

                    // Fire retry event
                    var retryInfo = new RetryAttemptInfo
                    {
                        AttemptNumber = attemptCount,
                        Delay = delay,
                        Exception = ex,
                        OperationName = operationName,
                        ServiceName = serviceName
                    };

                    OnRetryAttempted(new RetryEventArgs
                    {
                        AttemptInfo = retryInfo,
                        IsLastAttempt = attemptCount == retryPolicy.MaxAttempts,
                        WillRetry = attemptCount < retryPolicy.MaxAttempts
                    });

                    // Record retry statistics
                    RecordRetryAttempt(operationName, ex, delay);

                    // Wait before retry
                    if (delay > TimeSpan.Zero)
                    {
                        await Task.Delay(delay, cancellationToken);
                    }
                }
                catch (Exception ex)
                {
                    // Final attempt failed
                    attemptStopwatch.Stop();
                    lastException = ex;
                    break;
                }
            }

            // All retries exhausted
            totalStopwatch.Stop();
            RecordOperationFailure(operationName, lastException!, totalStopwatch.Elapsed);

            _logger.LogError(lastException, "Operation {OperationName} failed after {AttemptCount} attempts in {TotalTime}ms",
                operationName, attemptCount, totalStopwatch.ElapsedMilliseconds);

            throw lastException!;
        }
        catch (Exception ex) when (attemptCount == 1)
        {
            // First attempt failed and not retriable
            totalStopwatch.Stop();
            RecordOperationFailure(operationName, ex, totalStopwatch.Elapsed);
            throw;
        }
        finally
        {
            if (totalStopwatch.IsRunning)
            {
                totalStopwatch.Stop();
                RecordOperationSuccess(operationName, totalStopwatch.Elapsed);
            }
        }
    }

    private bool ShouldRetryException(Exception exception, RetryPolicy retryPolicy)
    {
        // Use custom predicate if provided
        if (retryPolicy.ShouldRetryPredicate != null)
        {
            return retryPolicy.ShouldRetryPredicate(exception);
        }

        // Use error handler to determine if retriable
        return _errorHandler.ShouldRetry(exception, 1);
    }

    private TimeSpan CalculateDelay(int attemptCount, RetryPolicy retryPolicy)
    {
        // Use custom delay calculator if provided
        if (retryPolicy.DelayCalculator != null)
        {
            return retryPolicy.DelayCalculator(attemptCount, retryPolicy.BaseDelay);
        }

        var delay = retryPolicy.BackoffStrategy switch
        {
            BackoffStrategy.Fixed => retryPolicy.BaseDelay,
            BackoffStrategy.Linear => TimeSpan.FromMilliseconds(retryPolicy.BaseDelay.TotalMilliseconds * attemptCount),
            BackoffStrategy.Exponential => TimeSpan.FromMilliseconds(retryPolicy.BaseDelay.TotalMilliseconds * Math.Pow(2, attemptCount - 1)),
            _ => retryPolicy.BaseDelay
        };

        // Apply maximum delay constraint
        if (delay > retryPolicy.MaxDelay)
        {
            delay = retryPolicy.MaxDelay;
        }

        // Add jitter if enabled
        if (retryPolicy.UseJitter)
        {
            var jitter = TimeSpan.FromMilliseconds(_random.NextDouble() * retryPolicy.MaxJitter.TotalMilliseconds);
            delay = delay.Add(jitter);
        }

        return delay;
    }

    private static RetryPolicy CreateHttpRetryPolicy()
    {
        return new RetryPolicy
        {
            MaxAttempts = 3,
            BaseDelay = TimeSpan.FromSeconds(1),
            MaxDelay = TimeSpan.FromSeconds(30),
            BackoffStrategy = BackoffStrategy.Exponential,
            UseJitter = true,
            EnableCircuitBreaker = true,
            ShouldRetryPredicate = IsRetriableHttpException
        };
    }

    private static bool IsRetriableHttpException(Exception exception)
    {
        return exception switch
        {
            HttpRequestException => true,
            TaskCanceledException => true,
            ExternalApiException apiEx => apiEx.IsRetriable,
            _ when exception.Message.Contains("timeout", StringComparison.OrdinalIgnoreCase) => true,
            _ when exception.Message.Contains("network", StringComparison.OrdinalIgnoreCase) => true,
            _ => false
        };
    }

    private void RecordOperationStart(string operationName)
    {
        lock (_statsLock)
        {
            _statistics.TotalOperations++;
            _statistics.OperationCounts[operationName] = _statistics.OperationCounts.GetValueOrDefault(operationName, 0) + 1;
        }
    }

    private void RecordOperationSuccess(string operationName, TimeSpan duration)
    {
        lock (_statsLock)
        {
            _statistics.SuccessfulOperations++;
            UpdateAverageOperationTime(duration);
        }
    }

    private void RecordOperationFailure(string operationName, Exception exception, TimeSpan duration)
    {
        lock (_statsLock)
        {
            _statistics.FailedOperations++;
            _statistics.ErrorCounts[exception.GetType().Name] = _statistics.ErrorCounts.GetValueOrDefault(exception.GetType().Name, 0) + 1;
            UpdateAverageOperationTime(duration);
        }
    }

    private void RecordRetryAttempt(string operationName, Exception exception, TimeSpan delay)
    {
        lock (_statsLock)
        {
            _statistics.TotalRetries++;
            _statistics.TotalRetryTime = _statistics.TotalRetryTime.Add(delay);
        }
    }

    private void UpdateAverageOperationTime(TimeSpan duration)
    {
        var totalOperations = _statistics.SuccessfulOperations + _statistics.FailedOperations;
        if (totalOperations > 0)
        {
            var totalTime = _statistics.AverageOperationTime.TotalMilliseconds * (totalOperations - 1) + duration.TotalMilliseconds;
            _statistics.AverageOperationTime = TimeSpan.FromMilliseconds(totalTime / totalOperations);
        }
    }

    private void OnRetryAttempted(RetryEventArgs args)
    {
        RetryAttempted?.Invoke(this, args);
    }
}
