# SmaTrendFollower Dual Storage System

## Overview

The SmaTrendFollower now implements a sophisticated dual storage architecture that separates **real-time state** (Redis) from **long-term historical storage** (SQLite). This design optimizes performance, reliability, and scalability for high-frequency trading operations.

## Architecture

```
┌─────────────────┐    ┌─────────────────┐
│   Redis Store   │    │  SQLite Store   │
│   (Live State)  │    │  (Historical)   │
├─────────────────┤    ├─────────────────┤
│ • Trailing Stops│    │ • Bar Data      │
│ • Signal Flags  │    │ • Cache Metadata│
│ • Position State│    │ • Audit Logs    │
│ • Retry Queue   │    │ • Backtest Data │
│ • Market Cache  │    │ • Compressed    │
└─────────────────┘    └─────────────────┘
         │                       │
         └───────────┬───────────┘
                     │
            ┌─────────────────┐
            │ State Flush     │
            │ Service         │
            │ (Background)    │
            └─────────────────┘
```

## Storage Tiers

| Store | Purpose | Data Types | Retention | Performance |
|-------|---------|------------|-----------|-------------|
| **Redis** | Real-time trading state | Stops, signals, positions, retries | Session-based | Ultra-fast |
| **SQLite** | Historical persistence | Bars, metadata, logs | 1+ years | Fast queries |

## Key Features

### ✅ Redis Live State Store
- **Trailing Stops**: Real-time stop-loss tracking
- **Signal Deduplication**: Prevent duplicate signals per day
- **Position State**: Track entry, current, stop prices
- **Retry Queue**: Automatic error recovery
- **Market State Cache**: Temporary market data with TTL
- **Health Monitoring**: Connection status and statistics

### ✅ SQLite Historical Store
- **Bar Storage**: OHLCV data with compression
- **Metadata Tracking**: Cache freshness and statistics
- **Bulk Operations**: Optimized batch inserts
- **Cleanup Automation**: Configurable data retention
- **Query Optimization**: Indexed for fast retrieval

### ✅ Background Services
- **State Flush Service**: Periodic Redis → SQLite backup
- **Automatic Recovery**: Restore state on startup
- **Graceful Shutdown**: Preserve state on exit

## Configuration

Add to your `.env` file:

```bash
# Redis Configuration
REDIS_URL=localhost:6379
REDIS_PASSWORD=
REDIS_DATABASE=0

# Storage Configuration
STORAGE_DEFAULT_TIER=Cold
STORAGE_HOT_RETENTION_DAYS=1
STORAGE_COLD_RETENTION_DAYS=365
STORAGE_ENABLE_COMPRESSION=true
STORAGE_COMPRESSION_THRESHOLD_DAYS=30

# State Management
STATE_FLUSH_INTERVAL_MINUTES=15
STATE_BACKUP_PATH=Data/state_backup.json
```

## Usage Examples

### Basic Operations

```csharp
// Inject services
public MyTradingService(ILiveStateStore liveStore, IBarStore historicalStore)
{
    _liveStore = liveStore;
    _historicalStore = historicalStore;
}

// Set trailing stop (Redis)
await _liveStore.SetTrailingStopAsync("AAPL", 150.25m);

// Check if already signaled today (Redis)
var wasSignaled = await _liveStore.WasSignaledAsync("AAPL", DateTime.UtcNow.Date);

// Load historical bars (SQLite)
var bars = await _historicalStore.LoadBarsAsync("AAPL", "Day", fromDate, toDate);

// Save new bars (SQLite)
await _historicalStore.SaveBarsAsync("AAPL", "Day", newBars);
```

### Error Handling with Retry Queue

```csharp
try
{
    await PlaceOrderAsync(symbol, quantity, price);
}
catch (Exception ex)
{
    var retryItem = new RetryItem(
        Guid.NewGuid().ToString(),
        "PlaceOrder",
        JsonSerializer.Serialize(new { symbol, quantity, price }),
        1,
        DateTime.UtcNow.AddMinutes(5),
        DateTime.UtcNow
    );
    
    await _liveStore.EnqueueRetryAsync(retryItem);
}
```

## Command Line Interface

### Dual Storage Management

```bash
# Show system status
dotnet run -- --dual-storage status

# View trailing stops
dotnet run -- --dual-storage stops

# Check retry queue
dotnet run -- --dual-storage retry

# Run health check
dotnet run -- --dual-storage health

# Cleanup old data (retain 180 days)
dotnet run -- --dual-storage cleanup 180

# Export state to JSON
dotnet run -- --dual-storage export backup.json

# Flush Redis (CAUTION!)
dotnet run -- --dual-storage flush --confirm
```

### Run Example

```bash
# Demonstrate dual storage features
dotnet run -- --dual-storage-example
```

## Performance Benefits

### Before (Single SQLite)
- ❌ Blocking I/O for real-time operations
- ❌ Lock contention during high-frequency updates
- ❌ No automatic retry mechanism
- ❌ State lost on crashes

### After (Dual Storage)
- ✅ Sub-millisecond Redis operations
- ✅ Parallel read/write operations
- ✅ Automatic error recovery
- ✅ State persistence across restarts
- ✅ Optimized bulk operations
- ✅ Configurable data retention

## Monitoring

### Health Checks
```csharp
var isHealthy = await _liveStore.IsHealthyAsync();
var stats = await _liveStore.GetStatsAsync();
```

### Statistics
- Active trailing stops count
- Signal flags count
- Position states count
- Retry queue length
- Market state cache size
- Storage size and bar counts

## Best Practices

### 🔥 Hot Data (Redis)
- Use for data that changes frequently
- Set appropriate TTL for temporary data
- Monitor memory usage
- Implement connection retry logic

### 🧊 Cold Data (SQLite)
- Use for historical analysis
- Enable compression for old data
- Regular cleanup of expired data
- Optimize queries with proper indexes

### 🔄 Data Flow
1. **Real-time updates** → Redis
2. **Periodic snapshots** → SQLite (via StateFlushService)
3. **Historical queries** → SQLite
4. **Recovery** → SQLite → Redis (on startup)

## Troubleshooting

### Redis Connection Issues
```bash
# Check Redis status
redis-cli ping

# Monitor Redis memory
redis-cli info memory

# View Redis keys
redis-cli keys "*"
```

### SQLite Issues
```bash
# Check database integrity
sqlite3 stock_cache.db "PRAGMA integrity_check;"

# View table sizes
sqlite3 stock_cache.db ".tables"
```

### Performance Tuning
- Adjust `STATE_FLUSH_INTERVAL_MINUTES` based on risk tolerance
- Monitor Redis memory usage and set `maxmemory` policy
- Use SQLite WAL mode for better concurrency
- Enable compression for older data

## Migration Guide

If upgrading from single SQLite storage:

1. **Backup existing data**
2. **Update configuration** (add Redis settings)
3. **Install Redis** locally or use cloud service
4. **Test with paper trading** first
5. **Monitor performance** and adjust settings

The system is backward compatible and will work without Redis (degraded performance).

## Next Steps

This dual storage foundation enables:
- Real-time streaming data processing
- Advanced caching strategies  
- Distributed trading systems
- Enhanced monitoring and alerting
- Improved disaster recovery
