namespace SmaTrendFollower.Services;

/// <summary>
/// Service for pre-populating cache with frequently used symbols during off-market hours
/// </summary>
public interface ICacheWarmingService
{
    /// <summary>
    /// Warms cache for essential symbols (SPY, QQQ, etc.) with historical data
    /// </summary>
    /// <param name="daysOfHistory">Number of days of history to cache (default: 365)</param>
    /// <returns>Number of symbols warmed and total bars cached</returns>
    Task<(int symbolsWarmed, int totalBarsCached)> WarmEssentialSymbolsAsync(int daysOfHistory = 365);

    /// <summary>
    /// Warms cache for universe symbols (top 500 stocks)
    /// </summary>
    /// <param name="symbols">List of symbols to warm</param>
    /// <param name="daysOfHistory">Number of days of history to cache (default: 250)</param>
    /// <returns>Number of symbols warmed and total bars cached</returns>
    Task<(int symbolsWarmed, int totalBarsCached)> WarmUniverseSymbolsAsync(IEnumerable<string> symbols, int daysOfHistory = 250);

    /// <summary>
    /// Performs incremental cache warming (only missing data)
    /// </summary>
    /// <param name="symbols">Symbols to update</param>
    /// <returns>Number of symbols updated and new bars cached</returns>
    Task<(int symbolsUpdated, int newBarsCached)> IncrementalWarmAsync(IEnumerable<string> symbols);

    /// <summary>
    /// Checks if cache warming should run based on market hours and last run time
    /// </summary>
    /// <returns>True if warming should run</returns>
    Task<bool> ShouldRunWarmingAsync();

    /// <summary>
    /// Gets cache warming statistics
    /// </summary>
    /// <returns>Warming statistics</returns>
    Task<CacheWarmingStats> GetWarmingStatsAsync();

    /// <summary>
    /// Schedules cache warming to run during off-market hours
    /// </summary>
    /// <param name="cancellationToken">Cancellation token</param>
    Task RunScheduledWarmingAsync(CancellationToken cancellationToken = default);
}

/// <summary>
/// Statistics about cache warming operations
/// </summary>
public readonly record struct CacheWarmingStats(
    DateTime? LastWarmingRun,
    int TotalSymbolsWarmed,
    int TotalBarsWarmed,
    TimeSpan LastWarmingDuration,
    int FailedSymbols,
    string[] MostRecentlyWarmedSymbols
);

/// <summary>
/// Configuration for cache warming operations
/// </summary>
public record class CacheWarmingConfig(
    string[] EssentialSymbols,
    int EssentialSymbolsDays,
    int UniverseSymbolsDays,
    TimeSpan WarmingInterval,
    TimeOnly PreferredStartTime,
    TimeOnly PreferredEndTime,
    int MaxConcurrentSymbols,
    int BatchSize
)
{
    public static CacheWarmingConfig Default => new(
        EssentialSymbols: new[] { "SPY", "QQQ", "IWM", "VIX", "TLT", "GLD", "AAPL", "MSFT", "GOOGL", "AMZN" },
        EssentialSymbolsDays: 365,
        UniverseSymbolsDays: 250,
        WarmingInterval: TimeSpan.FromHours(6),
        PreferredStartTime: new TimeOnly(18, 0), // 6 PM ET
        PreferredEndTime: new TimeOnly(6, 0),    // 6 AM ET
        MaxConcurrentSymbols: 5,
        BatchSize: 50
    );
}
