# SmaTrendFollower Performance and Monitoring Guide

## Performance Overview

SmaTrendFollower is designed for high-performance algorithmic trading with sub-second signal generation and execution capabilities. This guide covers performance optimization strategies, monitoring setup, and troubleshooting procedures.

## Performance Architecture

### 1. Execution Pipeline Performance
```
Cache Warming (Pre-market)     → 30-60 seconds
Signal Generation              → 5-15 seconds
Risk Calculation              → 1-3 seconds
Trade Execution               → 2-5 seconds
Stop Loss Management          → 1-2 seconds
Total Cycle Time              → 40-85 seconds
```

### 2. Data Processing Performance
```
SQLite Cache Query            → 10-50ms per symbol
Redis Cache Access            → 1-5ms per operation
Alpaca API Call              → 100-500ms per request
Polygon API Call             → 50-200ms per request
Technical Indicator Calc     → 5-20ms per symbol
```

## Caching Strategy

### 1. Multi-Layer Caching Architecture
```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Memory Cache  │ -> │   Redis Cache   │ -> │  SQLite Cache   │
│   (In-Process)  │    │  (Distributed)  │    │   (Persistent)  │
│   < 1ms         │    │   1-5ms         │    │   10-50ms       │
└─────────────────┘    └─────────────────┘    └─────────────────┘
                                                        │
                                                        v
                                               ┌─────────────────┐
                                               │   External APIs │
                                               │ (Alpaca/Polygon)│
                                               │   100-500ms     │
                                               └─────────────────┘
```

### 2. Cache Performance Metrics
```csharp
public class CacheMetrics
{
    public long TotalRequests { get; set; }
    public long CacheHits { get; set; }
    public long CacheMisses { get; set; }
    public double HitRatio => TotalRequests > 0 ? (double)CacheHits / TotalRequests : 0;
    public TimeSpan AverageResponseTime { get; set; }
    public long TotalDataSize { get; set; }
    public DateTime LastUpdated { get; set; }
}
```

### 3. Cache Optimization Strategies

#### SQLite Optimization
```sql
-- Index optimization for bar queries
CREATE INDEX idx_bars_symbol_timeframe_date ON stock_bars(symbol, timeframe, date);
CREATE INDEX idx_bars_date_symbol ON stock_bars(date, symbol);

-- Pragma settings for performance
PRAGMA journal_mode = WAL;
PRAGMA synchronous = NORMAL;
PRAGMA cache_size = 10000;
PRAGMA temp_store = MEMORY;
```

#### Redis Optimization
```csharp
// Connection pooling and optimization
services.AddStackExchangeRedisCache(options =>
{
    options.Configuration = connectionString;
    options.ConfigurationOptions = new ConfigurationOptions
    {
        AbortOnConnectFail = false,
        ConnectRetry = 3,
        ConnectTimeout = 5000,
        SyncTimeout = 1000,
        AsyncTimeout = 5000,
        KeepAlive = 60,
        DefaultDatabase = 0
    };
});
```

## API Performance Optimization

### 1. Rate Limiting and Throttling
```csharp
public class ApiRateLimiter
{
    private readonly SemaphoreSlim _alpacaSemaphore = new(200, 200); // 200 req/min
    private readonly SemaphoreSlim _polygonSemaphore = new(5, 5);    // 5 req/sec
    
    public async Task<T> ExecuteAlpacaRequestAsync<T>(Func<Task<T>> request)
    {
        await _alpacaSemaphore.WaitAsync();
        try
        {
            return await request();
        }
        finally
        {
            // Release after 60 seconds for Alpaca (200/min limit)
            _ = Task.Delay(TimeSpan.FromSeconds(60/200.0))
                .ContinueWith(_ => _alpacaSemaphore.Release());
        }
    }
}
```

### 2. Batch Processing
```csharp
// Batch multiple symbol requests
public async Task<IDictionary<string, IPage<IBar>>> GetStockBarsAsync(
    IEnumerable<string> symbols, DateTime startDate, DateTime endDate)
{
    var symbolBatches = symbols.Chunk(20); // Process 20 symbols per batch
    var results = new ConcurrentDictionary<string, IPage<IBar>>();
    
    await Parallel.ForEachAsync(symbolBatches, 
        new ParallelOptions { MaxDegreeOfParallelism = 4 },
        async (batch, ct) =>
        {
            var batchResults = await _alpacaClient.GetHistoricalBarsAsync(
                new HistoricalBarsRequest(batch, startDate, endDate));
            
            foreach (var kvp in batchResults)
            {
                results[kvp.Key] = kvp.Value;
            }
        });
    
    return results;
}
```

### 3. Connection Management
```csharp
// HTTP client factory with optimized settings
services.AddHttpClient<PolygonClient>(client =>
{
    client.BaseAddress = new Uri("https://api.polygon.io/");
    client.Timeout = TimeSpan.FromSeconds(30);
    client.DefaultRequestHeaders.Add("User-Agent", "SmaTrendFollower/1.0");
})
.ConfigurePrimaryHttpMessageHandler(() => new HttpClientHandler
{
    MaxConnectionsPerServer = 10,
    PooledConnectionLifetime = TimeSpan.FromMinutes(15)
})
.AddPolicyHandler(GetRetryPolicy());
```

## Memory Management

### 1. Memory Optimization Patterns
```csharp
// Use object pooling for frequently created objects
public class BarCalculationPool
{
    private readonly ObjectPool<List<decimal>> _listPool;
    
    public BarCalculationPool(ObjectPoolProvider poolProvider)
    {
        _listPool = poolProvider.Create(new ListPooledObjectPolicy<decimal>());
    }
    
    public decimal CalculateSma(IEnumerable<IBar> bars, int period)
    {
        var prices = _listPool.Get();
        try
        {
            prices.AddRange(bars.TakeLast(period).Select(b => b.Close));
            return prices.Average();
        }
        finally
        {
            _listPool.Return(prices);
        }
    }
}
```

### 2. Streaming Data Processing
```csharp
// Process large datasets in chunks to avoid memory spikes
public async IAsyncEnumerable<TradingSignal> ProcessUniverseAsync(
    IEnumerable<string> symbols,
    [EnumeratorCancellation] CancellationToken cancellationToken = default)
{
    await foreach (var symbolBatch in symbols.Chunk(50).ToAsyncEnumerable())
    {
        var batchTasks = symbolBatch.Select(async symbol =>
        {
            var bars = await GetBarsAsync(symbol);
            return await GenerateSignalAsync(symbol, bars);
        });
        
        var signals = await Task.WhenAll(batchTasks);
        
        foreach (var signal in signals.Where(s => s != null))
        {
            yield return signal;
        }
        
        // Allow garbage collection between batches
        if (cancellationToken.IsCancellationRequested)
            yield break;
    }
}
```

## Monitoring and Observability

### 1. Performance Metrics Collection
```csharp
public class PerformanceMetrics
{
    private readonly IMetricsCollector _metrics;
    
    public async Task<T> MeasureAsync<T>(string operationName, Func<Task<T>> operation)
    {
        using var activity = Activity.StartActivity(operationName);
        var stopwatch = Stopwatch.StartNew();
        
        try
        {
            var result = await operation();
            
            _metrics.RecordValue($"{operationName}.duration", stopwatch.ElapsedMilliseconds);
            _metrics.IncrementCounter($"{operationName}.success");
            
            return result;
        }
        catch (Exception ex)
        {
            _metrics.IncrementCounter($"{operationName}.error");
            _metrics.RecordValue($"{operationName}.error.{ex.GetType().Name}", 1);
            throw;
        }
        finally
        {
            stopwatch.Stop();
        }
    }
}
```

### 2. Health Check Implementation
```csharp
public class TradingSystemHealthCheck : IHealthCheck
{
    private readonly IMarketDataService _marketData;
    private readonly IAlpacaClientFactory _alpacaFactory;
    
    public async Task<HealthCheckResult> CheckHealthAsync(
        HealthCheckContext context, 
        CancellationToken cancellationToken = default)
    {
        var checks = new Dictionary<string, bool>();
        
        try
        {
            // Check Alpaca connectivity
            var account = await _marketData.GetAccountAsync();
            checks["alpaca_api"] = account != null;
            
            // Check database connectivity
            var testBars = await _marketData.GetStockBarsAsync("SPY", 
                DateTime.UtcNow.AddDays(-1), DateTime.UtcNow);
            checks["database"] = testBars?.Items?.Any() == true;
            
            // Check Redis connectivity (if configured)
            // ... Redis health check
            
            var allHealthy = checks.Values.All(v => v);
            var status = allHealthy ? HealthStatus.Healthy : HealthStatus.Degraded;
            
            return new HealthCheckResult(status, 
                $"System health: {string.Join(", ", checks.Select(kvp => $"{kvp.Key}:{kvp.Value}"))}");
        }
        catch (Exception ex)
        {
            return new HealthCheckResult(HealthStatus.Unhealthy, 
                "Health check failed", ex);
        }
    }
}
```

### 3. Structured Logging
```csharp
public class TradingService
{
    private readonly ILogger<TradingService> _logger;
    
    public async Task ExecuteCycleAsync(CancellationToken cancellationToken = default)
    {
        using var scope = _logger.BeginScope(new Dictionary<string, object>
        {
            ["TradingCycleId"] = Guid.NewGuid(),
            ["ExecutionTime"] = DateTime.UtcNow
        });
        
        _logger.LogInformation("Starting trading cycle");
        
        var stopwatch = Stopwatch.StartNew();
        
        try
        {
            var signals = await _signalGenerator.RunAsync(10, cancellationToken);
            
            _logger.LogInformation("Generated {SignalCount} trading signals in {ElapsedMs}ms", 
                signals.Count(), stopwatch.ElapsedMilliseconds);
            
            foreach (var signal in signals)
            {
                var quantity = await _riskManager.CalculateQuantityAsync(signal, cancellationToken);
                await _tradeExecutor.ExecuteTradeAsync(signal, quantity, cancellationToken);
                
                _logger.LogInformation("Executed trade: {Symbol} {Quantity} shares at {Price:C}", 
                    signal.Symbol, quantity, signal.Price);
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Trading cycle failed after {ElapsedMs}ms", 
                stopwatch.ElapsedMilliseconds);
            throw;
        }
        finally
        {
            stopwatch.Stop();
            _logger.LogInformation("Trading cycle completed in {ElapsedMs}ms", 
                stopwatch.ElapsedMilliseconds);
        }
    }
}
```

## Performance Monitoring Dashboard

### 1. Key Performance Indicators (KPIs)
```
Trading Performance:
- Signal Generation Time: < 15 seconds
- Trade Execution Time: < 5 seconds
- Cache Hit Ratio: > 90%
- API Success Rate: > 99%
- Memory Usage: < 500MB
- CPU Usage: < 50%

System Health:
- Uptime: > 99.9%
- Error Rate: < 0.1%
- Response Time P95: < 2 seconds
- Database Connection Pool: < 80% utilization
```

### 2. Alerting Thresholds
```yaml
alerts:
  high_latency:
    condition: signal_generation_time > 30s
    severity: warning
    
  api_failures:
    condition: api_error_rate > 5%
    severity: critical
    
  memory_usage:
    condition: memory_usage > 1GB
    severity: warning
    
  cache_performance:
    condition: cache_hit_ratio < 80%
    severity: warning
```

## Troubleshooting Guide

### 1. Common Performance Issues

#### Slow Signal Generation
```bash
# Check database performance
sqlite3 stock_cache.db "EXPLAIN QUERY PLAN SELECT * FROM stock_bars WHERE symbol = 'AAPL';"

# Monitor API call patterns
grep "API call" logs/trading-*.log | tail -100

# Check memory usage
dotnet-counters monitor --process-id <pid> --counters System.Runtime
```

#### High Memory Usage
```csharp
// Memory profiling in code
private void LogMemoryUsage(string operation)
{
    var memoryBefore = GC.GetTotalMemory(false);
    // ... perform operation
    var memoryAfter = GC.GetTotalMemory(false);
    
    _logger.LogInformation("{Operation} memory delta: {MemoryDelta:N0} bytes", 
        operation, memoryAfter - memoryBefore);
}
```

#### API Rate Limiting
```csharp
// Monitor rate limit headers
public async Task<T> ExecuteWithRateLimitMonitoring<T>(Func<Task<T>> apiCall)
{
    var response = await apiCall();
    
    if (response is HttpResponseMessage httpResponse)
    {
        var remaining = httpResponse.Headers.GetValues("X-RateLimit-Remaining").FirstOrDefault();
        var resetTime = httpResponse.Headers.GetValues("X-RateLimit-Reset").FirstOrDefault();
        
        _logger.LogDebug("Rate limit remaining: {Remaining}, Reset: {Reset}", 
            remaining, resetTime);
    }
    
    return response;
}
```

### 2. Performance Optimization Checklist

#### Daily Optimization Tasks
- [ ] Review cache hit ratios
- [ ] Monitor API response times
- [ ] Check memory usage patterns
- [ ] Validate database query performance
- [ ] Review error logs for patterns

#### Weekly Optimization Tasks
- [ ] Analyze trading cycle performance trends
- [ ] Review and optimize database indexes
- [ ] Update cache warming strategies
- [ ] Performance test with increased load
- [ ] Review and tune garbage collection

#### Monthly Optimization Tasks
- [ ] Comprehensive performance review
- [ ] Database maintenance and optimization
- [ ] Cache strategy evaluation
- [ ] Infrastructure scaling assessment
- [ ] Performance benchmark comparison

This performance and monitoring guide ensures SmaTrendFollower operates at peak efficiency with comprehensive observability and proactive optimization strategies.
