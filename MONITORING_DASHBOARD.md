# SmaTrendFollower Monitoring Dashboard

## Overview
The SmaTrendFollower monitoring dashboard provides comprehensive real-time monitoring for live trading operations with multiple interfaces and monitoring capabilities.

## 🌐 Web Dashboard

### Starting the Web Dashboard
```bash
# Start the metrics API service
dotnet run --project SmaTrendFollower.Console -- metrics-api

# Dashboard will be available at:
# http://localhost:8080/dashboard
```

### Features
- **Real-time Metrics**: Live trading statistics, P&L, positions
- **System Health**: API connectivity, service status, performance
- **Market Data**: Live market snapshots, alerts, volatility indicators
- **Interactive Controls**: Emergency stop, refresh, detailed metrics
- **Auto-refresh**: 30-second automatic updates
- **Responsive Design**: Works on desktop and mobile devices

### API Endpoints
- `/dashboard` - Main web dashboard interface
- `/health` - System health status (JSON)
- `/metrics` - Trading metrics and statistics (JSON)
- `/live/market` - Live market data and alerts (JSON)
- `/live/signals` - Live trading signals (JSON)
- `/system` - System information (JSON)
- `/metrics/prometheus` - Prometheus format metrics
- `/api/emergency-stop` - Emergency trading halt (POST)

## 💻 Console Dashboard

### Starting the Console Dashboard
```powershell
# Start PowerShell dashboard
.\monitoring-dashboard.ps1 -Continuous

# Or use the startup script
.\start-monitoring.ps1 -ConsoleDashboard
```

### Features
- **Terminal Interface**: PowerShell-based real-time monitoring
- **System Health**: Service status, connectivity checks
- **Trading Metrics**: Performance statistics, P&L tracking
- **Account Status**: Equity, positions, buying power
- **Live Market Data**: Price movements, alerts
- **Interactive Controls**: Refresh, emergency stop, status checks

## 🚀 Unified Monitoring Startup

### Using the Startup Script
```powershell
# Interactive mode - choose dashboard type
.\start-monitoring.ps1

# Start web dashboard only
.\start-monitoring.ps1 -WebDashboard -OpenBrowser

# Start console dashboard only
.\start-monitoring.ps1 -ConsoleDashboard

# Start both dashboards
.\start-monitoring.ps1 -Both

# Custom port for web dashboard
.\start-monitoring.ps1 -WebDashboard -Port 9090
```

### Startup Script Features
- **Automatic Service Management**: Starts and monitors dashboard processes
- **Health Checks**: Validates services are responding
- **Process Monitoring**: Tracks running services, automatic restart
- **Interactive Controls**: Keyboard shortcuts for management
- **Error Handling**: Graceful failure recovery and logging

## 📊 Monitoring Capabilities

### Real-Time Metrics
- **Account Status**: Equity, buying power, day trading power
- **Daily P&L**: Real-time profit/loss tracking with alerts
- **Active Positions**: Current holdings, market value, unrealized P&L
- **Win Rate**: Success percentage, trade statistics
- **Signal Generation**: Daily signals, execution rate
- **Risk Assessment**: Current risk level, safety limits

### System Health Monitoring
- **API Connectivity**: Alpaca, Polygon API status
- **Service Health**: Background services, database connections
- **Performance Metrics**: CPU, memory, response times
- **Error Tracking**: Recent errors, warning alerts
- **Cache Status**: Redis connectivity, cache hit rates

### Market Data Monitoring
- **Market Indices**: SPY, QQQ, IWM, VIX real-time prices
- **Price Alerts**: Significant movement notifications
- **Volatility Tracking**: Market regime detection
- **Volume Analysis**: Trading volume patterns

### Trading Intelligence
- **Live Signals**: Real-time signal generation and confidence
- **Signal States**: Tracking signal lifecycle and execution
- **Market Trends**: Trend analysis and regime detection
- **Risk Events**: Volatility spikes, market stress indicators

## 🎮 Interactive Controls

### Web Dashboard Controls
- **Refresh Button**: Manual data refresh
- **Emergency Stop**: Immediate trading halt
- **Detailed Metrics**: Expanded statistics view
- **Keyboard Shortcuts**:
  - `R` - Refresh data
  - `E` - Emergency stop
  - `Space` - Toggle auto-refresh

### Console Dashboard Controls
- **Q** - Quit dashboard
- **R** - Refresh now
- **E** - Emergency stop
- **S** - Show service status

### Startup Script Controls
- **Q** - Quit all monitoring services
- **S** - Show status of all services
- **R** - Restart all services
- **B** - Open browser (web dashboard)

## 🚨 Emergency Procedures

### Emergency Stop
```bash
# Via web dashboard
POST http://localhost:8080/api/emergency-stop

# Via console
dotnet run -- emergency-stop

# Via PowerShell dashboard
Press 'E' key in any dashboard
```

### Emergency Actions
1. **Immediate**: Cancel all pending orders
2. **Position Exit**: Market sell all positions
3. **System Halt**: Stop new signal generation
4. **Safety Mode**: Enable dry run mode
5. **Notification**: Send Discord alerts

## 📈 Performance Monitoring

### Key Performance Indicators (KPIs)
- **Sharpe Ratio**: Risk-adjusted returns
- **Maximum Drawdown**: Largest peak-to-trough decline
- **Win Rate**: Percentage of profitable trades
- **Average Win/Loss**: Trade size analysis
- **Signal Execution Rate**: Signal-to-trade conversion
- **System Uptime**: Service availability

### Performance Alerts
- **Daily Loss Threshold**: Alert at -$100 loss
- **Drawdown Alert**: Warning at 5% drawdown
- **API Errors**: Connectivity issue notifications
- **System Performance**: High CPU/memory alerts

## 🔧 Configuration

### Environment Variables
```bash
# Metrics API Configuration
METRICS_API_PORT=8080
METRICS_API_ENABLE_CORS=true

# Dashboard Refresh Intervals
DASHBOARD_REFRESH_INTERVAL=30
CONSOLE_REFRESH_INTERVAL=30

# Alert Thresholds
ALERT_DAILY_LOSS_THRESHOLD=100
ALERT_DRAWDOWN_THRESHOLD=0.05
```

### Customization
- **Refresh Intervals**: Adjust update frequency
- **Alert Thresholds**: Customize warning levels
- **Display Options**: Show/hide specific metrics
- **Color Themes**: Customize dashboard appearance

## 🔍 Troubleshooting

### Common Issues
1. **Port Already in Use**: Change METRICS_API_PORT
2. **Redis Connection**: Check Redis server status
3. **API Errors**: Verify Alpaca/Polygon credentials
4. **Dashboard Not Loading**: Check firewall settings

### Diagnostic Commands
```bash
# Check service health
dotnet run -- health

# Test API connectivity
dotnet run -- --test-connectivity

# Validate configuration
dotnet run -- validate

# Check system status
dotnet run -- system
```

## 📋 Monitoring Checklist

### Daily Monitoring Tasks
- [ ] Check system health status
- [ ] Review daily P&L and performance
- [ ] Verify API connectivity
- [ ] Monitor position count and risk
- [ ] Check for system alerts/errors

### Weekly Monitoring Tasks
- [ ] Review performance metrics trends
- [ ] Analyze signal generation effectiveness
- [ ] Check system resource usage
- [ ] Validate safety configuration
- [ ] Review error logs and patterns

---

**Dashboard Status**: ✅ **FULLY OPERATIONAL**
**Last Updated**: 2025-06-21
**Version**: 1.0.0
