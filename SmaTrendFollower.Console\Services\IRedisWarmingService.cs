namespace SmaTrendFollower.Services;

/// <summary>
/// Interface for Redis cache warming service that prepares live cache state before trading begins.
/// </summary>
public interface IRedisWarmingService
{
    /// <summary>
    /// Performs complete cache warming operation, loading trailing stops, signal flags, and throttle keys.
    /// </summary>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Task representing the warming operation</returns>
    Task WarmCacheAsync(CancellationToken cancellationToken = default);

    /// <summary>
    /// Saves current Redis state to SQLite for persistence across restarts.
    /// </summary>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Task representing the persistence operation</returns>
    Task PersistRedisStateAsync(CancellationToken cancellationToken = default);

    /// <summary>
    /// Clears all cached data from Redis.
    /// </summary>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Task representing the clear operation</returns>
    Task ClearCacheAsync(CancellationToken cancellationToken = default);
}
