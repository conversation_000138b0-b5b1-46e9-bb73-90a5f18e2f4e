using Microsoft.Extensions.Logging;
using StackExchange.Redis;
using System.Text.Json;

namespace SmaTrendFollower.Services;

/// <summary>
/// Service for tracking VIX cache performance metrics and monitoring
/// </summary>
public interface IVixCacheMetricsService
{
    /// <summary>
    /// Records a cache hit for VIX data
    /// </summary>
    Task RecordCacheHitAsync(string dataSource, TimeSpan age, decimal qualityScore);

    /// <summary>
    /// Records a cache miss for VIX data
    /// </summary>
    Task RecordCacheMissAsync(string reason);

    /// <summary>
    /// Records VIX data retrieval performance
    /// </summary>
    Task RecordRetrievalPerformanceAsync(string dataSource, TimeSpan duration, bool success, int apiCallsUsed);

    /// <summary>
    /// Gets current cache performance metrics
    /// </summary>
    Task<VixCacheMetrics> GetMetricsAsync();

    /// <summary>
    /// Resets all metrics counters
    /// </summary>
    Task ResetMetricsAsync();
}

public class VixCacheMetricsService : IVixCacheMetricsService
{
    private readonly ILogger<VixCacheMetricsService> _logger;
    private readonly IDatabase? _redis;
    
    // In-memory counters for performance
    private long _cacheHits = 0;
    private long _cacheMisses = 0;
    private long _totalRetrievals = 0;
    private long _totalApiCalls = 0;
    private readonly object _metricsLock = new();

    public VixCacheMetricsService(
        ILogger<VixCacheMetricsService> logger,
        ConnectionMultiplexer? connectionMultiplexer = null)
    {
        _logger = logger;
        _redis = connectionMultiplexer?.GetDatabase();
    }

    public async Task RecordCacheHitAsync(string dataSource, TimeSpan age, decimal qualityScore)
    {
        lock (_metricsLock)
        {
            _cacheHits++;
        }

        try
        {
            var hitMetric = new VixCacheHitMetric
            {
                DataSource = dataSource,
                Age = age,
                QualityScore = qualityScore,
                Timestamp = DateTime.UtcNow
            };

            _logger.LogDebug("VIX cache hit recorded: {DataSource}, age: {Age}, quality: {Quality:F2}", 
                dataSource, age, qualityScore);

            // Store detailed metrics in Redis if available
            if (_redis != null)
            {
                var key = $"vix:metrics:hit:{DateTime.UtcNow:yyyyMMddHHmm}";
                await _redis.StringSetAsync(key, hitMetric.ToJson(), TimeSpan.FromHours(24));
            }
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "Error recording VIX cache hit metric");
        }
    }

    public async Task RecordCacheMissAsync(string reason)
    {
        lock (_metricsLock)
        {
            _cacheMisses++;
        }

        try
        {
            var missMetric = new VixCacheMissMetric
            {
                Reason = reason,
                Timestamp = DateTime.UtcNow
            };

            _logger.LogDebug("VIX cache miss recorded: {Reason}", reason);

            // Store detailed metrics in Redis if available
            if (_redis != null)
            {
                var key = $"vix:metrics:miss:{DateTime.UtcNow:yyyyMMddHHmm}";
                await _redis.StringSetAsync(key, missMetric.ToJson(), TimeSpan.FromHours(24));
            }
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "Error recording VIX cache miss metric");
        }
    }

    public async Task RecordRetrievalPerformanceAsync(string dataSource, TimeSpan duration, bool success, int apiCallsUsed)
    {
        lock (_metricsLock)
        {
            _totalRetrievals++;
            _totalApiCalls += apiCallsUsed;
        }

        try
        {
            var performanceMetric = new VixRetrievalPerformanceMetric
            {
                DataSource = dataSource,
                Duration = duration,
                Success = success,
                ApiCallsUsed = apiCallsUsed,
                Timestamp = DateTime.UtcNow
            };

            _logger.LogDebug("VIX retrieval performance recorded: {DataSource}, duration: {Duration}ms, success: {Success}, API calls: {ApiCalls}", 
                dataSource, duration.TotalMilliseconds, success, apiCallsUsed);

            // Store detailed metrics in Redis if available
            if (_redis != null)
            {
                var key = $"vix:metrics:perf:{DateTime.UtcNow:yyyyMMddHHmm}";
                await _redis.StringSetAsync(key, performanceMetric.ToJson(), TimeSpan.FromHours(24));
            }
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "Error recording VIX retrieval performance metric");
        }
    }

    public async Task<VixCacheMetrics> GetMetricsAsync()
    {
        try
        {
            long cacheHits, cacheMisses, totalRetrievals, totalApiCalls;
            
            lock (_metricsLock)
            {
                cacheHits = _cacheHits;
                cacheMisses = _cacheMisses;
                totalRetrievals = _totalRetrievals;
                totalApiCalls = _totalApiCalls;
            }

            var totalCacheRequests = cacheHits + cacheMisses;
            var cacheHitRate = totalCacheRequests > 0 ? (decimal)cacheHits / totalCacheRequests : 0m;
            var avgApiCallsPerRetrieval = totalRetrievals > 0 ? (decimal)totalApiCalls / totalRetrievals : 0m;

            var metrics = new VixCacheMetrics
            {
                CacheHits = cacheHits,
                CacheMisses = cacheMisses,
                TotalCacheRequests = totalCacheRequests,
                CacheHitRate = cacheHitRate,
                TotalRetrievals = totalRetrievals,
                TotalApiCalls = totalApiCalls,
                AverageApiCallsPerRetrieval = avgApiCallsPerRetrieval,
                LastUpdated = DateTime.UtcNow
            };

            // Get additional metrics from Redis if available
            if (_redis != null)
            {
                await EnrichMetricsFromRedisAsync(metrics);
            }

            return metrics;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting VIX cache metrics");
            return new VixCacheMetrics { LastUpdated = DateTime.UtcNow };
        }
    }

    public async Task ResetMetricsAsync()
    {
        try
        {
            lock (_metricsLock)
            {
                _cacheHits = 0;
                _cacheMisses = 0;
                _totalRetrievals = 0;
                _totalApiCalls = 0;
            }

            _logger.LogInformation("VIX cache metrics reset");

            // Clear Redis metrics if available
            if (_redis != null)
            {
                var pattern = "vix:metrics:*";
                // Note: In production, you might want to use a more targeted cleanup
                _logger.LogDebug("Redis VIX metrics cleanup would be performed here");
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error resetting VIX cache metrics");
        }
    }

    private async Task EnrichMetricsFromRedisAsync(VixCacheMetrics metrics)
    {
        try
        {
            // This could be enhanced to pull detailed metrics from Redis
            // For now, we'll just log that enrichment is available
            _logger.LogDebug("VIX cache metrics enrichment from Redis available");
        }
        catch (Exception ex)
        {
            _logger.LogDebug(ex, "Error enriching VIX metrics from Redis");
        }
    }
}

/// <summary>
/// VIX cache performance metrics
/// </summary>
public class VixCacheMetrics
{
    public long CacheHits { get; set; }
    public long CacheMisses { get; set; }
    public long TotalCacheRequests { get; set; }
    public decimal CacheHitRate { get; set; }
    public long TotalRetrievals { get; set; }
    public long TotalApiCalls { get; set; }
    public decimal AverageApiCallsPerRetrieval { get; set; }
    public DateTime LastUpdated { get; set; }
}

/// <summary>
/// VIX cache hit metric details
/// </summary>
public class VixCacheHitMetric
{
    public string DataSource { get; set; } = string.Empty;
    public TimeSpan Age { get; set; }
    public decimal QualityScore { get; set; }
    public DateTime Timestamp { get; set; }

    public string ToJson() => JsonSerializer.Serialize(this);
    public static VixCacheHitMetric? FromJson(string json) => JsonSerializer.Deserialize<VixCacheHitMetric>(json);
}

/// <summary>
/// VIX cache miss metric details
/// </summary>
public class VixCacheMissMetric
{
    public string Reason { get; set; } = string.Empty;
    public DateTime Timestamp { get; set; }

    public string ToJson() => JsonSerializer.Serialize(this);
    public static VixCacheMissMetric? FromJson(string json) => JsonSerializer.Deserialize<VixCacheMissMetric>(json);
}

/// <summary>
/// VIX retrieval performance metric details
/// </summary>
public class VixRetrievalPerformanceMetric
{
    public string DataSource { get; set; } = string.Empty;
    public TimeSpan Duration { get; set; }
    public bool Success { get; set; }
    public int ApiCallsUsed { get; set; }
    public DateTime Timestamp { get; set; }

    public string ToJson() => JsonSerializer.Serialize(this);
    public static VixRetrievalPerformanceMetric? FromJson(string json) => JsonSerializer.Deserialize<VixRetrievalPerformanceMetric>(json);
}
