using SmaTrendFollower.Models;

namespace SmaTrendFollower.Services;

/// <summary>
/// Service for detecting and caching market regime information
/// </summary>
public interface IMarketRegimeService
{
    /// <summary>
    /// Detects the current market regime based on SPY analysis
    /// </summary>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>The detected market regime</returns>
    Task<MarketRegime> DetectRegimeAsync(CancellationToken cancellationToken = default);
    
    /// <summary>
    /// Gets the cached market regime from Redis
    /// </summary>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>The cached market regime, or Sideways if not found</returns>
    Task<MarketRegime> GetCachedRegimeAsync(CancellationToken cancellationToken = default);
    
    /// <summary>
    /// Gets detailed regime information including metrics
    /// </summary>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Detailed regime information</returns>
    Task<RedisMarketRegime?> GetRegimeDetailsAsync(CancellationToken cancellationToken = default);
    
    /// <summary>
    /// Forces a regime detection and cache update
    /// </summary>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>The newly detected regime</returns>
    Task<MarketRegime> RefreshRegimeAsync(CancellationToken cancellationToken = default);
    
    /// <summary>
    /// Checks if trading should be allowed based on current regime
    /// </summary>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>True if trading is allowed, false otherwise</returns>
    Task<bool> IsTradingAllowedAsync(CancellationToken cancellationToken = default);
}
