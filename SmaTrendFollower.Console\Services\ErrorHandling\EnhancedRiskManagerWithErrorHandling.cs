using SmaTrendFollower.Models;
using SmaTrendFollower.Services.ErrorHandling;
using Alpaca.Markets;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Caching.Memory;

namespace SmaTrendFollower.Services;

/// <summary>
/// Enhanced risk manager with comprehensive error handling and caching
/// </summary>
public sealed class EnhancedRiskManagerWithErrorHandling : IRiskManager
{
    private readonly IAlpacaClientFactory _clientFactory;
    private readonly IEnhancedRetryService _retryService;
    private readonly IErrorHandler _errorHandler;
    private readonly IMemoryCache _cache;
    private readonly ILogger<EnhancedRiskManagerWithErrorHandling> _logger;
    private readonly SemaphoreSlim _accountSemaphore = new(1, 1);

    private const string ACCOUNT_CACHE_KEY = "account_info";
    private static readonly TimeSpan AccountCacheExpiry = TimeSpan.FromMinutes(5);

    public EnhancedRiskManagerWithErrorHandling(
        IAlpacaClientFactory clientFactory,
        IEnhancedRetryService retryService,
        IErrorHandler errorHandler,
        IMemoryCache cache,
        ILogger<EnhancedRiskManagerWithErrorHandling> logger)
    {
        _clientFactory = clientFactory;
        _retryService = retryService;
        _errorHandler = errorHandler;
        _cache = cache;
        _logger = logger;
    }

    public async Task<decimal> CalculateQuantityAsync(TradingSignal signal)
    {
        var context = new ErrorContext
        {
            OperationName = nameof(CalculateQuantityAsync),
            ServiceName = nameof(EnhancedRiskManagerWithErrorHandling)
        }.WithProperty("Symbol", signal.Symbol)
         .WithProperty("Price", signal.Price)
         .WithProperty("ATR", signal.Atr);

        try
        {
            // Validate input signal
            ValidateSignal(signal);

            return await _retryService.ExecuteAsync(
                async () => await CalculateQuantityInternalAsync(signal),
                $"CalculateQuantity-{signal.Symbol}",
                "Alpaca");
        }
        catch (Exception ex)
        {
            var result = await _errorHandler.HandleErrorAsync(ex, context);
            
            switch (result.Strategy)
            {
                case RecoveryStrategy.Fallback:
                    _logger.LogWarning("Using fallback risk calculation for {Symbol}", signal.Symbol);
                    return CalculateFallbackQuantity(signal);
                
                case RecoveryStrategy.Skip:
                    _logger.LogWarning("Skipping position sizing for {Symbol} due to error", signal.Symbol);
                    return 0;
                
                default:
                    throw new RiskManagementException(
                        $"Failed to calculate position size for {signal.Symbol}",
                        "PositionSizing",
                        innerException: ex);
            }
        }
    }

    private async Task<decimal> CalculateQuantityInternalAsync(TradingSignal signal)
    {
        // Get account information with caching and error handling
        var account = await GetAccountWithErrorHandlingAsync();
        
        if (account == null)
        {
            throw new RiskManagementException("Unable to retrieve account information", "AccountAccess");
        }

        var equity = account.Equity ?? 0m;
        
        // Validate account equity
        if (equity <= 0)
        {
            throw new RiskManagementException($"Invalid account equity: {equity}", "AccountValidation");
        }

        // Calculate dynamic risk parameters
        var riskParams = CalculateDynamicRiskParameters(equity);
        
        // Validate risk parameters
        ValidateRiskParameters(riskParams, equity);

        // Calculate risk dollars using dynamic percentage
        var riskDollars = equity * riskParams.RiskPercentPerTrade;
        riskDollars = Math.Min(riskDollars, riskParams.MaxRiskPerTrade);

        // Calculate quantity with multiple safety checks
        var quantity = CalculatePositionSize(signal, riskDollars, equity, riskParams);

        // Final validation
        ValidateCalculatedQuantity(signal, quantity, equity, riskDollars);

        _logger.LogInformation("Risk calculation for {Symbol}: Equity={Equity:C}, " +
                             "RiskPercent={RiskPercent:P3}, RiskDollars={RiskDollars:C}, " +
                             "Price={Price:C}, ATR={ATR:C}, Quantity={Quantity}, " +
                             "PositionValue={PositionValue:C}, PositionPercent={PositionPercent:P2}",
            signal.Symbol, equity, riskParams.RiskPercentPerTrade, riskDollars,
            signal.Price, signal.Atr, quantity, quantity * signal.Price,
            (quantity * signal.Price) / equity);

        return quantity;
    }

    private async Task<IAccount?> GetAccountWithErrorHandlingAsync()
    {
        // Check cache first
        if (_cache.TryGetValue(ACCOUNT_CACHE_KEY, out IAccount? cachedAccount))
        {
            _logger.LogDebug("Using cached account information");
            return cachedAccount;
        }

        await _accountSemaphore.WaitAsync();
        try
        {
            // Double-check cache after acquiring lock
            if (_cache.TryGetValue(ACCOUNT_CACHE_KEY, out cachedAccount))
            {
                return cachedAccount;
            }

            var context = new ErrorContext
            {
                OperationName = "GetAccount",
                ServiceName = nameof(EnhancedRiskManagerWithErrorHandling)
            };

            try
            {
                var account = await _retryService.ExecuteAsync(
                    async () =>
                    {
                        using var tradingClient = _clientFactory.CreateTradingClient();
                        return await tradingClient.GetAccountAsync();
                    },
                    "GetAccount",
                    "Alpaca");

                // Cache the account information
                _cache.Set(ACCOUNT_CACHE_KEY, account, AccountCacheExpiry);
                
                return account;
            }
            catch (Exception ex)
            {
                var result = await _errorHandler.HandleErrorAsync(ex, context);
                
                if (result.Strategy == RecoveryStrategy.Fallback)
                {
                    _logger.LogWarning("Unable to get fresh account data, checking for stale cache");
                    // Try to use stale cached data if available
                    return _cache.Get<IAccount>(ACCOUNT_CACHE_KEY);
                }

                throw new RiskManagementException(
                    "Failed to retrieve account information",
                    "AccountAccess",
                    innerException: ex);
            }
        }
        finally
        {
            _accountSemaphore.Release();
        }
    }

    private void ValidateSignal(TradingSignal signal)
    {
        if (signal == null)
            throw new ArgumentNullException(nameof(signal));

        if (string.IsNullOrEmpty(signal.Symbol))
            throw new RiskManagementException("Signal symbol cannot be null or empty", "SignalValidation");

        if (signal.Price <= 0)
            throw new RiskManagementException($"Invalid signal price: {signal.Price}", "SignalValidation");

        if (signal.Atr <= 0)
            throw new RiskManagementException($"Invalid signal ATR: {signal.Atr}", "SignalValidation");

        if (signal.Atr / signal.Price > 0.5m) // ATR > 50% of price is suspicious
            throw new RiskManagementException($"Suspicious ATR/Price ratio: {signal.Atr / signal.Price:P2}", "SignalValidation");
    }

    private void ValidateRiskParameters(DynamicRiskParameters riskParams, decimal equity)
    {
        if (riskParams.RiskPercentPerTrade <= 0 || riskParams.RiskPercentPerTrade > 0.1m)
            throw new RiskManagementException($"Invalid risk percentage: {riskParams.RiskPercentPerTrade:P2}", "RiskParameterValidation");

        if (riskParams.MaxRiskPerTrade <= 0)
            throw new RiskManagementException($"Invalid max risk amount: {riskParams.MaxRiskPerTrade}", "RiskParameterValidation");

        if (riskParams.MaxPositionSizePercent <= 0 || riskParams.MaxPositionSizePercent > 0.5m)
            throw new RiskManagementException($"Invalid max position size: {riskParams.MaxPositionSizePercent:P2}", "RiskParameterValidation");
    }

    private decimal CalculatePositionSize(TradingSignal signal, decimal riskDollars, decimal equity, DynamicRiskParameters riskParams)
    {
        // Calculate quantity: qty = riskDollars / (atr14 * price)
        var quantity = riskDollars / (signal.Atr * signal.Price);

        // Ensure quantity doesn't exceed risk tolerance: qty <= riskDollars / price
        var maxQuantityByRisk = riskDollars / signal.Price;
        quantity = Math.Min(quantity, maxQuantityByRisk);

        // Apply position size limit as percentage of equity
        var maxQuantityByPosition = (equity * riskParams.MaxPositionSizePercent) / signal.Price;
        quantity = Math.Min(quantity, maxQuantityByPosition);

        // Round down to avoid over-allocation
        quantity = Math.Floor(quantity * 100) / 100; // Round to 2 decimal places

        return Math.Max(0, quantity);
    }

    private void ValidateCalculatedQuantity(TradingSignal signal, decimal quantity, decimal equity, decimal riskDollars)
    {
        if (quantity < 0)
            throw new RiskManagementException($"Negative quantity calculated: {quantity}", "QuantityValidation");

        var positionValue = quantity * signal.Price;
        
        if (positionValue > equity)
            throw new RiskManagementException($"Position value ({positionValue:C}) exceeds equity ({equity:C})", "QuantityValidation");

        if (positionValue > riskDollars * 10) // Sanity check: position shouldn't be more than 10x risk
            throw new RiskManagementException($"Position value ({positionValue:C}) is excessive compared to risk ({riskDollars:C})", "QuantityValidation");
    }

    private decimal CalculateFallbackQuantity(TradingSignal signal)
    {
        // Conservative fallback calculation without account data
        // Assume $10,000 account with 0.5% risk
        const decimal fallbackEquity = 10000m;
        const decimal fallbackRiskPercent = 0.005m;
        
        var fallbackRiskDollars = fallbackEquity * fallbackRiskPercent;
        var quantity = fallbackRiskDollars / (signal.Atr * signal.Price);
        
        // Apply conservative limits
        var maxQuantity = Math.Min(fallbackRiskDollars / signal.Price, 10); // Max 10 shares
        quantity = Math.Min(quantity, maxQuantity);
        
        quantity = Math.Floor(quantity * 100) / 100;
        
        _logger.LogWarning("Using fallback quantity calculation for {Symbol}: {Quantity} shares", 
            signal.Symbol, quantity);
        
        return Math.Max(0, quantity);
    }

    private DynamicRiskParameters CalculateDynamicRiskParameters(decimal equity)
    {
        // Progressive risk scaling based on account size with enhanced validation
        var (riskPercent, maxRisk, maxPositionPercent) = equity switch
        {
            // Very small accounts (under $5k) - higher risk tolerance
            < 5000m => (0.015m, 75m, 0.08m),      // 1.5% risk, $75 max, 8% position max

            // Small accounts ($5k - $15k) - moderate risk
            < 15000m => (0.012m, 150m, 0.06m),    // 1.2% risk, $150 max, 6% position max

            // Medium accounts ($15k - $50k) - balanced risk
            < 50000m => (0.010m, 400m, 0.05m),    // 1.0% risk, $400 max, 5% position max

            // Large accounts ($50k - $100k) - conservative risk
            < 100000m => (0.008m, 600m, 0.04m),   // 0.8% risk, $600 max, 4% position max

            // Very large accounts ($100k+) - very conservative
            _ => (0.006m, 800m, 0.03m)             // 0.6% risk, $800 max, 3% position max
        };

        return new DynamicRiskParameters(riskPercent, maxRisk, maxPositionPercent);
    }
}

/// <summary>
/// Dynamic risk parameters based on account size
/// </summary>
public record DynamicRiskParameters(
    decimal RiskPercentPerTrade,
    decimal MaxRiskPerTrade,
    decimal MaxPositionSizePercent
);
