# Dynamic Risk Management System

## Overview

The SmaTrendFollower now features a **Dynamic Risk Management System** that automatically adjusts risk parameters based on the current account size. This system eliminates the need for manual safety configuration updates when funds are added or withdrawn from the account.

## Key Features

### 🎯 **Automatic Risk Scaling**
- Risk parameters automatically adjust based on real-time account equity
- No manual configuration required when account size changes
- Progressive risk scaling optimized for different account tiers

### 📊 **Account Size Tiers**

| Tier | Account Size | Risk % | Max Risk | Position % | Max Positions |
|------|-------------|--------|----------|------------|---------------|
| **Very Small** | < $5,000 | 1.5% | $75 | 8% | 3 |
| **Small** | $5,000 - $15,000 | 1.2% | $150 | 6% | 4 |
| **Medium** | $15,000 - $50,000 | 1.0% | $400 | 5% | 5 |
| **Large** | $50,000 - $100,000 | 0.8% | $600 | 4% | 6 |
| **Very Large** | $100,000 - $500,000 | 0.8% | $600 | 3% | 8 |
| **Institutional** | $500,000+ | 0.6% | $800 | 2.5% | 10 |

### 🛡️ **Safety Features**
- **Daily Loss Limits**: Automatically scaled as percentage of equity
- **Position Size Limits**: Maximum position size as percentage of account
- **Trade Count Limits**: Scaled based on account size and complexity
- **Single Trade Limits**: Maximum risk per individual trade

## Current Account Status

**Your Account**: $12,035 (Small Tier)
- **Risk per Trade**: 1.2% = $144 max
- **Max Single Trade**: $150 (capped)
- **Max Position Size**: 6% of equity = $722
- **Max Positions**: 4 concurrent positions
- **Daily Loss Limit**: 1.5% = $180

## Risk Philosophy by Account Size

### Small Accounts ($5k - $15k)
- **Higher risk tolerance** to enable meaningful position sizes
- **Focused diversification** with fewer positions
- **Growth-oriented** approach with moderate safety nets

### Medium Accounts ($15k - $50k)
- **Balanced approach** between growth and preservation
- **Moderate diversification** with reasonable position limits
- **Steady growth** focus with conservative risk management

### Large Accounts ($50k+)
- **Capital preservation** becomes primary focus
- **Lower risk percentages** with higher absolute dollar limits
- **Institutional-grade** risk controls and diversification

## Commands

### View Dynamic Risk Summary
```bash
dotnet run --project SmaTrendFollower.Console -- --risk-summary
```

### View Current Safety Configuration
```bash
dotnet run --project SmaTrendFollower.Console -- --show-safety
```

### Check Account Status
```bash
dotnet run --project SmaTrendFollower.Console -- --account-status
```

## Technical Implementation

### Core Components

1. **DynamicSafetyConfigurationService**
   - Fetches real-time account equity
   - Calculates appropriate risk parameters
   - Returns dynamic safety configuration

2. **Enhanced RiskManager**
   - Uses dynamic risk parameters for position sizing
   - Applies progressive risk scaling
   - Enforces position size limits

3. **Account Risk Summary**
   - Provides comprehensive risk overview
   - Shows tier classification
   - Displays all active limits

### Integration Points

- **Program.cs**: Uses dynamic configuration instead of static .env values
- **TradingService**: Automatically benefits from dynamic risk calculations
- **Safety Guards**: All safety systems use dynamic parameters

## Benefits

### ✅ **Automatic Adaptation**
- No manual updates required when account size changes
- Risk parameters always appropriate for current equity level
- Seamless scaling as account grows or shrinks

### ✅ **Optimized Risk-Return**
- Small accounts: Higher risk tolerance for meaningful growth
- Large accounts: Conservative approach for capital preservation
- Medium accounts: Balanced strategy for steady growth

### ✅ **Enhanced Safety**
- Account-appropriate risk limits
- Progressive scaling prevents over-leveraging
- Built-in safeguards for all account sizes

### ✅ **Professional Grade**
- Institutional-quality risk management
- Real-time parameter adjustment
- Comprehensive monitoring and reporting

## Migration from Static Configuration

The system has been updated to use dynamic configuration while maintaining backward compatibility:

- **Old**: Static values in .env file
- **New**: Dynamic calculation based on account equity
- **Fallback**: Base configuration used if dynamic calculation fails

## Production Readiness

With the dynamic risk management system in place:

✅ **Risk parameters automatically scale with account size**  
✅ **No manual configuration required for different equity levels**  
✅ **Professional-grade risk management for $12k account**  
✅ **All safety systems validated and tested**  
✅ **Real-time adaptation to account changes**

The system is now **production-ready** with appropriate risk management for your current account size and will automatically adapt as your account grows.
