using Alpaca.Markets;
using Microsoft.Extensions.Logging;
using System.Collections.Concurrent;
using System.Diagnostics;

namespace SmaTrendFollower.Services;

/// <summary>
/// High-performance market data service with parallel fetching capabilities.
/// Maximizes bandwidth utilization and minimizes latency through concurrent operations.
/// </summary>
public sealed class ParallelMarketDataService : IMarketDataService
{
    private readonly IMarketDataService _baseService;
    private readonly ILogger<ParallelMarketDataService> _logger;
    private readonly SemaphoreSlim _concurrencyLimiter;

    public ParallelMarketDataService(
        IMarketDataService baseService,
        ILogger<ParallelMarketDataService> logger)
    {
        _baseService = baseService;
        _logger = logger;
        
        // Limit concurrent requests to avoid overwhelming APIs
        _concurrencyLimiter = new SemaphoreSlim(25, 25);
    }

    // Delegate single symbol requests to base service
    public Task<IPage<IBar>> GetStockBarsAsync(string symbol, DateTime startDate, DateTime endDate)
        => _baseService.GetStockBarsAsync(symbol, startDate, endDate);

    public Task<IPage<IBar>> GetStockMinuteBarsAsync(string symbol, DateTime startDate, DateTime endDate)
        => _baseService.GetStockMinuteBarsAsync(symbol, startDate, endDate);

    /// <summary>
    /// Enhanced parallel implementation for multiple symbols with intelligent batching
    /// </summary>
    public async Task<IDictionary<string, IPage<IBar>>> GetStockBarsAsync(IEnumerable<string> symbols, DateTime startDate, DateTime endDate)
    {
        return await GetMultipleStockBarsParallelAsync(symbols, "Day", startDate, endDate);
    }

    /// <summary>
    /// Enhanced parallel implementation for multiple minute bars
    /// </summary>
    public async Task<IDictionary<string, IPage<IBar>>> GetStockMinuteBarsAsync(IEnumerable<string> symbols, DateTime startDate, DateTime endDate)
    {
        return await GetMultipleStockBarsParallelAsync(symbols, "Minute", startDate, endDate);
    }

    /// <summary>
    /// High-performance parallel data fetching with adaptive batching and error resilience
    /// </summary>
    private async Task<IDictionary<string, IPage<IBar>>> GetMultipleStockBarsParallelAsync(
        IEnumerable<string> symbols, string timeFrame, DateTime startDate, DateTime endDate)
    {
        var stopwatch = Stopwatch.StartNew();
        var symbolList = symbols.ToList();
        var results = new ConcurrentDictionary<string, IPage<IBar>>();
        var successCount = 0;
        var errorCount = 0;

        _logger.LogInformation("Starting parallel {TimeFrame} data fetch for {Count} symbols", timeFrame, symbolList.Count);

        // Create parallel tasks with concurrency limiting
        var fetchTasks = symbolList.Select(async symbol =>
        {
            await _concurrencyLimiter.WaitAsync();
            try
            {
                IPage<IBar> bars;
                if (timeFrame == "Day")
                {
                    bars = await _baseService.GetStockBarsAsync(symbol, startDate, endDate);
                }
                else
                {
                    bars = await _baseService.GetStockMinuteBarsAsync(symbol, startDate, endDate);
                }

                results[symbol] = bars;
                Interlocked.Increment(ref successCount);

                if (successCount % 25 == 0)
                {
                    _logger.LogDebug("Parallel fetch progress: {Success}/{Total} symbols completed", 
                        successCount, symbolList.Count);
                }
            }
            catch (Exception ex)
            {
                Interlocked.Increment(ref errorCount);
                _logger.LogWarning(ex, "Failed to fetch {TimeFrame} bars for {Symbol}", timeFrame, symbol);
            }
            finally
            {
                _concurrencyLimiter.Release();
            }
        });

        await Task.WhenAll(fetchTasks);
        stopwatch.Stop();

        var throughput = symbolList.Count / stopwatch.Elapsed.TotalSeconds;
        
        _logger.LogInformation("Parallel {TimeFrame} fetch completed: {Success}/{Total} symbols in {ElapsedMs:F0}ms " +
                              "({Throughput:F1} symbols/sec, {ErrorCount} errors)", 
            timeFrame, successCount, symbolList.Count, stopwatch.Elapsed.TotalMilliseconds, 
            throughput, errorCount);

        return results;
    }

    // Delegate all other methods to base service
    public Task<decimal?> GetIndexValueAsync(string indexSymbol)
        => _baseService.GetIndexValueAsync(indexSymbol);

    public Task<IEnumerable<IndexBar>> GetIndexBarsAsync(string indexSymbol, DateTime startDate, DateTime endDate)
        => _baseService.GetIndexBarsAsync(indexSymbol, startDate, endDate);

    public Task<IAccount> GetAccountAsync()
        => _baseService.GetAccountAsync();

    public Task<IReadOnlyList<IPosition>> GetPositionsAsync()
        => _baseService.GetPositionsAsync();

    public Task<IReadOnlyList<IOrder>> GetRecentFillsAsync(int limitCount = 100)
        => _baseService.GetRecentFillsAsync(limitCount);

    public Task<IEnumerable<OptionData>> GetOptionsDataAsync(string underlyingSymbol, DateTime? expirationDate = null)
        => _baseService.GetOptionsDataAsync(underlyingSymbol, expirationDate);

    public Task<IEnumerable<VixTermData>> GetVixTermStructureAsync()
        => _baseService.GetVixTermStructureAsync();

    public Task<IEnumerable<OptionQuote>> GetOptionsQuotesAsync(IEnumerable<string> optionSymbols)
        => _baseService.GetOptionsQuotesAsync(optionSymbols);

    public Task<IEnumerable<OptionData>> GetProtectivePutOptionsAsync(string underlyingSymbol, int daysToExpiration = 30)
        => _baseService.GetProtectivePutOptionsAsync(underlyingSymbol, daysToExpiration);

    public Task<IEnumerable<OptionData>> GetCoveredCallOptionsAsync(string underlyingSymbol, decimal currentPrice, int daysToExpiration = 7)
        => _baseService.GetCoveredCallOptionsAsync(underlyingSymbol, currentPrice, daysToExpiration);

    public Task<IEnumerable<string>> GetUniverseWithAdvFilterAsync(decimal minAdv = 20_000_000m)
        => _baseService.GetUniverseWithAdvFilterAsync(minAdv);

    public Task<VixAnalysis> GetVixAnalysisAsync()
        => _baseService.GetVixAnalysisAsync();

    public Task<bool> IsVixSpikeAsync(decimal threshold = 25.0m)
        => _baseService.IsVixSpikeAsync(threshold);

    public void Dispose()
    {
        _concurrencyLimiter?.Dispose();
        if (_baseService is IDisposable disposable)
        {
            disposable.Dispose();
        }
    }
}

/// <summary>
/// Performance metrics for parallel market data operations
/// </summary>
public record ParallelDataMetrics(
    int RequestedSymbols,
    int SuccessfulFetches,
    int FailedFetches,
    double ElapsedTimeMs,
    double ThroughputSymbolsPerSecond,
    double AverageLatencyMs,
    int ConcurrentRequests
);
