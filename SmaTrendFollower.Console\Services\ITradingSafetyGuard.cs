using SmaTrendFollower.Models;

namespace SmaTrendFollower.Services;

/// <summary>
/// Safety guard interface for trading operations
/// </summary>
public interface ITradingSafetyGuard
{
    /// <summary>
    /// Validates if trading is safe to proceed
    /// </summary>
    Task<SafetyCheckResult> ValidateTradeAsync(TradingSignal signal, decimal quantity);
    
    /// <summary>
    /// Validates if the trading cycle can proceed
    /// </summary>
    Task<SafetyCheckResult> ValidateTradingCycleAsync();
    
    /// <summary>
    /// Gets current safety configuration
    /// </summary>
    SafetyConfiguration GetConfiguration();
    
    /// <summary>
    /// Updates safety configuration
    /// </summary>
    void UpdateConfiguration(SafetyConfiguration config);
}

/// <summary>
/// Result of safety validation
/// </summary>
public record SafetyCheckResult(
    bool IsAllowed,
    string Reason,
    SafetyLevel Level = SafetyLevel.Info
);

/// <summary>
/// Safety level enumeration
/// </summary>
public enum SafetyLevel
{
    Info,
    Warning,
    Error,
    Critical
}

/// <summary>
/// Safety configuration settings
/// </summary>
public record SafetyConfiguration
{
    /// <summary>
    /// Maximum daily loss limit in dollars
    /// </summary>
    public decimal MaxDailyLoss { get; init; } = 500m;
    
    /// <summary>
    /// Maximum position size as percentage of equity
    /// </summary>
    public decimal MaxPositionSizePercent { get; init; } = 0.05m; // 5%
    
    /// <summary>
    /// Maximum number of positions
    /// </summary>
    public int MaxPositions { get; init; } = 10;
    
    /// <summary>
    /// Maximum daily trades
    /// </summary>
    public int MaxDailyTrades { get; init; } = 20;
    
    /// <summary>
    /// Minimum account equity required for trading
    /// </summary>
    public decimal MinAccountEquity { get; init; } = 1000m;
    
    /// <summary>
    /// Maximum single trade value
    /// </summary>
    public decimal MaxSingleTradeValue { get; init; } = 2000m;
    
    /// <summary>
    /// Require confirmation for live trading
    /// </summary>
    public bool RequireConfirmation { get; init; } = true;
    
    /// <summary>
    /// Enable dry run mode (no actual trades)
    /// </summary>
    public bool DryRunMode { get; init; } = false;
    
    /// <summary>
    /// Trading environment validation
    /// </summary>
    public TradingEnvironment AllowedEnvironment { get; init; } = TradingEnvironment.Paper;
}

/// <summary>
/// Trading environment types
/// </summary>
public enum TradingEnvironment
{
    Paper,
    Live,
    Both
}
