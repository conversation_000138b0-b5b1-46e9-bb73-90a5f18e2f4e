#!/usr/bin/env pwsh

# Optimize test performance by fixing timeouts and categories

Write-Host "🚀 Optimizing test performance..." -ForegroundColor Cyan

$testFiles = Get-ChildItem -Path "SmaTrendFollower.Tests" -Filter "*.cs" -Recurse | Where-Object { $_.Name -like "*Tests.cs" }

$changes = 0

foreach ($file in $testFiles) {
    Write-Host "Processing: $($file.Name)" -ForegroundColor Yellow
    
    $content = Get-Content $file.FullName -Raw
    $originalContent = $content
    
    # Fix Database tests with excessive timeouts
    $content = $content -replace '\[TestTimeout\(TestTimeouts\.Database\)\]\s*\[Trait\("Category", TestCategories\.Database\)\]', '[TestTimeout(TestTimeouts.Unit)]`n    [Trait("Category", TestCategories.Database)]'
    
    # Fix Network tests that are actually unit tests (no real network calls)
    if ($file.Name -like "*ClientFactory*" -or $file.Name -like "*Factory*") {
        $content = $content -replace '\[TestTimeout\(TestTimeouts\.Network\)\]\s*\[Trait\("Category", TestCategories\.Network\)\]', '[TestTimeout(TestTimeouts.Unit)]`n    [Trait("Category", TestCategories.Unit)]'
    }
    
    # Fix Performance tests with reduced iterations
    $content = $content -replace 'const int iterations = 1000;', 'const int iterations = 100;'
    $content = $content -replace 'for \(int i = 0; i < 1000; i\+\+\)', 'for (int i = 0; i < 100; i++)'
    
    # Fix concurrent operation tests (reduce from 10 to 3)
    $content = $content -replace 'for \(int i = 0; i < 10; i\+\+\)', 'for (int i = 0; i < 3; i++)'
    
    # Fix delay operations in tests
    $content = $content -replace 'await Task\.Delay\(1000\)', 'await Task.Delay(10)'
    $content = $content -replace 'await Task\.Delay\(500\)', 'await Task.Delay(5)'
    $content = $content -replace 'TimeSpan\.FromSeconds\(1\)', 'TimeSpan.FromMilliseconds(10)'
    
    if ($content -ne $originalContent) {
        Set-Content $file.FullName -Value $content -NoNewline
        $changes++
        Write-Host "  ✅ Optimized $($file.Name)" -ForegroundColor Green
    } else {
        Write-Host "  ⏭️  No changes needed for $($file.Name)" -ForegroundColor Gray
    }
}

Write-Host "`n📊 Performance optimization complete!" -ForegroundColor Green
Write-Host "   Files processed: $($testFiles.Count)" -ForegroundColor White
Write-Host "   Files optimized: $changes" -ForegroundColor White

# Create fast test runner script
$fastTestScript = @'
#!/usr/bin/env pwsh

# Fast test execution script

Write-Host "🏃‍♂️ Running fast unit tests..." -ForegroundColor Cyan

# Run only unit tests with optimized settings
dotnet test SmaTrendFollower.Tests `
    --filter "Category=Unit" `
    --settings SmaTrendFollower.Tests/TestFilters.runsettings `
    --verbosity minimal `
    --no-build `
    --parallel `
    --logger "console;verbosity=minimal"

if ($LASTEXITCODE -eq 0) {
    Write-Host "✅ All unit tests passed!" -ForegroundColor Green
} else {
    Write-Host "❌ Some tests failed. Check output above." -ForegroundColor Red
}
'@

Set-Content "run-fast-tests.ps1" -Value $fastTestScript
Write-Host "📝 Created run-fast-tests.ps1 script" -ForegroundColor Blue

Write-Host "`n🎯 Next steps:" -ForegroundColor Cyan
Write-Host "   1. Run: ./run-fast-tests.ps1" -ForegroundColor White
Write-Host "   2. Expected execution time: under 30 seconds" -ForegroundColor White
Write-Host "   3. For integration tests: dotnet test --filter Category=Integration" -ForegroundColor White
