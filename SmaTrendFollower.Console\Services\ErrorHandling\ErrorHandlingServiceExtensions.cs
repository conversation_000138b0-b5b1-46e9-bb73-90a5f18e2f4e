using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Options;
using SmaTrendFollower.Models;

namespace SmaTrendFollower.Services.ErrorHandling;

/// <summary>
/// Extension methods for registering error handling services
/// </summary>
public static class ErrorHandlingServiceExtensions
{
    /// <summary>
    /// Add comprehensive error handling services to the service collection
    /// </summary>
    public static IServiceCollection AddErrorHandling(this IServiceCollection services, IConfiguration configuration)
    {
        // Configure error handler options
        services.Configure<ErrorHandlerOptions>(configuration.GetSection("ErrorHandling"));
        
        // Configure circuit breaker options
        services.Configure<CircuitBreakerOptions>(configuration.GetSection("CircuitBreaker"));
        
        // Configure retry policy options
        services.Configure<RetryPolicy>(configuration.GetSection("RetryPolicy"));

        // Register core error handling services
        services.AddSingleton<IErrorHandler, ErrorHandler>();
        services.AddSingleton<ICircuitBreakerService, CircuitBreakerService>();
        services.AddSingleton<IEnhancedRetryService, EnhancedRetryService>();
        services.AddSingleton<IRecoveryService, RecoveryService>();
        services.AddSingleton<IHealthMonitoringService, HealthMonitoringService>();

        return services;
    }

    /// <summary>
    /// Add error handling services with custom configuration
    /// </summary>
    public static IServiceCollection AddErrorHandling(
        this IServiceCollection services,
        Action<ErrorHandlerOptions>? configureErrorHandler = null,
        Action<CircuitBreakerOptions>? configureCircuitBreaker = null,
        Action<RetryPolicy>? configureRetryPolicy = null)
    {
        // Configure options with custom actions
        if (configureErrorHandler != null)
        {
            services.Configure(configureErrorHandler);
        }
        else
        {
            services.Configure<ErrorHandlerOptions>(options =>
            {
                options.MaxRetryAttempts = 3;
                options.BaseDelayMs = 1000;
                options.MaxDelayMs = 30000;
                options.UseJitter = true;
                options.EnableCircuitBreaker = true;
                options.CircuitBreakerThreshold = 5;
                options.CircuitBreakerTimeoutMinutes = 5;
                options.EnableNotifications = true;
                options.NotificationThreshold = ErrorSeverity.High;
            });
        }

        if (configureCircuitBreaker != null)
        {
            services.Configure(configureCircuitBreaker);
        }
        else
        {
            services.Configure<CircuitBreakerOptions>(options =>
            {
                options.FailureThreshold = 5;
                options.OpenTimeout = TimeSpan.FromMinutes(1);
                options.SuccessThreshold = 3;
                options.FailureWindow = TimeSpan.FromMinutes(5);
                options.EnableAutoRecovery = true;
                options.RecoveryInterval = TimeSpan.FromSeconds(30);
            });
        }

        if (configureRetryPolicy != null)
        {
            services.Configure(configureRetryPolicy);
        }
        else
        {
            services.Configure<RetryPolicy>(options =>
            {
                options.MaxAttempts = 3;
                options.BaseDelay = TimeSpan.FromSeconds(1);
                options.MaxDelay = TimeSpan.FromSeconds(30);
                options.BackoffStrategy = BackoffStrategy.Exponential;
                options.UseJitter = true;
                options.EnableCircuitBreaker = true;
            });
        }

        // Register services
        services.AddSingleton<IErrorHandler, ErrorHandler>();
        services.AddSingleton<ICircuitBreakerService, CircuitBreakerService>();
        services.AddSingleton<IEnhancedRetryService, EnhancedRetryService>();
        services.AddSingleton<IRecoveryService, RecoveryService>();
        services.AddSingleton<IHealthMonitoringService, HealthMonitoringService>();

        return services;
    }

    /// <summary>
    /// Register predefined health checks for common services
    /// </summary>
    public static IServiceCollection AddPredefinedHealthChecks(this IServiceCollection services)
    {
        services.AddSingleton<IHealthCheckRegistrar, HealthCheckRegistrar>();
        return services;
    }

    /// <summary>
    /// Configure error handling for trading services
    /// </summary>
    public static IServiceCollection AddTradingErrorHandling(this IServiceCollection services, IConfiguration configuration)
    {
        services.AddErrorHandling(configuration);

        // Register enhanced trading services with error handling
        services.AddScoped<EnhancedSignalGeneratorWithErrorHandling>();
        services.AddScoped<EnhancedRiskManagerWithErrorHandling>();
        services.AddScoped<EnhancedPortfolioGateWithErrorHandling>();

        // Register fallback strategies
        services.AddSingleton<ITradingFallbackStrategies, TradingFallbackStrategies>();

        return services;
    }
}

/// <summary>
/// Health check registrar for setting up predefined health checks
/// </summary>
public interface IHealthCheckRegistrar
{
    /// <summary>
    /// Register all predefined health checks
    /// </summary>
    Task RegisterAllHealthChecksAsync();
}

/// <summary>
/// Implementation of health check registrar
/// </summary>
public sealed class HealthCheckRegistrar : IHealthCheckRegistrar
{
    private readonly IHealthMonitoringService _healthMonitoringService;
    private readonly IServiceProvider _serviceProvider;
    private readonly ILogger<HealthCheckRegistrar> _logger;

    public HealthCheckRegistrar(
        IHealthMonitoringService healthMonitoringService,
        IServiceProvider serviceProvider,
        ILogger<HealthCheckRegistrar> logger)
    {
        _healthMonitoringService = healthMonitoringService;
        _serviceProvider = serviceProvider;
        _logger = logger;
    }

    public async Task RegisterAllHealthChecksAsync()
    {
        try
        {
            // Register Alpaca health check
            if (_serviceProvider.GetService<IAlpacaClientFactory>() is { } alpacaFactory)
            {
                _healthMonitoringService.RegisterHealthCheck(
                    "Alpaca",
                    PredefinedHealthChecks.CreateAlpacaHealthCheck(alpacaFactory, _logger),
                    new HealthCheckOptions
                    {
                        CheckInterval = TimeSpan.FromMinutes(2),
                        Timeout = TimeSpan.FromSeconds(30),
                        EnableAlerting = true,
                        AlertPriority = AlertPriority.Critical,
                        Tags = { "trading", "api", "critical" }
                    });
            }

            // Register Polygon health check
            if (_serviceProvider.GetService<IPolygonClientFactory>() is { } polygonFactory)
            {
                _healthMonitoringService.RegisterHealthCheck(
                    "Polygon",
                    PredefinedHealthChecks.CreatePolygonHealthCheck(polygonFactory, _logger),
                    new HealthCheckOptions
                    {
                        CheckInterval = TimeSpan.FromMinutes(3),
                        Timeout = TimeSpan.FromSeconds(20),
                        EnableAlerting = true,
                        AlertPriority = AlertPriority.High,
                        Tags = { "data", "api", "fallback" }
                    });
            }

            // Register Redis health check
            if (_serviceProvider.GetService<StackExchange.Redis.IConnectionMultiplexer>() is { } redis)
            {
                _healthMonitoringService.RegisterHealthCheck(
                    "Redis",
                    PredefinedHealthChecks.CreateRedisHealthCheck(redis, _logger),
                    new HealthCheckOptions
                    {
                        CheckInterval = TimeSpan.FromMinutes(1),
                        Timeout = TimeSpan.FromSeconds(10),
                        EnableAlerting = true,
                        AlertPriority = AlertPriority.Medium,
                        Tags = { "cache", "storage" }
                    });
            }

            // Register Discord health check
            var discordToken = Environment.GetEnvironmentVariable("DISCORD_BOT_TOKEN");
            if (!string.IsNullOrEmpty(discordToken))
            {
                var httpClient = _serviceProvider.GetRequiredService<IHttpClientFactory>().CreateClient();
                _healthMonitoringService.RegisterHealthCheck(
                    "Discord",
                    PredefinedHealthChecks.CreateDiscordHealthCheck(httpClient, discordToken, _logger),
                    new HealthCheckOptions
                    {
                        CheckInterval = TimeSpan.FromMinutes(5),
                        Timeout = TimeSpan.FromSeconds(15),
                        EnableAlerting = false, // Non-critical
                        AlertPriority = AlertPriority.Low,
                        Tags = { "notifications", "optional" }
                    });
            }

            // Register network health check
            _healthMonitoringService.RegisterHealthCheck(
                "Network",
                PredefinedHealthChecks.CreateNetworkHealthCheck("8.8.8.8", _logger),
                new HealthCheckOptions
                {
                    CheckInterval = TimeSpan.FromMinutes(2),
                    Timeout = TimeSpan.FromSeconds(10),
                    EnableAlerting = true,
                    AlertPriority = AlertPriority.High,
                    Tags = { "network", "infrastructure" }
                });

            // Register system resources health check
            _healthMonitoringService.RegisterHealthCheck(
                "SystemResources",
                PredefinedHealthChecks.CreateSystemResourcesHealthCheck(_logger),
                new HealthCheckOptions
                {
                    CheckInterval = TimeSpan.FromMinutes(1),
                    Timeout = TimeSpan.FromSeconds(5),
                    EnableAlerting = true,
                    AlertPriority = AlertPriority.Medium,
                    Tags = { "system", "performance" }
                });

            _logger.LogInformation("Successfully registered all predefined health checks");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to register some health checks");
        }
    }
}

/// <summary>
/// Trading-specific fallback strategies
/// </summary>
public interface ITradingFallbackStrategies
{
    /// <summary>
    /// Register all trading fallback strategies
    /// </summary>
    void RegisterFallbackStrategies(IRecoveryService recoveryService);
}

/// <summary>
/// Implementation of trading fallback strategies
/// </summary>
public sealed class TradingFallbackStrategies : ITradingFallbackStrategies
{
    private readonly ILogger<TradingFallbackStrategies> _logger;

    public TradingFallbackStrategies(ILogger<TradingFallbackStrategies> logger)
    {
        _logger = logger;
    }

    public void RegisterFallbackStrategies(IRecoveryService recoveryService)
    {
        // Register fallback for signal generation
        recoveryService.RegisterFallback<IEnumerable<TradingSignal>>("GenerateSignals", async (ex) =>
        {
            _logger.LogWarning("Using fallback signal generation: returning SPY only");
            // Return a conservative SPY signal as fallback
            return new[] { new TradingSignal("SPY", 400m, 5m, 0.1m) };
        });

        // Register fallback for risk calculation
        recoveryService.RegisterFallback<decimal>("CalculateQuantity", async (ex) =>
        {
            _logger.LogWarning("Using fallback risk calculation: returning conservative quantity");
            return 1m; // Conservative single share
        });

        // Register fallback for portfolio gate
        recoveryService.RegisterFallback<bool>("ShouldTrade", async (ex) =>
        {
            _logger.LogWarning("Using fallback portfolio gate: blocking trading for safety");
            return false; // Conservative: don't trade if uncertain
        });

        _logger.LogInformation("Registered trading fallback strategies");
    }
}
