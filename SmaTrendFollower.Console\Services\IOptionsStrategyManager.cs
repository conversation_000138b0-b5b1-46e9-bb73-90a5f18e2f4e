using Alpaca.Markets;

namespace SmaTrendFollower.Services;

/// <summary>
/// Interface for managing options overlay strategies
/// </summary>
public interface IOptionsStrategyManager
{
    /// <summary>
    /// Evaluates and executes protective put overlay strategy
    /// </summary>
    Task<ProtectivePutResult> EvaluateProtectivePutAsync(string symbol, decimal portfolioValue, decimal currentPrice);

    /// <summary>
    /// Evaluates and executes covered call income strategy
    /// </summary>
    Task<CoveredCallResult> EvaluateCoveredCallAsync(string symbol, decimal sharesOwned, decimal currentPrice);

    /// <summary>
    /// Manages delta-efficient long exposure using deep ITM calls
    /// </summary>
    Task<DeltaEfficientResult> EvaluateDeltaEfficientExposureAsync(string symbol, decimal targetExposure, decimal currentPrice);

    /// <summary>
    /// Monitors and manages existing options positions
    /// </summary>
    Task ManageExistingOptionsAsync();

    /// <summary>
    /// Checks for auto-exercise risk and manages positions before expiration
    /// </summary>
    Task ManageExpirationRiskAsync();
}

/// <summary>
/// Interface for VIX-based volatility analysis and position sizing
/// </summary>
public interface IVolatilityManager
{
    /// <summary>
    /// Gets current VIX regime and position size adjustment
    /// </summary>
    Task<VolatilityRegime> GetCurrentRegimeAsync();

    /// <summary>
    /// Calculates position size adjustment based on VIX levels
    /// </summary>
    Task<decimal> GetVixPositionAdjustmentAsync();

    /// <summary>
    /// Detects VIX spikes for defensive positioning
    /// </summary>
    Task<bool> IsVixSpikeDetectedAsync(decimal threshold = 25.0m);

    /// <summary>
    /// Gets IV-adjusted stop loss levels
    /// </summary>
    Task<decimal> GetIvAdjustedStopAsync(string symbol, decimal entryPrice, decimal baseAtr);
}

/// <summary>
/// Enhanced interface for Discord notifications with daily reports and health monitoring
/// </summary>
public interface IDiscordNotificationService
{
    /// <summary>
    /// Sends trade execution notification
    /// </summary>
    Task SendTradeNotificationAsync(string symbol, string action, decimal quantity, decimal price, decimal pnl);

    /// <summary>
    /// Sends portfolio P&L snapshot
    /// </summary>
    Task SendPortfolioSnapshotAsync(decimal totalEquity, decimal dayPnl, decimal totalPnl, int positionCount);

    /// <summary>
    /// Sends VIX spike alert
    /// </summary>
    Task SendVixSpikeAlertAsync(decimal currentVix, decimal threshold, string action);

    /// <summary>
    /// Sends options strategy notification
    /// </summary>
    Task SendOptionsNotificationAsync(string strategy, string symbol, string details);

    /// <summary>
    /// Sends comprehensive daily trading report
    /// </summary>
    Task SendDailyReportAsync(DailyTradingReport report);

    /// <summary>
    /// Sends system health summary
    /// </summary>
    Task SendHealthSummaryAsync(string healthStatus, List<string> warnings);
}

// === Result Types ===

/// <summary>
/// Result of protective put evaluation
/// </summary>
public readonly record struct ProtectivePutResult(
    bool ShouldExecute,
    string? OptionSymbol,
    decimal Strike,
    DateTime Expiration,
    decimal Premium,
    decimal ProtectionLevel,
    string Reason
);

/// <summary>
/// Result of covered call evaluation
/// </summary>
public readonly record struct CoveredCallResult(
    bool ShouldExecute,
    string? OptionSymbol,
    decimal Strike,
    DateTime Expiration,
    decimal Premium,
    decimal AnnualizedYield,
    decimal AssignmentRisk,
    string Reason
);

/// <summary>
/// Result of delta-efficient exposure evaluation
/// </summary>
public readonly record struct DeltaEfficientResult(
    bool ShouldExecute,
    string? LongCallSymbol,
    string? ShortCallSymbol,
    decimal LongStrike,
    decimal ShortStrike,
    DateTime Expiration,
    decimal NetPremium,
    decimal EffectiveDelta,
    decimal CapitalEfficiency,
    string Reason
);

/// <summary>
/// Volatility regime information
/// </summary>
public readonly record struct VolatilityRegime(
    decimal CurrentVix,
    decimal VixSma30,
    bool IsHighVol,
    bool IsVixSpike,
    decimal PositionSizeMultiplier,
    string RegimeName
);
