using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using System.Collections.Concurrent;
using System.Diagnostics;

namespace SmaTrendFollower.Services.ErrorHandling;

/// <summary>
/// Circuit breaker service implementation with comprehensive state management
/// </summary>
public sealed class CircuitBreakerService : ICircuitBreakerService, IDisposable
{
    private readonly ILogger<CircuitBreakerService> _logger;
    private readonly CircuitBreakerOptions _options;
    private readonly ConcurrentDictionary<string, CircuitBreakerContext> _circuits;
    private readonly Timer _healthCheckTimer;
    private readonly SemaphoreSlim _stateLock = new(1, 1);

    public event EventHandler<CircuitBreakerEventArgs>? StateChanged;

    public CircuitBreakerService(ILogger<CircuitBreakerService> logger, IOptions<CircuitBreakerOptions> options)
    {
        _logger = logger;
        _options = options.Value;
        _circuits = new ConcurrentDictionary<string, CircuitBreakerContext>();
        
        // Start health check timer
        _healthCheckTimer = new Timer(PerformHealthChecks, null, TimeSpan.FromMinutes(1), TimeSpan.FromMinutes(1));
    }

    public async Task<T> ExecuteAsync<T>(
        string serviceName,
        Func<Task<T>> operation,
        Func<Task<T>>? fallback = null,
        CancellationToken cancellationToken = default)
    {
        var context = GetOrCreateContext(serviceName);
        var config = GetServiceConfig(serviceName);

        // Check if circuit is open
        if (await IsCircuitOpenAsync(context, config))
        {
            _logger.LogWarning("Circuit breaker is open for service {ServiceName}", serviceName);
            
            if (fallback != null)
            {
                _logger.LogInformation("Executing fallback for service {ServiceName}", serviceName);
                return await fallback();
            }
            
            throw new ExternalApiException(
                $"Circuit breaker is open for service {serviceName}",
                serviceName,
                isRetriable: false);
        }

        var stopwatch = Stopwatch.StartNew();
        try
        {
            var result = await operation();
            stopwatch.Stop();
            
            await RecordSuccessAsync(context, stopwatch.Elapsed);
            return result;
        }
        catch (Exception ex)
        {
            stopwatch.Stop();
            await RecordFailureAsync(context, ex, stopwatch.Elapsed);
            throw;
        }
    }

    public async Task ExecuteAsync(
        string serviceName,
        Func<Task> operation,
        Func<Task>? fallback = null,
        CancellationToken cancellationToken = default)
    {
        await ExecuteAsync(serviceName, async () =>
        {
            await operation();
            return true; // Dummy return value
        }, fallback == null ? null : async () =>
        {
            await fallback();
            return true;
        }, cancellationToken);
    }

    public bool IsCircuitOpen(string serviceName)
    {
        var context = GetOrCreateContext(serviceName);
        return context.State == CircuitBreakerState.Open;
    }

    public CircuitBreakerInfo GetCircuitState(string serviceName)
    {
        var context = GetOrCreateContext(serviceName);
        return CreateCircuitInfo(serviceName, context);
    }

    public void OpenCircuit(string serviceName, string reason)
    {
        var context = GetOrCreateContext(serviceName);
        TransitionToOpen(context, reason);
    }

    public void CloseCircuit(string serviceName)
    {
        var context = GetOrCreateContext(serviceName);
        TransitionToClosed(context, "Manual close");
    }

    public void ResetCircuit(string serviceName)
    {
        var context = GetOrCreateContext(serviceName);
        lock (context.Lock)
        {
            context.FailureCount = 0;
            context.SuccessCount = 0;
            context.LastFailureTime = null;
            context.LastSuccessTime = null;
            context.OpenedAt = null;
            context.NextRetryAt = null;
            context.LastError = null;
            context.ResponseTimes.Clear();
            context.TotalRequests = 0;
        }
        
        TransitionToClosed(context, "Manual reset");
    }

    public Dictionary<string, CircuitBreakerInfo> GetAllCircuitStates()
    {
        return _circuits.ToDictionary(
            kvp => kvp.Key,
            kvp => CreateCircuitInfo(kvp.Key, kvp.Value));
    }

    private CircuitBreakerContext GetOrCreateContext(string serviceName)
    {
        return _circuits.GetOrAdd(serviceName, _ => new CircuitBreakerContext
        {
            ServiceName = serviceName,
            State = CircuitBreakerState.Closed,
            Lock = new object(),
            ResponseTimes = new Queue<TimeSpan>()
        });
    }

    private CircuitBreakerServiceConfig GetServiceConfig(string serviceName)
    {
        return _options.ServiceConfigs.GetValueOrDefault(serviceName, new CircuitBreakerServiceConfig());
    }

    private async Task<bool> IsCircuitOpenAsync(CircuitBreakerContext context, CircuitBreakerServiceConfig config)
    {
        await _stateLock.WaitAsync();
        try
        {
            lock (context.Lock)
            {
                var now = DateTime.UtcNow;

                switch (context.State)
                {
                    case CircuitBreakerState.Closed:
                        return false;

                    case CircuitBreakerState.Open:
                        var openTimeout = config.OpenTimeout ?? _options.OpenTimeout;
                        if (context.OpenedAt.HasValue && now - context.OpenedAt.Value >= openTimeout)
                        {
                            TransitionToHalfOpen(context);
                            return false;
                        }
                        return true;

                    case CircuitBreakerState.HalfOpen:
                        var maxConcurrentTests = config.MaxConcurrentTests ?? _options.MaxConcurrentTests;
                        return context.TestRequestCount >= maxConcurrentTests;

                    default:
                        return false;
                }
            }
        }
        finally
        {
            _stateLock.Release();
        }
    }

    private async Task RecordSuccessAsync(CircuitBreakerContext context, TimeSpan responseTime)
    {
        await _stateLock.WaitAsync();
        try
        {
            lock (context.Lock)
            {
                context.SuccessCount++;
                context.TotalRequests++;
                context.LastSuccessTime = DateTime.UtcNow;
                context.ResponseTimes.Enqueue(responseTime);

                // Keep only recent response times (last 100)
                while (context.ResponseTimes.Count > 100)
                {
                    context.ResponseTimes.Dequeue();
                }

                if (context.State == CircuitBreakerState.HalfOpen)
                {
                    context.TestRequestCount--;
                    var successThreshold = GetServiceConfig(context.ServiceName).SuccessThreshold ?? _options.SuccessThreshold;
                    
                    if (context.SuccessCount >= successThreshold)
                    {
                        TransitionToClosed(context, "Success threshold reached");
                    }
                }
            }
        }
        finally
        {
            _stateLock.Release();
        }
    }

    private async Task RecordFailureAsync(CircuitBreakerContext context, Exception exception, TimeSpan responseTime)
    {
        await _stateLock.WaitAsync();
        try
        {
            lock (context.Lock)
            {
                context.FailureCount++;
                context.TotalRequests++;
                context.LastFailureTime = DateTime.UtcNow;
                context.LastError = exception.Message;

                if (context.State == CircuitBreakerState.HalfOpen)
                {
                    context.TestRequestCount--;
                    TransitionToOpen(context, $"Test request failed: {exception.Message}");
                }
                else if (context.State == CircuitBreakerState.Closed)
                {
                    var failureThreshold = GetServiceConfig(context.ServiceName).FailureThreshold ?? _options.FailureThreshold;
                    
                    if (context.FailureCount >= failureThreshold)
                    {
                        TransitionToOpen(context, $"Failure threshold reached: {context.FailureCount} failures");
                    }
                }
            }
        }
        finally
        {
            _stateLock.Release();
        }
    }

    private void TransitionToOpen(CircuitBreakerContext context, string reason)
    {
        var previousState = context.State;
        context.State = CircuitBreakerState.Open;
        context.OpenedAt = DateTime.UtcNow;
        context.NextRetryAt = DateTime.UtcNow.Add(GetServiceConfig(context.ServiceName).OpenTimeout ?? _options.OpenTimeout);

        _logger.LogWarning("Circuit breaker opened for service {ServiceName}: {Reason}", context.ServiceName, reason);
        
        OnStateChanged(new CircuitBreakerEventArgs
        {
            ServiceName = context.ServiceName,
            PreviousState = previousState,
            NewState = CircuitBreakerState.Open,
            Reason = reason
        });
    }

    private void TransitionToHalfOpen(CircuitBreakerContext context)
    {
        var previousState = context.State;
        context.State = CircuitBreakerState.HalfOpen;
        context.SuccessCount = 0;
        context.TestRequestCount = 0;

        _logger.LogInformation("Circuit breaker transitioned to half-open for service {ServiceName}", context.ServiceName);
        
        OnStateChanged(new CircuitBreakerEventArgs
        {
            ServiceName = context.ServiceName,
            PreviousState = previousState,
            NewState = CircuitBreakerState.HalfOpen,
            Reason = "Timeout expired, allowing test requests"
        });
    }

    private void TransitionToClosed(CircuitBreakerContext context, string reason)
    {
        var previousState = context.State;
        context.State = CircuitBreakerState.Closed;
        context.FailureCount = 0;
        context.OpenedAt = null;
        context.NextRetryAt = null;

        _logger.LogInformation("Circuit breaker closed for service {ServiceName}: {Reason}", context.ServiceName, reason);
        
        OnStateChanged(new CircuitBreakerEventArgs
        {
            ServiceName = context.ServiceName,
            PreviousState = previousState,
            NewState = CircuitBreakerState.Closed,
            Reason = reason
        });
    }

    private CircuitBreakerInfo CreateCircuitInfo(string serviceName, CircuitBreakerContext context)
    {
        lock (context.Lock)
        {
            var successRate = context.TotalRequests > 0 
                ? (double)context.SuccessCount / context.TotalRequests 
                : 1.0;

            var averageResponseTime = context.ResponseTimes.Count > 0
                ? TimeSpan.FromMilliseconds(context.ResponseTimes.Average(t => t.TotalMilliseconds))
                : (TimeSpan?)null;

            return new CircuitBreakerInfo
            {
                ServiceName = serviceName,
                State = context.State,
                FailureCount = context.FailureCount,
                SuccessCount = context.SuccessCount,
                LastFailureTime = context.LastFailureTime ?? DateTime.MinValue,
                LastSuccessTime = context.LastSuccessTime ?? DateTime.MinValue,
                OpenedAt = context.OpenedAt,
                NextRetryAt = context.NextRetryAt,
                LastError = context.LastError,
                AverageResponseTime = averageResponseTime,
                SuccessRate = successRate,
                TotalRequests = context.TotalRequests
            };
        }
    }

    private void PerformHealthChecks(object? state)
    {
        foreach (var circuit in _circuits.Values)
        {
            var config = GetServiceConfig(circuit.ServiceName);
            if (!(config.EnableAutoRecovery ?? _options.EnableAutoRecovery))
                continue;

            lock (circuit.Lock)
            {
                if (circuit.State == CircuitBreakerState.Open && 
                    circuit.NextRetryAt.HasValue && 
                    DateTime.UtcNow >= circuit.NextRetryAt.Value)
                {
                    TransitionToHalfOpen(circuit);
                }
            }
        }
    }

    private void OnStateChanged(CircuitBreakerEventArgs args)
    {
        StateChanged?.Invoke(this, args);
    }

    public void Dispose()
    {
        _healthCheckTimer?.Dispose();
        _stateLock?.Dispose();
    }
}

/// <summary>
/// Internal circuit breaker context
/// </summary>
internal sealed class CircuitBreakerContext
{
    public string ServiceName { get; set; } = string.Empty;
    public CircuitBreakerState State { get; set; }
    public int FailureCount { get; set; }
    public int SuccessCount { get; set; }
    public int TestRequestCount { get; set; }
    public DateTime? LastFailureTime { get; set; }
    public DateTime? LastSuccessTime { get; set; }
    public DateTime? OpenedAt { get; set; }
    public DateTime? NextRetryAt { get; set; }
    public string? LastError { get; set; }
    public Queue<TimeSpan> ResponseTimes { get; set; } = new();
    public int TotalRequests { get; set; }
    public object Lock { get; set; } = new();
}
