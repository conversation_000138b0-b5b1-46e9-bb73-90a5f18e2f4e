# Options Strategies Implementation Guide

## Overview

SmaTrendFollower includes sophisticated options overlay strategies designed to enhance returns and provide portfolio protection. This guide covers the implementation, configuration, and usage of all options strategies.

## 🎯 Strategy Overview

### Supported Strategies

1. **Protective Puts** - Portfolio downside protection
2. **Covered Calls** - Income generation on long positions  
3. **Delta-Efficient Exposure** - Capital-efficient long exposure using deep ITM calls

## 🛡️ Protective Puts Strategy

### Purpose
Provides portfolio-level downside protection during volatile market conditions or when portfolio drawdown exceeds acceptable levels.

### Trigger Conditions
```csharp
// Automatic triggers for protective put evaluation
bool shouldTrigger = portfolioPnlPercent <= -0.03m ||  // Portfolio down 3%
                   vixRegime.CurrentVix > vixRegime.VixSma30 ||  // VIX above 30-day SMA
                   symbol == "SPY";  // Always consider for SPY positions
```

### Implementation Details

#### Strike Selection
- **Target**: At-the-money (ATM) puts for maximum protection
- **Tolerance**: Within 2% of current price
- **Expiration**: 30-45 days to expiration for optimal time decay balance

#### Cost Management
- **Maximum Cost**: 2% of portfolio value
- **Cost-Benefit Analysis**: Protection level vs premium cost
- **Dynamic Sizing**: Scales with portfolio value ($100k blocks)

#### Example Configuration
```bash
# Environment variables for protective puts
ENABLE_PROTECTIVE_PUTS=true
MAX_PROTECTION_COST_PERCENT=0.02
PROTECTION_TRIGGER_DRAWDOWN=0.03
MIN_PROTECTION_LEVEL=0.05
```

### Usage Example
```csharp
var protectiveResult = await optionsManager.EvaluateProtectivePutAsync(
    symbol: "SPY",
    portfolioValue: 250000m,
    currentPrice: 450.00m
);

if (protectiveResult.ShouldExecute)
{
    // Execute protective put order
    var putOrder = new NewOrderRequest(
        symbol: protectiveResult.OptionSymbol,
        quantity: CalculateContractsNeeded(portfolioValue),
        side: OrderSide.Buy,
        type: OrderType.Limit,
        timeInForce: TimeInForce.Day,
        limitPrice: protectiveResult.Premium * 1.02m  // 2% slippage buffer
    );
}
```

## 💰 Covered Calls Strategy

### Purpose
Generate additional income on existing long equity positions by selling call options against holdings.

### Selection Criteria
```csharp
// Covered call opportunity evaluation
var callOptions = await marketDataService.GetCoveredCallOptionsAsync(symbol, currentPrice, 7);
var optimalCall = callOptions
    .Where(c => c.Strike > currentPrice * 1.02m)  // At least 2% OTM
    .Where(c => c.Delta <= 0.30m)  // Low assignment risk
    .OrderByDescending(c => c.LastPrice / c.Strike)  // Best yield
    .FirstOrDefault();
```

### Risk Management
- **Strike Selection**: 2-5% out-of-the-money (OTM)
- **Delta Limit**: Maximum 0.30 delta to reduce assignment risk
- **Expiration**: 7-14 days for optimal time decay
- **Assignment Risk**: Monitored and managed automatically

### Income Targets
- **Minimum Premium**: 0.5% of underlying value
- **Annualized Target**: 12-24% additional income
- **Risk-Adjusted**: Premium per unit of assignment risk

## ⚡ Delta-Efficient Exposure Strategy

### Purpose
Achieve long equity exposure using deep in-the-money (ITM) call options for capital efficiency and leverage control.

### Implementation Strategy
```csharp
// Delta-efficient exposure evaluation
var deepItmCalls = await marketDataService.GetOptionsDataAsync(symbol)
    .Where(c => c.OptionType == "call")
    .Where(c => c.Strike <= currentPrice * 0.85m)  // Deep ITM
    .Where(c => c.Delta >= 0.80m)  // High delta
    .Where(c => c.ExpirationDate >= DateTime.UtcNow.AddDays(60))  // Sufficient time
    .OrderBy(c => c.Strike)  // Deepest ITM first
    .FirstOrDefault();
```

### Capital Efficiency Benefits
- **Reduced Capital**: ~20-30% of stock purchase cost
- **Leverage Control**: Defined maximum loss (premium paid)
- **Time Decay Management**: Long-dated options minimize theta impact
- **Dividend Risk**: Managed through expiration selection

## 🔧 Configuration and Setup

### Required Environment Variables
```bash
# Options strategy configuration
ENABLE_OPTIONS_OVERLAY=true
ENABLE_PROTECTIVE_PUTS=true
ENABLE_COVERED_CALLS=true
ENABLE_DELTA_EFFICIENT=true

# Risk management
MAX_OPTIONS_ALLOCATION_PERCENT=0.20
MIN_OPTION_LIQUIDITY_VOLUME=100
MAX_BID_ASK_SPREAD_PERCENT=0.05

# Polygon options data
POLY_API_KEY=your_polygon_api_key
POLYGON_OPTIONS_ENABLED=true
```

### Service Registration
```csharp
// In Program.cs
services.AddScoped<IOptionsStrategyManager, OptionsStrategyManager>();
services.AddScoped<IVolatilityManager, VolatilityManager>();
```

## 📊 Performance Monitoring

### Key Metrics Tracked
- **Options P&L**: Individual and aggregate options performance
- **Assignment Events**: Covered call assignments and management
- **Protection Effectiveness**: Protective put performance during drawdowns
- **Capital Efficiency**: Delta-efficient exposure vs direct equity

### Reporting and Alerts
```csharp
// Discord notifications for options activities
await discordService.SendOptionsNotificationAsync(
    strategy: "ProtectivePut",
    symbol: "SPY",
    details: $"Purchased {contracts} contracts at ${premium:F2} for ${totalCost:N0} protection"
);
```

## ⚠️ Risk Considerations

### Assignment Risk (Covered Calls)
- **Monitoring**: Real-time delta and gamma tracking
- **Management**: Roll or close positions approaching assignment
- **Tax Implications**: Consider holding period for tax efficiency

### Liquidity Risk
- **Minimum Volume**: 100+ contracts daily volume
- **Bid-Ask Spreads**: Maximum 5% spread for execution
- **Market Hours**: Options trading limited to market hours

### Time Decay (Theta)
- **Long Options**: Protective puts and delta-efficient calls affected by time decay
- **Short Options**: Covered calls benefit from time decay
- **Management**: Regular evaluation and adjustment

## 🎯 Best Practices

### Position Sizing
- **Portfolio Allocation**: Maximum 20% of portfolio in options strategies
- **Risk Budgeting**: Separate risk budget for each strategy type
- **Correlation Management**: Avoid over-concentration in single underlying

### Execution Timing
- **Market Conditions**: Execute during normal market hours for best liquidity
- **Volatility Timing**: Consider implied volatility levels for entry/exit
- **Earnings Avoidance**: Avoid options expiring around earnings announcements

### Monitoring and Adjustment
- **Daily Review**: Monitor all options positions for assignment risk
- **Weekly Adjustment**: Evaluate roll opportunities for expiring options
- **Monthly Analysis**: Review strategy performance and allocation

This comprehensive guide provides all necessary information for implementing and managing options strategies within the SmaTrendFollower system.
