<?xml version="1.0" encoding="utf-8"?>
<RunSettings>
  <RunConfiguration>
    <!-- Reduced timeout for faster feedback -->
    <TestSessionTimeout>300000</TestSessionTimeout>
    <!-- Use all available cores for parallel execution -->
    <MaxCpuCount>0</MaxCpuCount>
    <!-- Disable code coverage for faster execution -->
    <CollectSourceInformation>false</CollectSourceInformation>
    <!-- Disable app domain for faster startup -->
    <DisableAppDomain>true</DisableAppDomain>
    <!-- Enable parallel execution -->
    <DisableParallelization>false</DisableParallelization>
  </RunConfiguration>

  <TestRunParameters>
    <!-- Test environment parameters optimized for speed -->
    <Parameter name="Environment" value="Test" />
    <Parameter name="EnableIntegrationTests" value="false" />
    <Parameter name="UseRealApis" value="false" />
    <Parameter name="MockExternalDependencies" value="true" />
    <Parameter name="LogLevel" value="Error" />
  </TestRunParameters>

  <!-- Data collectors disabled for faster execution -->
  <DataCollectionRunSettings>
    <DataCollectors>
      <DataCollector friendlyName="XPlat code coverage" enabled="false" />
      <DataCollector friendlyName="Code Coverage" enabled="false" />
    </DataCollectors>
  </DataCollectionRunSettings>

  <!-- Optimized test execution settings -->
  <MSTest>
    <Parallel>
      <Workers>0</Workers>
      <Scope>method</Scope>
    </Parallel>
  </MSTest>

  <!-- xUnit settings for optimal performance -->
  <xUnit>
    <MaxParallelThreads>0</MaxParallelThreads>
    <ParallelizeTestCollections>true</ParallelizeTestCollections>
    <ParallelizeAssembly>false</ParallelizeAssembly>
    <PreEnumerateTheories>false</PreEnumerateTheories>
    <ShadowCopy>false</ShadowCopy>
  </xUnit>

  <!-- Minimal logger configuration for speed -->
  <LoggerRunSettings>
    <Loggers>
      <Logger friendlyName="console" enabled="True">
        <Configuration>
          <Verbosity>minimal</Verbosity>
        </Configuration>
      </Logger>
    </Loggers>
  </LoggerRunSettings>
</RunSettings>
