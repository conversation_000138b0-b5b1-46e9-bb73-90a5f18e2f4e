# SmaTrendFollower Trading Strategy Documentation

## Strategy Overview

SmaTrendFollower implements a systematic SMA-following momentum strategy designed to capture trending moves in equity markets while maintaining strict risk controls. The strategy combines technical analysis, universe screening, and dynamic risk management to generate consistent returns.

## Core Strategy Principles

### 1. Trend Following Philosophy
- **Primary Trend**: Only trade when broad market (SPY) is above its 200-day SMA
- **Individual Trends**: Target stocks above both 50-day and 200-day SMAs
- **Momentum Capture**: Focus on stocks with strong 6-month performance
- **Volatility Filter**: Avoid whipsaw markets with ATR-based filtering

### 2. Risk-First Approach
- **Position Sizing**: ATR-based position sizing for consistent risk per trade
- **Stop Losses**: 2×ATR trailing stops for capital preservation
- **Portfolio Risk**: Maximum 1% of equity per position, capped at $1000
- **Market Regime**: Skip trading during unfavorable market conditions

## Signal Generation Process

### Universe Selection
```
Initial Universe: SPY + Top 500 Tickers
    ↓
Price Filter: Close > $10
    ↓
Volume Filter: Average Volume > 1M shares
    ↓
Volatility Filter: Daily StdDev > 2%
    ↓
Technical Filter: Close > SMA50 && Close > SMA200
    ↓
Volatility Throttle: ATR14/Close < 3%
    ↓
Ranking: 6-Month Total Return (Descending)
    ↓
Selection: Top N Symbols (Default: 10)
```

### Technical Analysis Components

#### Simple Moving Averages (SMA)
- **SMA50**: 50-day simple moving average for intermediate trend
- **SMA200**: 200-day simple moving average for primary trend
- **Requirement**: Close > SMA50 AND Close > SMA200

#### Average True Range (ATR)
- **ATR14**: 14-day Average True Range for volatility measurement
- **Position Sizing**: Risk capital / (ATR14 × Price)
- **Stop Loss**: Entry price - (2 × ATR14)
- **Volatility Filter**: ATR14/Close < 3% to avoid whipsaw conditions

#### Performance Ranking
- **6-Month Return**: (Current Price / Price 126 days ago) - 1
- **Ranking Logic**: Descending order to prioritize momentum leaders
- **Concentration**: Focus capital on strongest performers

### Market Regime Detection

#### SPY Market Gate
```csharp
// Portfolio Gate Logic
bool shouldTrade = spyClose > spySma200;

// Market Regime Analysis (Enhanced)
MarketRegime regime = AnalyzeMarketRegime(spyBars);
if (regime == MarketRegime.Volatile || regime == MarketRegime.TrendingDown)
{
    skipTrading = true;
}
```

#### Regime Classifications
- **TrendingUp**: SMA slope > 0, low volatility, positive return/drawdown ratio
- **TrendingDown**: SMA slope < 0, declining market conditions
- **Sideways**: Flat SMA slope, range-bound conditions
- **Volatile**: High ATR relative to price, unstable conditions

## Risk Management Framework

### Position Sizing Algorithm
```csharp
// Risk Capital Calculation
decimal accountEquity = await GetAccountEquityAsync();
decimal riskPerTrade = Math.Min(accountEquity * 0.01m, 1000m); // 1% or $1000 max

// ATR-Based Position Sizing
decimal atr14 = CalculateAtr14(bars);
decimal positionSize = riskPerTrade / (atr14 * currentPrice);

// Position Constraints
decimal maxShares = riskPerTrade / currentPrice; // Maximum position limit
positionSize = Math.Min(positionSize, maxShares);
```

### Risk Controls
1. **Per-Trade Risk**: Maximum 1% of account equity per position
2. **Absolute Risk Cap**: $1000 maximum risk per position regardless of account size
3. **Volatility Adjustment**: Position size inversely related to ATR
4. **Concentration Limits**: Maximum position size cannot exceed risk capital / price

### Stop Loss Management
```csharp
// Initial Stop Loss
decimal stopPrice = entryPrice - (2 * atr14);

// Trailing Stop Logic (Daily Updates)
decimal newStopPrice = Math.Max(currentStopPrice, currentPrice - (2 * currentAtr14));

// Stop Loss Order
var stopOrder = new StopOrder
{
    Symbol = symbol,
    Quantity = position.Quantity,
    StopPrice = stopPrice,
    TimeInForce = TimeInForce.GTC
};
```

## Trade Execution Framework

### Entry Execution Pattern
```
1. Cancel Existing Orders
   ↓
2. Calculate Entry Price = LastClose × 1.002 (0.2% premium)
   ↓
3. Submit Limit-on-Open Order
   ↓
4. Calculate Stop Loss = Entry - (2 × ATR14)
   ↓
5. Submit GTC Stop Loss Order
   ↓
6. Log Trade Details
```

### Order Types and Timing
- **Entry Orders**: Limit-on-Open (LOO) at slight premium to last close
- **Stop Orders**: Good-Till-Canceled (GTC) stop-loss orders
- **Execution Timing**: Pre-market order submission for market open execution
- **Order Management**: Cancel and replace existing orders before new submissions

### Trade Lifecycle
1. **Signal Generation**: Daily after market close
2. **Order Submission**: Pre-market (6:00 AM - 9:30 AM ET)
3. **Execution Monitoring**: Market open execution tracking
4. **Stop Management**: Daily stop-loss updates based on trailing ATR
5. **Position Exit**: Stop-loss trigger or signal reversal

## Performance Optimization

### Cache Warming Strategy
```
Pre-Market Sequence (6:00 AM):
1. Load trailing stop levels from SQLite → Redis
2. Initialize daily signal flags
3. Warm universe cache with filtered symbols
4. Pre-calculate technical indicators
5. Validate API connectivity
```

### Execution Efficiency
- **Batch API Calls**: Multi-symbol data requests
- **Parallel Processing**: Concurrent signal generation
- **Connection Pooling**: Reuse HTTP connections
- **Data Caching**: SQLite + Redis hybrid caching

## Strategy Parameters

### Default Configuration
```yaml
Universe:
  MaxSymbols: 10
  MinPrice: 10.00
  MinVolume: 1000000
  MinVolatility: 0.02

Technical:
  SMA50Period: 50
  SMA200Period: 200
  ATRPeriod: 14
  VolatilityThreshold: 0.03
  RankingPeriod: 126  # 6 months

Risk:
  MaxRiskPerTrade: 0.01  # 1%
  MaxRiskDollars: 1000
  StopLossMultiplier: 2.0
  EntryPremium: 1.002

Execution:
  OrderType: LimitOnOpen
  StopType: GoodTillCanceled
  MaxOrderAge: 1  # Day
```

### Customization Options
- **Universe Size**: Adjust maxSymbols for concentration/diversification
- **Risk Parameters**: Modify risk percentage and dollar limits
- **Technical Periods**: Customize SMA and ATR calculation periods
- **Volatility Filters**: Adjust ATR/Price thresholds for market conditions

## Backtesting and Validation

### Historical Performance Metrics
- **Total Return**: Cumulative strategy performance
- **Sharpe Ratio**: Risk-adjusted returns
- **Maximum Drawdown**: Worst peak-to-trough decline
- **Win Rate**: Percentage of profitable trades
- **Average Trade**: Mean profit/loss per trade

### Validation Framework
```csharp
// Synthetic Data Testing
var testBars = GenerateSyntheticBars(symbol, startDate, endDate);
var signals = await signalGenerator.RunAsync(testBars);

// Performance Validation
var backtest = new BacktestEngine();
var results = await backtest.RunAsync(signals, testBars);

// Metrics Calculation
var metrics = CalculatePerformanceMetrics(results);
```

### Risk Validation
- **Stress Testing**: Performance during market crashes
- **Regime Analysis**: Strategy performance across different market conditions
- **Parameter Sensitivity**: Impact of parameter changes on performance
- **Monte Carlo**: Statistical validation of strategy robustness

## Market Conditions and Adaptations

### Bull Market Behavior
- **High Signal Count**: More stocks meet SMA criteria
- **Extended Trends**: Longer holding periods with trailing stops
- **Momentum Persistence**: Strong 6-month return rankings

### Bear Market Behavior
- **Reduced Activity**: SPY SMA200 gate blocks most trading
- **Quick Exits**: 2×ATR stops trigger faster in volatile conditions
- **Capital Preservation**: Focus shifts to risk management

### Sideways Market Behavior
- **Selective Trading**: Fewer qualifying signals
- **Shorter Holds**: More frequent stop-loss triggers
- **Volatility Management**: ATR filters reduce whipsaw trades

## Integration with Enhanced Features

### Options Overlay (Optional)
- **Covered Calls**: Generate income on long positions
- **Protective Puts**: Additional downside protection
- **VIX Hedging**: Portfolio-level volatility hedging

### Dynamic Universe (Optional)
- **Real-time Screening**: Intraday universe updates
- **Sector Rotation**: Adapt universe based on sector performance
- **Market Cap Filters**: Focus on specific market segments

### Regime-Based Adjustments (Optional)
- **Volatility Scaling**: Adjust position sizes based on VIX levels
- **Trend Strength**: Modify stop distances based on trend strength
- **Correlation Filters**: Avoid highly correlated positions

## Implementation Status

### Core Strategy (Production Ready) ✅
- **Signal Generation**: Fully implemented with comprehensive universe screening
- **Risk Management**: Complete ATR-based position sizing with dynamic risk adjustment
- **Trade Execution**: Limit-on-Open with 2×ATR stop-loss implementation
- **Portfolio Gate**: SPY SMA200 trend filter operational
- **Stop Management**: Trailing stop-loss system with daily updates

### Enhanced Features (Advanced) ⚠️
- **Market Regime Detection**: Implemented but requires careful configuration
- **Dynamic Universe**: Real-time screening available with Redis caching
- **Options Overlay**: Protective puts and covered calls strategies implemented
- **VIX Analysis**: Volatility regime detection and spike monitoring

### Performance Optimizations ✅
- **Cache Warming**: Pre-market Redis cache loading operational
- **Parallel Processing**: Multi-threaded signal generation available
- **Batch Operations**: Efficient multi-symbol API calls implemented
- **Connection Pooling**: HTTP client factory with connection reuse

### Validation and Testing ✅
- **Unit Tests**: 78% overall coverage, 92% for core services
- **Integration Tests**: Complete trading cycle validation
- **Synthetic Data**: Backtesting framework with performance metrics
- **Paper Trading**: Full paper trading mode for safe validation

## Usage Recommendations

### For New Users
Start with the **core strategy implementation** which provides:
- Proven SMA momentum strategy
- Robust risk management
- Comprehensive safety features
- Well-tested execution patterns

### For Advanced Users
Gradually enable **enhanced features** after mastering the core system:
- Market regime detection for improved timing
- Dynamic universe for better symbol selection
- Options overlay for additional income/protection
- Advanced caching for performance optimization

### Configuration Guidelines
- **Conservative**: Use default parameters for initial deployment
- **Aggressive**: Increase universe size and reduce volatility filters
- **Defensive**: Lower risk percentages and tighter stop-losses
- **Performance**: Enable caching and parallel processing features

This comprehensive strategy documentation provides the foundation for understanding, implementing, and optimizing the SmaTrendFollower trading system across all implementation levels.
