# Comprehensive Error Handling Guide

## Overview

The SmaTrendFollower project now includes a comprehensive error handling infrastructure designed to provide resilience, graceful degradation, and automatic recovery for all external dependencies and critical services.

## Architecture

### Core Components

1. **ErrorHandler** - Centralized error handling with categorization and recovery strategies
2. **CircuitBreakerService** - Circuit breaker pattern implementation for external services
3. **EnhancedRetryService** - Advanced retry mechanisms with exponential backoff and jitter
4. **RecoveryService** - Graceful degradation and automatic recovery mechanisms
5. **HealthMonitoringService** - Comprehensive health monitoring for all dependencies

### Error Categories

- **MarketData** - Data retrieval and processing errors
- **TradeExecution** - Order placement and management errors
- **RiskManagement** - Position sizing and risk validation errors
- **ExternalApi** - Third-party API communication errors
- **Configuration** - Setup and configuration errors
- **Network** - Connectivity and communication errors
- **Authentication** - Authorization and credential errors
- **RateLimit** - API throttling and rate limiting errors

### Error Severity Levels

- **Low** - Informational, system continues normally
- **Medium** - Warning, system continues with potential degradation
- **High** - Error requiring immediate attention, system can continue
- **Critical** - System failure requiring immediate intervention

## Usage Examples

### Basic Error Handling

```csharp
// Using the error handler directly
var context = new ErrorContext
{
    OperationName = "GetMarketData",
    ServiceName = "MarketDataService"
};

try
{
    var data = await GetMarketDataAsync();
}
catch (Exception ex)
{
    var result = await errorHandler.HandleErrorAsync(ex, context);
    
    switch (result.Strategy)
    {
        case RecoveryStrategy.Retry:
            // Retry with suggested delay
            await Task.Delay(result.RetryDelay ?? TimeSpan.FromSeconds(1));
            break;
        case RecoveryStrategy.Fallback:
            // Use fallback data source
            data = await GetFallbackDataAsync();
            break;
        case RecoveryStrategy.Skip:
            // Skip this operation
            return;
    }
}
```

### Enhanced Retry Service

```csharp
// Using the enhanced retry service
var result = await retryService.ExecuteAsync(
    async () => await apiClient.GetDataAsync(),
    "GetApiData",
    "ExternalApi");

// With custom retry policy
var customPolicy = new RetryPolicy
{
    MaxAttempts = 5,
    BaseDelay = TimeSpan.FromSeconds(2),
    BackoffStrategy = BackoffStrategy.Exponential,
    EnableCircuitBreaker = true
};

var result = await retryService.ExecuteWithPolicyAsync(
    operation,
    customPolicy,
    "CriticalOperation");
```

### Circuit Breaker Usage

```csharp
// Execute with circuit breaker protection
var result = await circuitBreakerService.ExecuteAsync(
    "AlpacaAPI",
    async () => await alpacaClient.GetAccountAsync(),
    async () => await GetCachedAccountAsync(), // Fallback
    cancellationToken);

// Check circuit breaker status
if (circuitBreakerService.IsCircuitOpen("AlpacaAPI"))
{
    // Handle open circuit scenario
    logger.LogWarning("Alpaca API circuit is open, using cached data");
}
```

### Recovery Service

```csharp
// Execute with automatic recovery
var recoveryOptions = RecoveryOptions.ForApi("GetSignals", "SignalGeneration");
var signals = await recoveryService.ExecuteWithRecoveryAsync(
    async () => await signalGenerator.RunAsync(),
    recoveryOptions);

// Register fallback strategies
recoveryService.RegisterFallback<IEnumerable<TradingSignal>>("GenerateSignals", 
    async (ex) => new[] { new TradingSignal("SPY", 400m, 5m, 0.1m) });
```

### Health Monitoring

```csharp
// Register health checks
healthMonitoringService.RegisterHealthCheck(
    "Alpaca",
    PredefinedHealthChecks.CreateAlpacaHealthCheck(alpacaFactory, logger),
    new HealthCheckOptions
    {
        CheckInterval = TimeSpan.FromMinutes(2),
        Timeout = TimeSpan.FromSeconds(30),
        EnableAlerting = true,
        AlertPriority = AlertPriority.Critical
    });

// Check overall health
var overallHealth = await healthMonitoringService.CheckOverallHealthAsync();
if (overallHealth.Status == HealthStatus.Unhealthy)
{
    logger.LogError("System health is compromised: {UnhealthyCount} services unhealthy", 
        overallHealth.UnhealthyCount);
}
```

## Configuration

### appsettings.json

```json
{
  "ErrorHandling": {
    "MaxRetryAttempts": 3,
    "BaseDelayMs": 1000,
    "MaxDelayMs": 30000,
    "UseJitter": true,
    "EnableCircuitBreaker": true,
    "CircuitBreakerThreshold": 5,
    "CircuitBreakerTimeoutMinutes": 5,
    "EnableNotifications": true,
    "NotificationThreshold": "High"
  },
  "CircuitBreaker": {
    "FailureThreshold": 5,
    "OpenTimeout": "00:01:00",
    "SuccessThreshold": 3,
    "FailureWindow": "00:05:00",
    "EnableAutoRecovery": true,
    "RecoveryInterval": "00:00:30",
    "ServiceConfigs": {
      "Alpaca": {
        "FailureThreshold": 3,
        "OpenTimeout": "00:02:00"
      },
      "Polygon": {
        "FailureThreshold": 5,
        "OpenTimeout": "00:01:00"
      }
    }
  },
  "RetryPolicy": {
    "MaxAttempts": 3,
    "BaseDelay": "00:00:01",
    "MaxDelay": "00:00:30",
    "BackoffStrategy": "Exponential",
    "UseJitter": true,
    "EnableCircuitBreaker": true
  }
}
```

### Service Registration

```csharp
// In Program.cs or Startup.cs
services.AddErrorHandling(configuration);
services.AddPredefinedHealthChecks();
services.AddTradingErrorHandling(configuration);

// Or with custom configuration
services.AddErrorHandling(
    errorHandler => {
        errorHandler.MaxRetryAttempts = 5;
        errorHandler.EnableNotifications = true;
    },
    circuitBreaker => {
        circuitBreaker.FailureThreshold = 3;
        circuitBreaker.OpenTimeout = TimeSpan.FromMinutes(2);
    });
```

## Enhanced Trading Services

The following enhanced services include comprehensive error handling:

### EnhancedSignalGeneratorWithErrorHandling
- Parallel processing with error isolation
- Symbol-level error tracking
- Fallback to SPY-only signals
- Comprehensive validation

### EnhancedRiskManagerWithErrorHandling
- Account data caching with staleness handling
- Input validation and sanity checks
- Fallback risk calculations
- Conservative error handling

### EnhancedPortfolioGateWithErrorHandling
- SPY data validation and quality checks
- Cached results with expiration
- Fallback decision logic
- Data freshness validation

## Best Practices

### 1. Error Context
Always provide rich context when handling errors:

```csharp
var context = new ErrorContext
{
    OperationName = "CalculatePosition",
    ServiceName = "RiskManager"
}.WithProperty("Symbol", symbol)
 .WithProperty("AccountEquity", equity);
```

### 2. Appropriate Recovery Strategies
- **Retry** - For transient network/API errors
- **Fallback** - For data retrieval failures
- **Skip** - For non-critical operations
- **Stop** - For critical safety violations
- **Degrade** - For performance issues

### 3. Circuit Breaker Configuration
- Set appropriate failure thresholds based on service criticality
- Use shorter timeouts for critical services
- Configure service-specific settings for different APIs

### 4. Health Check Design
- Keep health checks lightweight and fast
- Test actual functionality, not just connectivity
- Use appropriate check intervals based on service importance

### 5. Monitoring and Alerting
- Monitor error rates and patterns
- Set up alerts for circuit breaker state changes
- Track recovery success rates
- Monitor health check trends

## Testing

The error handling infrastructure includes comprehensive tests:

- **ErrorHandlerTests** - Core error handling logic
- **CircuitBreakerServiceTests** - Circuit breaker behavior
- **EnhancedRetryServiceTests** - Retry mechanisms
- **HealthMonitoringServiceTests** - Health monitoring

Run tests with:
```bash
dotnet test SmaTrendFollower.Tests/Services/ErrorHandling/
```

## Monitoring Dashboard

The system provides health monitoring data that can be integrated with monitoring dashboards:

```csharp
// Get current health status
var healthStatus = healthMonitoringService.GetCurrentHealthStatus();

// Get circuit breaker states
var circuitStates = circuitBreakerService.GetAllCircuitStates();

// Get retry statistics
var retryStats = retryService.GetStatistics();

// Get degradation status
var degradationStatus = recoveryService.GetDegradationStatus();
```

## Troubleshooting

### Common Issues

1. **High Error Rates**
   - Check external service status
   - Review circuit breaker thresholds
   - Verify network connectivity

2. **Circuit Breakers Opening Frequently**
   - Adjust failure thresholds
   - Increase timeout values
   - Check service health

3. **Slow Recovery**
   - Review recovery intervals
   - Check health check frequency
   - Verify fallback strategies

### Debugging

Enable detailed logging for error handling components:

```json
{
  "Logging": {
    "LogLevel": {
      "SmaTrendFollower.Services.ErrorHandling": "Debug"
    }
  }
}
```

## Future Enhancements

- Integration with external monitoring systems (Prometheus, Grafana)
- Machine learning-based error prediction
- Dynamic threshold adjustment based on historical data
- Advanced correlation analysis for error patterns
- Integration with incident management systems
