using Microsoft.Extensions.Logging;
using <PERSON>;
using Polly.Extensions.Http;
using System.Net;

namespace SmaTrendFollower.Services;

/// <summary>
/// Factory for creating rate limiting and retry policies for API clients
/// Implements exponential back-off strategies for both Alpaca and Polygon APIs
/// </summary>
public sealed class RateLimitPolicyFactory : IRateLimitPolicyFactory
{
    private readonly ILogger<RateLimitPolicyFactory> _logger;

    public RateLimitPolicyFactory(ILogger<RateLimitPolicyFactory> logger)
    {
        _logger = logger;
    }

    /// <summary>
    /// Creates a rate limiting policy for Alpaca API (200 requests/minute)
    /// with exponential back-off retry policy
    /// </summary>
    public IAsyncPolicy<HttpResponseMessage> CreateAlpacaPolicy()
    {
        // Alpaca: 200 requests/minute = ~3.33 requests/second
        // Conservative approach: 300ms base delay between requests
        var rateLimitPolicy = Policy
            .RateLimitAsync(200, TimeSpan.FromMinutes(1), 10)
            .AsAsyncPolicy<HttpResponseMessage>();

        // Exponential back-off for rate limit and transient errors
        var retryPolicy = Policy
            .HandleResult<HttpResponseMessage>(r => 
                r.StatusCode == HttpStatusCode.TooManyRequests ||
                r.StatusCode == HttpStatusCode.ServiceUnavailable ||
                r.StatusCode == HttpStatusCode.RequestTimeout)
            .Or<HttpRequestException>()
            .Or<TaskCanceledException>()
            .WaitAndRetryAsync(
                retryCount: 5,
                sleepDurationProvider: retryAttempt => TimeSpan.FromSeconds(Math.Pow(2, retryAttempt)) + TimeSpan.FromMilliseconds(Random.Shared.Next(0, 1000)),
                onRetry: (outcome, timespan, retryCount, context) =>
                {
                    _logger.LogWarning("Alpaca API retry {RetryCount}/5 after {Delay}ms. Reason: {Reason}",
                        retryCount, timespan.TotalMilliseconds, 
                        outcome.Result?.StatusCode.ToString() ?? outcome.Exception?.Message ?? "Unknown");
                });

        // Combine rate limit and retry policies
        return Policy.WrapAsync(retryPolicy, rateLimitPolicy);
    }

    /// <summary>
    /// Creates a rate limiting policy for Polygon API (5 requests/second)
    /// with exponential back-off retry policy
    /// </summary>
    public IAsyncPolicy<HttpResponseMessage> CreatePolygonPolicy()
    {
        // Polygon Starter: 5 requests/second
        // Conservative approach: 250ms base delay between requests
        var rateLimitPolicy = Policy
            .RateLimitAsync(5, TimeSpan.FromSeconds(1), 2)
            .AsAsyncPolicy<HttpResponseMessage>();

        // Exponential back-off for rate limit and transient errors
        var retryPolicy = Policy
            .HandleResult<HttpResponseMessage>(r => 
                r.StatusCode == HttpStatusCode.TooManyRequests ||
                r.StatusCode == HttpStatusCode.ServiceUnavailable ||
                r.StatusCode == HttpStatusCode.RequestTimeout)
            .Or<HttpRequestException>()
            .Or<TaskCanceledException>()
            .WaitAndRetryAsync(
                retryCount: 5,
                sleepDurationProvider: retryAttempt => TimeSpan.FromSeconds(Math.Pow(2, retryAttempt)) + TimeSpan.FromMilliseconds(Random.Shared.Next(0, 1000)),
                onRetry: (outcome, timespan, retryCount, context) =>
                {
                    _logger.LogWarning("Polygon API retry {RetryCount}/5 after {Delay}ms. Reason: {Reason}",
                        retryCount, timespan.TotalMilliseconds, 
                        outcome.Result?.StatusCode.ToString() ?? outcome.Exception?.Message ?? "Unknown");
                });

        // Combine rate limit and retry policies
        return Policy.WrapAsync(retryPolicy, rateLimitPolicy);
    }
}
