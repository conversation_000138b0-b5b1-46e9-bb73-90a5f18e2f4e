# SmaTrendFollower System Backup Script
# Comprehensive backup solution for all system components

param(
    [string]$BackupPath = ".\backups\$(Get-Date -Format 'yyyyMMdd_HHmmss')",
    [switch]$IncludeLogs,
    [switch]$CompressBackup,
    [switch]$EncryptBackup,
    [string]$EncryptionKey = "",
    [switch]$Verbose
)

# Color output functions
function Write-Info { param($Message) Write-Host $Message -ForegroundColor Cyan }
function Write-Success { param($Message) Write-Host $Message -ForegroundColor Green }
function Write-Warning { param($Message) Write-Host $Message -ForegroundColor Yellow }
function Write-Error { param($Message) Write-Host $Message -ForegroundColor Red }

$startTime = Get-Date

Write-Success "Starting SmaTrendFollower System Backup"
Write-Info "Backup destination: $BackupPath"

try {
    # Create backup directory structure
    Write-Info "📁 Creating backup directory structure..."
    $backupDirs = @(
        $BackupPath,
        "$BackupPath\config",
        "$BackupPath\database",
        "$BackupPath\state",
        "$BackupPath\logs",
        "$BackupPath\cache"
    )
    
    foreach ($dir in $backupDirs) {
        New-Item -ItemType Directory -Path $dir -Force | Out-Null
    }
    Write-Success "✅ Directory structure created"

    # 1. Configuration Backup
    Write-Info "⚙️ Backing up configuration files..."
    $configFiles = @(
        @{Source = ".env"; Dest = "$BackupPath\config\.env.backup"},
        @{Source = ".env.live"; Dest = "$BackupPath\config\.env.live.backup"},
        @{Source = "appsettings.json"; Dest = "$BackupPath\config\appsettings.backup.json"},
        @{Source = "appsettings.Development.json"; Dest = "$BackupPath\config\appsettings.Development.backup.json"},
        @{Source = "SmaTrendFollower.Console\appsettings.json"; Dest = "$BackupPath\config\console.appsettings.backup.json"}
    )
    
    $configCount = 0
    foreach ($file in $configFiles) {
        if (Test-Path $file.Source) {
            Copy-Item $file.Source $file.Dest -ErrorAction SilentlyContinue
            $configCount++
            if ($Verbose) { Write-Info "  * $($file.Source)" }
        }
    }
    Write-Success "Configuration backup complete ($configCount files)"

    # 2. Database Backup
    Write-Info "Backing up databases..."
    $dbFiles = @(
        @{Source = "index_cache.db"; Dest = "$BackupPath\database\index_cache.backup.db"},
        @{Source = "stock_cache.db"; Dest = "$BackupPath\database\stock_cache.backup.db"},
        @{Source = "Data\state_backup.json"; Dest = "$BackupPath\state\state_backup.json"}
    )
    
    $dbCount = 0
    foreach ($db in $dbFiles) {
        if (Test-Path $db.Source) {
            Copy-Item $db.Source $db.Dest -ErrorAction SilentlyContinue
            $dbCount++
            if ($Verbose) { Write-Info "  * $($db.Source)" }
        }
    }
    Write-Success "✅ Database backup complete ($dbCount files)"

    # 3. Redis State Backup
    Write-Info "📊 Backing up Redis state..."
    try {
        # Test Redis connectivity
        $redisTest = redis-cli ping 2>$null
        if ($redisTest -eq "PONG") {
            # Create Redis backup
            redis-cli --rdb "$BackupPath\state\redis.backup.rdb" 2>$null
            if (Test-Path "$BackupPath\state\redis.backup.rdb") {
                Write-Success "✅ Redis state backup complete"
            } else {
                Write-Warning "⚠️ Redis backup file not created"
            }
        } else {
            Write-Warning "⚠️ Redis not responding - skipping Redis backup"
        }
    } catch {
        Write-Warning "⚠️ Redis backup failed: $($_.Exception.Message)"
    }

    # 4. Live Trading State Backup
    Write-Info "💹 Backing up live trading state..."
    try {
        $stateBackupCmd = "dotnet run --project SmaTrendFollower.Console -- dual-storage export --output `"$BackupPath\state\trading_state.backup.json`""
        $stateResult = Invoke-Expression $stateBackupCmd 2>$null
        if (Test-Path "$BackupPath\state\trading_state.backup.json") {
            Write-Success "✅ Trading state backup complete"
        } else {
            Write-Warning "⚠️ Trading state backup may have failed"
        }
    } catch {
        Write-Warning "⚠️ Trading state backup failed: $($_.Exception.Message)"
    }

    # 5. Cache Backup
    Write-Info "🗂️ Backing up cache data..."
    $cacheFiles = @()
    if (Test-Path "cache") {
        $cacheFiles = Get-ChildItem "cache" -Recurse -File | Where-Object { $_.Length -lt 100MB }
        foreach ($file in $cacheFiles) {
            $relativePath = $file.FullName.Substring((Get-Location).Path.Length + 1)
            $destPath = "$BackupPath\cache\$($file.Name)"
            Copy-Item $file.FullName $destPath -ErrorAction SilentlyContinue
        }
    }
    Write-Success "Cache backup complete ($($cacheFiles.Count) files)"

    # 6. Log Files Backup (if requested)
    if ($IncludeLogs) {
        Write-Info "Backing up log files..."
        $logCount = 0
        if (Test-Path "logs") {
            # Get recent logs (last 7 days)
            $recentLogs = Get-ChildItem "logs" -Filter "*.log" | Where-Object { 
                $_.LastWriteTime -gt (Get-Date).AddDays(-7) 
            }
            
            foreach ($log in $recentLogs) {
                Copy-Item $log.FullName "$BackupPath\logs\$($log.Name)" -ErrorAction SilentlyContinue
                $logCount++
                if ($Verbose) { Write-Info "  * $($log.Name)" }
            }
        }
        Write-Success "Log backup complete ($logCount files)"
    }

    # 7. Create Backup Manifest
    Write-Info "📋 Creating backup manifest..."
    $backupFiles = Get-ChildItem $BackupPath -Recurse -File
    $totalSize = ($backupFiles | Measure-Object -Property Length -Sum).Sum
    
    $manifest = @{
        BackupInfo = @{
            BackupDate = Get-Date -Format "yyyy-MM-dd HH:mm:ss"
            BackupPath = $BackupPath
            BackupType = "Full System Backup"
            TotalFiles = $backupFiles.Count
            TotalSizeMB = [math]::Round($totalSize / 1MB, 2)
            IncludedLogs = $IncludeLogs
            Compressed = $CompressBackup
            Encrypted = $EncryptBackup
        }
        SystemInfo = @{
            MachineName = $env:COMPUTERNAME
            UserName = $env:USERNAME
            OSVersion = [System.Environment]::OSVersion.VersionString
            PowerShellVersion = $PSVersionTable.PSVersion.ToString()
            DotNetVersion = try { (dotnet --version) } catch { "Not Available" }
            BackupScriptVersion = "1.0.0"
        }
        Files = $backupFiles | Select-Object @{
            Name = "RelativePath"
            Expression = { $_.FullName.Substring($BackupPath.Length + 1) }
        }, Length, LastWriteTime | Sort-Object RelativePath
        Verification = @{
            ConfigFiles = $configCount
            DatabaseFiles = $dbCount
            CacheFiles = $cacheFiles.Count
            LogFiles = if ($IncludeLogs) { $logCount } else { "Not Included" }
            RedisBackup = Test-Path "$BackupPath\state\redis.backup.rdb"
            TradingStateBackup = Test-Path "$BackupPath\state\trading_state.backup.json"
        }
    }

    $manifestJson = $manifest | ConvertTo-Json -Depth 4
    $manifestJson | Out-File "$BackupPath\backup_manifest.json" -Encoding UTF8
    Write-Success "✅ Backup manifest created"

    # 8. Compression (if requested)
    if ($CompressBackup) {
        Write-Info "🗜️ Compressing backup..."
        $archivePath = "$BackupPath.zip"
        try {
            Compress-Archive -Path "$BackupPath\*" -DestinationPath $archivePath -CompressionLevel Optimal
            $originalSize = [math]::Round($totalSize / 1MB, 2)
            $compressedSize = [math]::Round((Get-Item $archivePath).Length / 1MB, 2)
            $compressionRatio = [math]::Round((1 - ($compressedSize / $originalSize)) * 100, 1)
            
            Write-Success "✅ Backup compressed: $originalSize MB → $compressedSize MB ($compressionRatio% reduction)"
            
            # Remove uncompressed backup
            Remove-Item $BackupPath -Recurse -Force
            $BackupPath = $archivePath
        } catch {
            Write-Error "❌ Compression failed: $($_.Exception.Message)"
        }
    }

    # 9. Encryption (if requested)
    if ($EncryptBackup -and $EncryptionKey) {
        Write-Info "🔐 Encrypting backup..."
        # Note: This would require additional encryption tools
        Write-Warning "⚠️ Encryption feature requires additional setup (GPG or similar)"
    }

    # 10. Cleanup old backups (keep last 10)
    Write-Info "🧹 Cleaning up old backups..."
    $backupDir = Split-Path $BackupPath -Parent
    if (Test-Path $backupDir) {
        $oldBackups = Get-ChildItem $backupDir | Where-Object { 
            $_.Name -match '^\d{8}_\d{6}' -and $_.Name -ne (Split-Path $BackupPath -Leaf)
        } | Sort-Object LastWriteTime -Descending | Select-Object -Skip 10
        
        foreach ($oldBackup in $oldBackups) {
            Remove-Item $oldBackup.FullName -Recurse -Force
            if ($Verbose) { Write-Info "  🗑️ Removed old backup: $($oldBackup.Name)" }
        }
        Write-Success "✅ Cleanup complete (removed $($oldBackups.Count) old backups)"
    }

    # Final Summary
    Write-Success "`n🎉 BACKUP COMPLETED SUCCESSFULLY!"
    Write-Info "📍 Backup Location: $BackupPath"
    Write-Info "📊 Total Size: $([math]::Round($totalSize / 1MB, 2)) MB"
    Write-Info "📁 Total Files: $($backupFiles.Count)"
    Write-Info "⏱️ Backup Duration: $((Get-Date) - $startTime)"
    
    # Verification
    Write-Info "`n🔍 Backup Verification:"
    Write-Info "  ✓ Configuration: $configCount files"
    Write-Info "  ✓ Databases: $dbCount files"
    Write-Info "  ✓ Cache: $($cacheFiles.Count) files"
    if ($IncludeLogs) { Write-Info "  ✓ Logs: $logCount files" }
    Write-Info "  ✓ Redis State: $(if (Test-Path "$BackupPath\state\redis.backup.rdb" -or $CompressBackup) { "Yes" } else { "No" })"
    Write-Info "  ✓ Trading State: $(if (Test-Path "$BackupPath\state\trading_state.backup.json" -or $CompressBackup) { "Yes" } else { "No" })"

} catch {
    Write-Error "❌ BACKUP FAILED: $($_.Exception.Message)"
    Write-Error "Stack Trace: $($_.Exception.StackTrace)"
    exit 1
} finally {
    $endTime = Get-Date
    $duration = $endTime - $startTime
    Write-Info "⏱️ Total execution time: $($duration.ToString('mm\:ss'))"
}
