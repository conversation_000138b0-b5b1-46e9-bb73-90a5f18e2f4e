using SmaTrendFollower.Console.Extensions;
using SmaTrendFollower.Services.ErrorHandling;
using Alpaca.Markets;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Caching.Memory;

namespace SmaTrendFollower.Services;

/// <summary>
/// Enhanced portfolio gate with comprehensive error handling and caching
/// </summary>
public sealed class EnhancedPortfolioGateWithErrorHandling : IPortfolioGate
{
    private readonly IAlpacaClientFactory _clientFactory;
    private readonly IEnhancedRetryService _retryService;
    private readonly IErrorHandler _errorHandler;
    private readonly IMemoryCache _cache;
    private readonly ILogger<EnhancedPortfolioGateWithErrorHandling> _logger;

    private const string SPY_CACHE_KEY = "spy_sma200_check";
    private static readonly TimeSpan CacheExpiry = TimeSpan.FromMinutes(15); // Cache for 15 minutes

    public EnhancedPortfolioGateWithErrorHandling(
        IAlpacaClientFactory clientFactory,
        IEnhancedRetryService retryService,
        IErrorHandler errorHandler,
        IMemoryCache cache,
        ILogger<EnhancedPortfolioGateWithErrorHandling> logger)
    {
        _clientFactory = clientFactory;
        _retryService = retryService;
        _errorHandler = errorHandler;
        _cache = cache;
        _logger = logger;
    }

    public async Task<bool> ShouldTradeAsync()
    {
        var context = new ErrorContext
        {
            OperationName = nameof(ShouldTradeAsync),
            ServiceName = nameof(EnhancedPortfolioGateWithErrorHandling)
        };

        try
        {
            // Check cache first
            if (_cache.TryGetValue(SPY_CACHE_KEY, out PortfolioGateResult? cachedResult))
            {
                _logger.LogDebug("Using cached SPY SMA200 result: {ShouldTrade}", cachedResult.ShouldTrade);
                return cachedResult.ShouldTrade;
            }

            return await _retryService.ExecuteAsync(
                async () => await CheckSpySma200InternalAsync(),
                "CheckSpySma200",
                "Alpaca");
        }
        catch (Exception ex)
        {
            var result = await _errorHandler.HandleErrorAsync(ex, context);
            
            switch (result.Strategy)
            {
                case RecoveryStrategy.Fallback:
                    _logger.LogWarning("Using fallback portfolio gate decision");
                    return GetFallbackDecision();
                
                case RecoveryStrategy.Skip:
                    _logger.LogWarning("Portfolio gate check failed, defaulting to no trading");
                    return false;
                
                default:
                    _logger.LogError("Portfolio gate check failed critically, blocking trading");
                    return false;
            }
        }
    }

    private async Task<bool> CheckSpySma200InternalAsync()
    {
        var spyData = await GetSpyDataWithValidationAsync();
        
        if (spyData == null || spyData.Bars.Count < 200)
        {
            throw new MarketDataException(
                $"Insufficient SPY data for SMA200 calculation. Got {spyData?.Bars.Count ?? 0} bars, need at least 200",
                "SPY",
                "Alpaca");
        }

        var sma200 = CalculateSma200WithValidation(spyData.Bars);
        var currentPrice = spyData.Bars.Last().Close;
        
        ValidateSpyData(currentPrice, sma200);

        var shouldTrade = currentPrice > sma200;
        
        // Cache the result
        var result = new PortfolioGateResult
        {
            ShouldTrade = shouldTrade,
            CurrentPrice = currentPrice,
            Sma200 = sma200,
            Timestamp = DateTime.UtcNow,
            DataPoints = spyData.Bars.Count
        };
        
        _cache.Set(SPY_CACHE_KEY, result, CacheExpiry);
        
        _logger.LogInformation("SPY SMA200 check: Current={Current:C}, SMA200={SMA200:C}, ShouldTrade={ShouldTrade}, DataPoints={DataPoints}", 
            currentPrice, sma200, shouldTrade, spyData.Bars.Count);

        return shouldTrade;
    }

    private async Task<SpyDataResult?> GetSpyDataWithValidationAsync()
    {
        var context = new ErrorContext
        {
            OperationName = "GetSpyData",
            ServiceName = nameof(EnhancedPortfolioGateWithErrorHandling)
        }.WithProperty("Symbol", "SPY");

        try
        {
            return await _retryService.ExecuteAsync(
                async () =>
                {
                    using var dataClient = _clientFactory.CreateDataClient();
                    
                    // Get 250 days of SPY data for SMA200 calculation with buffer
                    var startDate = DateTime.UtcNow.AddDays(-350); // Extra buffer for weekends/holidays
                    var endDate = DateTime.UtcNow;
                    
                    var request = new HistoricalBarsRequest("SPY", BarTimeFrame.Day,
                        new Interval<DateTime>(startDate, endDate));

                    var response = await dataClient.ListHistoricalBarsAsync(request);
                    var bars = response.Items.ToList();

                    if (bars.Count == 0)
                    {
                        throw new MarketDataException("No SPY data received from Alpaca", "SPY", "Alpaca");
                    }

                    // Validate data quality
                    ValidateBarData(bars);

                    return new SpyDataResult { Bars = bars };
                },
                "GetSpyBars",
                "Alpaca");
        }
        catch (Exception ex)
        {
            var result = await _errorHandler.HandleErrorAsync(ex, context);
            
            if (result.Strategy == RecoveryStrategy.Fallback)
            {
                _logger.LogWarning("SPY data retrieval failed, checking for cached data");
                // Try to use stale cached data if available
                if (_cache.TryGetValue(SPY_CACHE_KEY, out PortfolioGateResult? staleResult))
                {
                    _logger.LogWarning("Using stale cached SPY data from {Timestamp}", staleResult.Timestamp);
                    // Convert cached result back to bar data format for consistency
                    return null; // Will trigger fallback logic
                }
            }

            throw new MarketDataException(
                "Failed to retrieve SPY data for portfolio gate check",
                "SPY",
                "Alpaca",
                isRetriable: true,
                innerException: ex);
        }
    }

    private void ValidateBarData(List<IBar> bars)
    {
        if (bars.Count < 50)
        {
            throw new MarketDataException($"Insufficient SPY data quality: only {bars.Count} bars received");
        }

        // Check for data gaps or invalid prices
        var invalidBars = bars.Where(b => b.Close <= 0 || b.High <= 0 || b.Low <= 0 || b.Open <= 0).ToList();
        if (invalidBars.Any())
        {
            throw new MarketDataException($"Invalid price data detected in {invalidBars.Count} bars");
        }

        // Check for reasonable price ranges (SPY should be between $50 and $1000)
        var unreasonableBars = bars.Where(b => b.Close < 50 || b.Close > 1000).ToList();
        if (unreasonableBars.Any())
        {
            throw new MarketDataException($"Unreasonable SPY prices detected in {unreasonableBars.Count} bars");
        }

        // Check for data freshness (most recent bar should be within last 5 days)
        var mostRecentBar = bars.OrderByDescending(b => b.TimeUtc).First();
        if (DateTime.UtcNow - mostRecentBar.TimeUtc > TimeSpan.FromDays(5))
        {
            throw new MarketDataException($"SPY data is stale: most recent bar is from {mostRecentBar.TimeUtc:yyyy-MM-dd}");
        }
    }

    private decimal CalculateSma200WithValidation(List<IBar> bars)
    {
        try
        {
            var sma200 = (decimal)bars.GetSma200();
            
            if (sma200 <= 0)
            {
                throw new MarketDataException("Invalid SMA200 calculation result: value is zero or negative");
            }

            // Sanity check: SMA200 should be within reasonable range of current price
            var currentPrice = bars.Last().Close;
            var ratio = Math.Abs(currentPrice - sma200) / currentPrice;
            
            if (ratio > 0.5m) // SMA200 shouldn't be more than 50% different from current price
            {
                _logger.LogWarning("Large divergence between SPY price ({CurrentPrice:C}) and SMA200 ({SMA200:C}): {Ratio:P1}",
                    currentPrice, sma200, ratio);
            }

            return sma200;
        }
        catch (Exception ex)
        {
            throw new MarketDataException("Failed to calculate SMA200 from SPY data", innerException: ex);
        }
    }

    private void ValidateSpyData(decimal currentPrice, decimal sma200)
    {
        if (currentPrice <= 0)
        {
            throw new MarketDataException($"Invalid SPY current price: {currentPrice}");
        }

        if (sma200 <= 0)
        {
            throw new MarketDataException($"Invalid SPY SMA200: {sma200}");
        }

        // Additional validation: prices should be in reasonable ranges
        if (currentPrice < 50 || currentPrice > 1000)
        {
            throw new MarketDataException($"SPY price outside reasonable range: {currentPrice:C}");
        }

        if (sma200 < 50 || sma200 > 1000)
        {
            throw new MarketDataException($"SPY SMA200 outside reasonable range: {sma200:C}");
        }
    }

    private bool GetFallbackDecision()
    {
        // Conservative fallback: check if we have any cached data
        if (_cache.TryGetValue(SPY_CACHE_KEY, out PortfolioGateResult? cachedResult))
        {
            var age = DateTime.UtcNow - cachedResult.Timestamp;
            if (age < TimeSpan.FromHours(4)) // Use cached data if less than 4 hours old
            {
                _logger.LogWarning("Using cached portfolio gate decision from {Age} ago: {ShouldTrade}",
                    age, cachedResult.ShouldTrade);
                return cachedResult.ShouldTrade;
            }
        }

        // Ultimate fallback: be conservative and don't trade
        _logger.LogWarning("No reliable SPY data available, defaulting to no trading for safety");
        return false;
    }
}

/// <summary>
/// SPY data result container
/// </summary>
internal sealed class SpyDataResult
{
    public List<IBar> Bars { get; set; } = new();
}

/// <summary>
/// Portfolio gate result with caching information
/// </summary>
internal sealed class PortfolioGateResult
{
    public bool ShouldTrade { get; set; }
    public decimal CurrentPrice { get; set; }
    public decimal Sma200 { get; set; }
    public DateTime Timestamp { get; set; }
    public int DataPoints { get; set; }
}
