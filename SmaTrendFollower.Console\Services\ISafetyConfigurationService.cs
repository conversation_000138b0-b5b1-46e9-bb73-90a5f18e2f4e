namespace SmaTrendFollower.Services;

/// <summary>
/// Service for managing safety configuration
/// </summary>
public interface ISafetyConfigurationService
{
    /// <summary>
    /// Loads safety configuration from environment variables and settings
    /// </summary>
    SafetyConfiguration LoadConfiguration();
    
    /// <summary>
    /// Creates a safe default configuration for production
    /// </summary>
    SafetyConfiguration CreateSafeDefaults();
    
    /// <summary>
    /// Creates a configuration for paper trading
    /// </summary>
    SafetyConfiguration CreatePaperTradingConfiguration();
    
    /// <summary>
    /// Creates a configuration for live trading with enhanced safety
    /// </summary>
    SafetyConfiguration CreateLiveTradingConfiguration();
    
    /// <summary>
    /// Validates configuration settings
    /// </summary>
    (bool IsValid, string[] Errors) ValidateConfiguration(SafetyConfiguration config);
}
