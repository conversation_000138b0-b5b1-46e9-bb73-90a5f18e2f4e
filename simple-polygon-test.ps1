param(
    [string]$ApiKey = $env:POLY_API_KEY
)

if (-not $ApiKey) {
    Write-Host "No Polygon API key found. Set POLY_API_KEY environment variable." -ForegroundColor Red
    exit 1
}

Write-Host "Testing Polygon API Connectivity..." -ForegroundColor Cyan
Write-Host "API Key: $($ApiKey.Substring(0, 8))..." -ForegroundColor Gray

$testResults = @()

# Test 1: Market Status
Write-Host "`nTesting Market Status..." -ForegroundColor Yellow
try {
    $url = "https://api.polygon.io/v1/marketstatus/now?apikey=$ApiKey"
    $response = Invoke-RestMethod -Uri $url -Method Get -TimeoutSec 10
    Write-Host "  Success: Market status retrieved" -ForegroundColor Green
    $testResults += "Market Status: PASS"
} catch {
    Write-Host "  Failed: $($_.Exception.Message)" -ForegroundColor Red
    $testResults += "Market Status: FAIL - $($_.Exception.Message)"
}

# Test 2: SPY Stock Bars
Write-Host "`nTesting SPY Stock Bars..." -ForegroundColor Yellow
try {
    $url = "https://api.polygon.io/v2/aggs/ticker/SPY/range/1/day/2024-12-01/2024-12-20?adjusted=true&sort=asc&apikey=$ApiKey"
    $response = Invoke-RestMethod -Uri $url -Method Get -TimeoutSec 15
    $count = if ($response.results) { $response.results.Count } else { 0 }
    Write-Host "  Success: Retrieved $count SPY bars" -ForegroundColor Green
    $testResults += "SPY Stock Bars: PASS ($count bars)"
} catch {
    Write-Host "  Failed: $($_.Exception.Message)" -ForegroundColor Red
    $testResults += "SPY Stock Bars: FAIL - $($_.Exception.Message)"
}

# Test 3: SPX Index Bars
Write-Host "`nTesting SPX Index Bars..." -ForegroundColor Yellow
try {
    $url = "https://api.polygon.io/v2/aggs/ticker/I:SPX/range/1/day/2024-12-01/2024-12-20?adjusted=true&sort=asc&apikey=$ApiKey"
    $response = Invoke-RestMethod -Uri $url -Method Get -TimeoutSec 15
    $count = if ($response.results) { $response.results.Count } else { 0 }
    Write-Host "  Success: Retrieved $count SPX bars" -ForegroundColor Green
    $testResults += "SPX Index Bars: PASS ($count bars)"
} catch {
    Write-Host "  Failed: $($_.Exception.Message)" -ForegroundColor Red
    $testResults += "SPX Index Bars: FAIL - $($_.Exception.Message)"
}

# Test 4: VIX Index
Write-Host "`nTesting VIX Index..." -ForegroundColor Yellow
try {
    $url = "https://api.polygon.io/v2/aggs/ticker/I:VIX/range/1/day/2024-12-20/2024-12-20?adjusted=true&sort=asc&apikey=$ApiKey"
    $response = Invoke-RestMethod -Uri $url -Method Get -TimeoutSec 15
    $count = if ($response.results) { $response.results.Count } else { 0 }
    Write-Host "  Success: Retrieved $count VIX bars" -ForegroundColor Green
    $testResults += "VIX Index: PASS ($count bars)"
} catch {
    Write-Host "  Failed: $($_.Exception.Message)" -ForegroundColor Red
    $testResults += "VIX Index: FAIL - $($_.Exception.Message)"
}

# Test 5: Minute Bars (High frequency)
Write-Host "`nTesting Minute Bars..." -ForegroundColor Yellow
try {
    $url = "https://api.polygon.io/v2/aggs/ticker/SPY/range/1/minute/2024-12-20/2024-12-20?adjusted=true&sort=asc&limit=100&apikey=$ApiKey"
    $response = Invoke-RestMethod -Uri $url -Method Get -TimeoutSec 20
    $count = if ($response.results) { $response.results.Count } else { 0 }
    Write-Host "  Success: Retrieved $count minute bars" -ForegroundColor Green
    $testResults += "Minute Bars: PASS ($count bars)"
} catch {
    Write-Host "  Failed: $($_.Exception.Message)" -ForegroundColor Red
    $testResults += "Minute Bars: FAIL - $($_.Exception.Message)"
}

# Test 6: Options Contracts
Write-Host "`nTesting Options Contracts..." -ForegroundColor Yellow
try {
    $url = "https://api.polygon.io/v3/reference/options/contracts?underlying_ticker=SPY&limit=10&apikey=$ApiKey"
    $response = Invoke-RestMethod -Uri $url -Method Get -TimeoutSec 15
    $count = if ($response.results) { $response.results.Count } else { 0 }
    Write-Host "  Success: Retrieved $count option contracts" -ForegroundColor Green
    $testResults += "Options Contracts: PASS ($count contracts)"
} catch {
    Write-Host "  Failed: $($_.Exception.Message)" -ForegroundColor Red
    $testResults += "Options Contracts: FAIL - $($_.Exception.Message)"
}

# Summary
Write-Host "`n" + "="*60 -ForegroundColor Cyan
Write-Host "POLYGON CONNECTIVITY TEST SUMMARY" -ForegroundColor Cyan
Write-Host "="*60 -ForegroundColor Cyan

$passedTests = ($testResults | Where-Object { $_ -like "*PASS*" }).Count
$totalTests = $testResults.Count

Write-Host "`nOverall Results:" -ForegroundColor White
Write-Host "  Passed: $passedTests/$totalTests tests" -ForegroundColor $(if ($passedTests -eq $totalTests) { "Green" } else { "Yellow" })

Write-Host "`nDetailed Results:" -ForegroundColor White
foreach ($result in $testResults) {
    $color = if ($result -like "*PASS*") { "Green" } else { "Red" }
    Write-Host "  $result" -ForegroundColor $color
}

# Recommendations
Write-Host "`nRecommendations:" -ForegroundColor White
$failedTests = ($testResults | Where-Object { $_ -like "*FAIL*" }).Count

if ($failedTests -eq 0) {
    Write-Host "  All endpoints are working correctly!" -ForegroundColor Green
} else {
    Write-Host "  $failedTests endpoint(s) have issues:" -ForegroundColor Yellow
    Write-Host "    - Check network connectivity" -ForegroundColor Yellow
    Write-Host "    - Verify API key permissions" -ForegroundColor Yellow
    Write-Host "    - Implement retry logic with exponential backoff" -ForegroundColor Yellow
    Write-Host "    - Enable Alpaca fallback mechanisms" -ForegroundColor Yellow
    Write-Host "    - Add connection pooling and timeout handling" -ForegroundColor Yellow
}

Write-Host "`nNext Steps:" -ForegroundColor White
Write-Host "  1. Review failed endpoints and implement fixes" -ForegroundColor Gray
Write-Host "  2. Add monitoring and alerting for connectivity issues" -ForegroundColor Gray
Write-Host "  3. Test fallback mechanisms under load" -ForegroundColor Gray
Write-Host "  4. Consider implementing connection pooling" -ForegroundColor Gray

if ($passedTests -eq $totalTests) {
    exit 0
} else {
    exit 1
}
