using Microsoft.Extensions.Logging;
using SmaTrendFollower.Configuration;
using SmaTrendFollower.Models;

namespace SmaTrendFollower.Services;

/// <summary>
/// Manages trading cycle intervals with dynamic VIX-based adjustments
/// </summary>
public class TradingCycleManager : ITradingCycleManager
{
    private readonly IMarketDataService _marketDataService;
    private readonly IMarketSessionGuard _marketSessionGuard;
    private readonly ILogger<TradingCycleManager> _logger;
    private TradingCycleConfig _config;

    public TradingCycleManager(
        IMarketDataService marketDataService,
        IMarketSessionGuard marketSessionGuard,
        ILogger<TradingCycleManager> logger,
        TradingCycleConfig config)
    {
        _marketDataService = marketDataService;
        _marketSessionGuard = marketSessionGuard;
        _logger = logger;
        _config = config;
    }

    public async Task<TimeSpan> GetCurrentCycleIntervalAsync(CancellationToken cancellationToken = default)
    {
        try
        {
            // Get current VIX if VIX-based adjustment is enabled
            decimal? currentVix = null;
            if (_config.EnableVixBasedAdjustment)
            {
                try
                {
                    var vixAnalysis = await _marketDataService.GetVixAnalysisAsync();
                    currentVix = vixAnalysis.CurrentVix;
                }
                catch (Exception ex)
                {
                    _logger.LogWarning(ex, "Failed to get VIX data for cycle interval calculation, using default interval");
                }
            }

            // Get market session information
            bool isMarketOpen = false;
            bool isExtendedHours = false;

            try
            {
                isMarketOpen = await _marketSessionGuard.CanTradeNowAsync();
                
                // Check if we're in extended hours (this is a simplified check)
                var now = DateTime.Now;
                var marketOpen = new TimeOnly(9, 30); // 9:30 AM ET
                var marketClose = new TimeOnly(16, 0); // 4:00 PM ET
                var currentTime = TimeOnly.FromDateTime(now);
                
                // Extended hours: 4:00 AM - 9:30 AM and 4:00 PM - 8:00 PM ET
                isExtendedHours = isMarketOpen && (
                    (currentTime >= new TimeOnly(4, 0) && currentTime < marketOpen) ||
                    (currentTime >= marketClose && currentTime <= new TimeOnly(20, 0))
                );
            }
            catch (Exception ex)
            {
                _logger.LogWarning(ex, "Failed to get market session information, assuming market is open");
                isMarketOpen = true;
            }

            var interval = _config.GetCycleInterval(currentVix, isMarketOpen, isExtendedHours);
            
            _logger.LogDebug("Calculated cycle interval: {Interval} minutes. VIX: {Vix}, Market Open: {IsOpen}, Extended Hours: {IsExtended}",
                interval.TotalMinutes, currentVix?.ToString("F1") ?? "N/A", isMarketOpen, isExtendedHours);

            return interval;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error calculating cycle interval, falling back to default");
            return TimeSpan.FromMinutes(_config.DefaultIntervalMinutes);
        }
    }

    public async Task<string> GetCurrentIntervalReasonAsync(CancellationToken cancellationToken = default)
    {
        try
        {
            // Get current VIX if VIX-based adjustment is enabled
            decimal? currentVix = null;
            if (_config.EnableVixBasedAdjustment)
            {
                try
                {
                    var vixAnalysis = await _marketDataService.GetVixAnalysisAsync();
                    currentVix = vixAnalysis.CurrentVix;
                }
                catch (Exception ex)
                {
                    _logger.LogDebug(ex, "Failed to get VIX data for interval reason");
                }
            }

            // Get market session information
            bool isMarketOpen = false;
            bool isExtendedHours = false;

            try
            {
                isMarketOpen = await _marketSessionGuard.CanTradeNowAsync();
                
                var now = DateTime.Now;
                var marketOpen = new TimeOnly(9, 30);
                var marketClose = new TimeOnly(16, 0);
                var currentTime = TimeOnly.FromDateTime(now);
                
                isExtendedHours = isMarketOpen && (
                    (currentTime >= new TimeOnly(4, 0) && currentTime < marketOpen) ||
                    (currentTime >= marketClose && currentTime <= new TimeOnly(20, 0))
                );
            }
            catch (Exception ex)
            {
                _logger.LogDebug(ex, "Failed to get market session information for interval reason");
                isMarketOpen = true;
            }

            return _config.GetIntervalReason(currentVix, isMarketOpen, isExtendedHours);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting interval reason");
            return $"Error determining reason, using default: {_config.DefaultIntervalMinutes} minutes";
        }
    }

    public void UpdateConfiguration(TradingCycleConfig config)
    {
        _config = config;
        _config.ValidateAndAdjust();
        
        _logger.LogInformation("Trading cycle configuration updated. Default interval: {DefaultMinutes} minutes, " +
                             "VIX adjustment enabled: {VixEnabled}, Extended hours adjustment enabled: {ExtendedEnabled}",
            _config.DefaultIntervalMinutes, _config.EnableVixBasedAdjustment, _config.EnableExtendedHoursAdjustment);
    }

    public TradingCycleConfig GetConfiguration()
    {
        return _config;
    }
}
