#!/usr/bin/env pwsh

# Fast test execution script

Write-Host "🏃‍♂️ Running fast unit tests..." -ForegroundColor Cyan

# Run only unit tests with optimized settings
dotnet test SmaTrendFollower.Tests `
    --filter "Category=Unit" `
    --settings SmaTrendFollower.Tests/TestFilters.runsettings `
    --verbosity minimal `
    --no-build `
    --logger "console;verbosity=minimal"

if ($LASTEXITCODE -eq 0) {
    Write-Host "✅ All unit tests passed!" -ForegroundColor Green
} else {
    Write-Host "❌ Some tests failed. Check output above." -ForegroundColor Red
}
