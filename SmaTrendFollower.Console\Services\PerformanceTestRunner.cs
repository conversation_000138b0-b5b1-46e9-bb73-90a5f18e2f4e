using Microsoft.Extensions.Logging;
using System.Diagnostics;

namespace SmaTrendFollower.Services;

/// <summary>
/// Comprehensive performance test runner for parallel and async operations.
/// Validates performance improvements and identifies bottlenecks.
/// </summary>
public sealed class PerformanceTestRunner
{
    private readonly ISignalGenerator _signalGenerator;
    private readonly IMarketDataService _marketDataService;
    private readonly AsyncBarFetchingService _asyncBarFetching;
    private readonly PerformanceMonitoringService _performanceMonitoring;
    private readonly IUniverseProvider _universeProvider;
    private readonly ILogger<PerformanceTestRunner> _logger;

    public PerformanceTestRunner(
        ISignalGenerator signalGenerator,
        IMarketDataService marketDataService,
        AsyncBarFetchingService asyncBarFetching,
        PerformanceMonitoringService performanceMonitoring,
        IUniverseProvider universeProvider,
        ILogger<PerformanceTestRunner> logger)
    {
        _signalGenerator = signalGenerator;
        _marketDataService = marketDataService;
        _asyncBarFetching = asyncBarFetching;
        _performanceMonitoring = performanceMonitoring;
        _universeProvider = universeProvider;
        _logger = logger;
    }

    /// <summary>
    /// Runs all performance tests
    /// </summary>
    public async Task RunAllTestsAsync()
    {
        _logger.LogInformation("=== SmaTrendFollower Performance Test Suite ===");
        _logger.LogInformation("System: {ProcessorCount} cores, {WorkingSet:F0}MB memory", 
            Environment.ProcessorCount, Process.GetCurrentProcess().WorkingSet64 / 1024.0 / 1024.0);

        await TestParallelSignalGenerationAsync();
        await TestParallelDataFetchingAsync();
        await TestAsyncBarFetchingAsync();
        await TestSystemUtilizationAsync();

        _logger.LogInformation("=== Performance Test Suite Complete ===");
        
        // Show final performance summary
        var allMetrics = _performanceMonitoring.GetAllOperationMetrics();
        foreach (var metric in allMetrics.OrderByDescending(m => m.ThroughputPerSecond))
        {
            _logger.LogInformation("Final: {Operation} - {Throughput:F1}/sec, P95: {P95:F0}ms, Errors: {ErrorRate:F1}%",
                metric.OperationName, metric.ThroughputPerSecond, metric.P95LatencyMs, metric.ErrorRate * 100);
        }
    }

    /// <summary>
    /// Tests parallel signal generation performance
    /// </summary>
    public async Task TestParallelSignalGenerationAsync()
    {
        _logger.LogInformation("--- Testing Parallel Signal Generation ---");

        try
        {
            var signals = await _performanceMonitoring.TrackOperationAsync("ParallelSignalGeneration", async () =>
            {
                return await _signalGenerator.RunAsync(20);
            });

            var signalList = signals.ToList();
            _logger.LogInformation("Generated {Count} signals using parallel processing", signalList.Count);

            if (signalList.Any())
            {
                _logger.LogInformation("Top signals:");
                foreach (var signal in signalList.Take(5))
                {
                    _logger.LogInformation("  {Symbol}: Price={Price:F2}, ATR={Atr:F2}, 6M Return={Return:F1}%",
                        signal.Symbol, signal.Price, signal.Atr, signal.SixMonthReturn * 100);
                }
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Parallel signal generation test failed");
        }
    }

    /// <summary>
    /// Tests parallel data fetching performance
    /// </summary>
    public async Task TestParallelDataFetchingAsync()
    {
        _logger.LogInformation("--- Testing Parallel Data Fetching ---");

        try
        {
            // Get a subset of symbols for testing
            var allSymbols = await _universeProvider.GetSymbolsAsync();
            var testSymbols = allSymbols.Take(50).ToList(); // Test with 50 symbols

            _logger.LogInformation("Testing parallel data fetch for {Count} symbols", testSymbols.Count);

            var results = await _performanceMonitoring.TrackOperationAsync("ParallelDataFetch", async () =>
            {
                var startDate = DateTime.UtcNow.AddDays(-30);
                var endDate = DateTime.UtcNow;

                if (_marketDataService is ParallelMarketDataService parallelService)
                {
                    return await parallelService.GetStockBarsAsync(testSymbols, startDate, endDate);
                }
                else
                {
                    // Fallback to sequential fetching for comparison
                    var sequentialResults = new Dictionary<string, Alpaca.Markets.IPage<Alpaca.Markets.IBar>>();
                    foreach (var symbol in testSymbols)
                    {
                        try
                        {
                            var bars = await _marketDataService.GetStockBarsAsync(symbol, startDate, endDate);
                            sequentialResults[symbol] = bars;
                        }
                        catch (Exception ex)
                        {
                            _logger.LogWarning(ex, "Failed to fetch data for {Symbol}", symbol);
                        }
                    }
                    return sequentialResults;
                }
            });

            var totalBars = results.Values.Sum(page => page.Items.Count());
            _logger.LogInformation("Fetched {TotalBars:N0} bars for {SymbolCount}/{RequestedCount} symbols",
                totalBars, results.Count, testSymbols.Count);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Parallel data fetching test failed");
        }
    }

    /// <summary>
    /// Tests async bar fetching service performance
    /// </summary>
    public async Task TestAsyncBarFetchingAsync()
    {
        _logger.LogInformation("--- Testing Async Bar Fetching Service ---");

        try
        {
            // Get a subset of symbols for testing
            var allSymbols = await _universeProvider.GetSymbolsAsync();
            var testSymbols = allSymbols.Take(30).ToList(); // Test with 30 symbols

            _logger.LogInformation("Testing async bar fetching for {Count} symbols", testSymbols.Count);

            var result = await _performanceMonitoring.TrackOperationAsync("AsyncBarFetching", async () =>
            {
                var startDate = DateTime.UtcNow.AddDays(-60);
                var endDate = DateTime.UtcNow;

                return await _asyncBarFetching.FetchBarsAsync(testSymbols, startDate, endDate);
            });

            _logger.LogInformation("Async fetch results: {Success}/{Total} symbols, {TotalBars:N0} bars, {Throughput:F1} symbols/sec",
                result.SuccessCount, result.TotalSymbols, result.TotalBars, result.ThroughputSymbolsPerSecond);

            // Test streaming functionality
            _logger.LogInformation("Testing streaming bar fetch...");
            var streamCount = 0;
            var streamStopwatch = Stopwatch.StartNew();

            await foreach (var symbolData in _asyncBarFetching.StreamBarsAsync(testSymbols.Take(10), 
                DateTime.UtcNow.AddDays(-30), DateTime.UtcNow))
            {
                streamCount++;
                if (symbolData.Success)
                {
                    _logger.LogDebug("Streamed {Count} bars for {Symbol}", symbolData.Bars.Count, symbolData.Symbol);
                }
            }

            streamStopwatch.Stop();
            _logger.LogInformation("Streamed data for {Count} symbols in {ElapsedMs:F0}ms",
                streamCount, streamStopwatch.Elapsed.TotalMilliseconds);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Async bar fetching test failed");
        }
    }

    /// <summary>
    /// Tests system resource utilization during parallel operations
    /// </summary>
    public async Task TestSystemUtilizationAsync()
    {
        _logger.LogInformation("--- Testing System Utilization ---");

        try
        {
            var initialMetrics = _performanceMonitoring.GetSystemMetrics();
            _logger.LogInformation("Initial system state: CPU {Cpu:F1}%, Memory {Memory:F0}MB, Threads {Threads}",
                initialMetrics.CpuUsagePercent, initialMetrics.WorkingSetMB, initialMetrics.ThreadCount);

            // Run a CPU-intensive parallel operation
            await _performanceMonitoring.TrackOperationAsync("CpuIntensiveTest", async () =>
            {
                var tasks = Enumerable.Range(0, Environment.ProcessorCount * 2)
                    .Select(_ => Task.Run(() =>
                    {
                        // Simulate CPU-intensive work
                        var random = new Random();
                        var sum = 0.0;
                        for (int i = 0; i < 1_000_000; i++)
                        {
                            sum += Math.Sqrt(random.NextDouble());
                        }
                        return sum;
                    }))
                    .ToArray();

                await Task.WhenAll(tasks);
                return tasks.Sum(t => t.Result);
            });

            // Wait a moment for metrics to update
            await Task.Delay(2000);

            var finalMetrics = _performanceMonitoring.GetSystemMetrics();
            _logger.LogInformation("Peak system state: CPU {Cpu:F1}%, Memory {Memory:F0}MB, Threads {Threads}",
                finalMetrics.CpuUsagePercent, finalMetrics.WorkingSetMB, finalMetrics.ThreadCount);

            // Test memory allocation patterns
            await _performanceMonitoring.TrackOperationAsync("MemoryAllocationTest", async () =>
            {
                var lists = new List<List<double>>();
                for (int i = 0; i < 100; i++)
                {
                    var list = new List<double>(10000);
                    for (int j = 0; j < 10000; j++)
                    {
                        list.Add(Random.Shared.NextDouble());
                    }
                    lists.Add(list);
                }

                // Force garbage collection
                GC.Collect();
                GC.WaitForPendingFinalizers();
                GC.Collect();

                await Task.Delay(100); // Simulate async work
                return lists.Sum(l => l.Count);
            });

            var gcMetrics = _performanceMonitoring.GetSystemMetrics();
            _logger.LogInformation("After GC: Memory {Memory:F0}MB, GC Memory {GcMemory:F0}MB",
                gcMetrics.WorkingSetMB, gcMetrics.GcMemoryMB);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "System utilization test failed");
        }
    }

    /// <summary>
    /// Benchmarks sequential vs parallel performance
    /// </summary>
    public async Task BenchmarkSequentialVsParallelAsync()
    {
        _logger.LogInformation("--- Benchmarking Sequential vs Parallel Performance ---");

        try
        {
            var testSymbols = new[] { "AAPL", "MSFT", "GOOGL", "AMZN", "TSLA", "META", "NVDA", "NFLX", "CRM", "ADBE" };
            var startDate = DateTime.UtcNow.AddDays(-100);
            var endDate = DateTime.UtcNow;

            // Sequential benchmark
            var sequentialStopwatch = Stopwatch.StartNew();
            var sequentialResults = new Dictionary<string, Alpaca.Markets.IPage<Alpaca.Markets.IBar>>();
            
            foreach (var symbol in testSymbols)
            {
                try
                {
                    var bars = await _marketDataService.GetStockBarsAsync(symbol, startDate, endDate);
                    sequentialResults[symbol] = bars;
                }
                catch (Exception ex)
                {
                    _logger.LogWarning(ex, "Sequential fetch failed for {Symbol}", symbol);
                }
            }
            
            sequentialStopwatch.Stop();

            // Parallel benchmark
            var parallelStopwatch = Stopwatch.StartNew();
            var parallelResults = new Dictionary<string, Alpaca.Markets.IPage<Alpaca.Markets.IBar>>();
            
            if (_marketDataService is ParallelMarketDataService parallelService)
            {
                parallelResults = (Dictionary<string, Alpaca.Markets.IPage<Alpaca.Markets.IBar>>)
                    await parallelService.GetStockBarsAsync(testSymbols, startDate, endDate);
            }
            
            parallelStopwatch.Stop();

            var speedup = sequentialStopwatch.Elapsed.TotalMilliseconds / parallelStopwatch.Elapsed.TotalMilliseconds;
            
            _logger.LogInformation("Performance Comparison:");
            _logger.LogInformation("  Sequential: {SequentialMs:F0}ms ({SequentialCount} symbols)",
                sequentialStopwatch.Elapsed.TotalMilliseconds, sequentialResults.Count);
            _logger.LogInformation("  Parallel:   {ParallelMs:F0}ms ({ParallelCount} symbols)",
                parallelStopwatch.Elapsed.TotalMilliseconds, parallelResults.Count);
            _logger.LogInformation("  Speedup:    {Speedup:F1}x faster", speedup);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Sequential vs parallel benchmark failed");
        }
    }
}
