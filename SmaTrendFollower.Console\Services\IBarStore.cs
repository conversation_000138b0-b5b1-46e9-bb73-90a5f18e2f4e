using Alpaca.Markets;

namespace SmaTrendFollower.Services;

/// <summary>
/// Unified interface for bar storage operations.
/// Abstracts the underlying storage mechanism (SQLite, Redis, etc.)
/// </summary>
public interface IBarStore
{
    /// <summary>
    /// Saves a single bar for a symbol and timeframe
    /// </summary>
    Task SaveBarAsync(string symbol, string timeFrame, IBar bar, CancellationToken cancellationToken = default);

    /// <summary>
    /// Saves multiple bars for a symbol and timeframe
    /// </summary>
    Task SaveBarsAsync(string symbol, string timeFrame, IEnumerable<IBar> bars, CancellationToken cancellationToken = default);

    /// <summary>
    /// Loads bars for a symbol and timeframe within a date range
    /// </summary>
    Task<List<IBar>> LoadBarsAsync(string symbol, string timeFrame, DateTime from, DateTime to, CancellationToken cancellationToken = default);

    /// <summary>
    /// Gets the latest cached date for a symbol and timeframe
    /// </summary>
    Task<DateTime?> GetLatestCachedDateAsync(string symbol, string timeFrame, CancellationToken cancellationToken = default);

    /// <summary>
    /// Gets the earliest cached date for a symbol and timeframe
    /// </summary>
    Task<DateTime?> GetEarliestCachedDateAsync(string symbol, string timeFrame, CancellationToken cancellationToken = default);

    /// <summary>
    /// Checks if bars exist for a symbol and timeframe within a date range
    /// </summary>
    Task<bool> HasBarsAsync(string symbol, string timeFrame, DateTime from, DateTime to, CancellationToken cancellationToken = default);

    /// <summary>
    /// Gets the count of cached bars for a symbol and timeframe
    /// </summary>
    Task<int> GetBarCountAsync(string symbol, string timeFrame, CancellationToken cancellationToken = default);

    /// <summary>
    /// Removes old bars older than the specified date
    /// </summary>
    Task CleanupOldBarsAsync(DateTime olderThan, CancellationToken cancellationToken = default);

    /// <summary>
    /// Gets storage statistics
    /// </summary>
    Task<BarStoreStats> GetStatsAsync(CancellationToken cancellationToken = default);

    /// <summary>
    /// Initializes the storage (creates tables, indexes, etc.)
    /// </summary>
    Task InitializeAsync(CancellationToken cancellationToken = default);
}

/// <summary>
/// Statistics about bar storage
/// </summary>
public record BarStoreStats(
    int TotalSymbols,
    long TotalBars,
    DateTime? EarliestBar,
    DateTime? LatestBar,
    long StorageSizeBytes,
    Dictionary<string, int> SymbolCounts
);

/// <summary>
/// Represents a storage tier for different data access patterns
/// </summary>
public enum StorageTier
{
    /// <summary>
    /// Fast, in-memory storage for real-time data
    /// </summary>
    Hot,
    
    /// <summary>
    /// Persistent storage for historical data
    /// </summary>
    Cold,
    
    /// <summary>
    /// Compressed storage for archived data
    /// </summary>
    Archive
}

/// <summary>
/// Configuration for bar storage behavior
/// </summary>
public record BarStoreConfig(
    StorageTier DefaultTier = StorageTier.Cold,
    TimeSpan HotDataRetention = default,
    TimeSpan ColdDataRetention = default,
    bool EnableCompression = true,
    int CompressionThresholdDays = 30
)
{
    public BarStoreConfig() : this(StorageTier.Cold, TimeSpan.FromDays(1), TimeSpan.FromDays(365), true, 30) { }
}
