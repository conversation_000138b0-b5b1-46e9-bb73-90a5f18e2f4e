using Microsoft.Extensions.Logging;

namespace SmaTrendFollower.Services;

public sealed class MarketSessionGuard : IMarketSessionGuard
{
    private readonly ITimeProvider _timeProvider;
    private readonly ILogger<MarketSessionGuard> _logger;

    public string Reason { get; private set; } = string.Empty;

    public MarketSessionGuard(ITimeProvider timeProvider, ILogger<MarketSessionGuard> logger)
    {
        _timeProvider = timeProvider;
        _logger = logger;
    }

    public Task<bool> CanTradeNowAsync()
    {
        var now = _timeProvider.UtcNow;
        var easternTime = TimeZoneInfo.ConvertTimeFromUtc(now, 
            TimeZoneInfo.FindSystemTimeZoneById("Eastern Standard Time"));

        // Check if it's a weekday
        if (easternTime.DayOfWeek == DayOfWeek.Saturday || easternTime.DayOfWeek == DayOfWeek.Sunday)
        {
            Reason = "Weekend - markets closed";
            _logger.LogInformation("Trading blocked: {Reason}", Reason);
            return Task.FromResult(false);
        }

        // For now, allow trading during any weekday
        // In a real implementation, you'd check market hours, holidays, etc.
        Reason = string.Empty;
        _logger.LogInformation("Trading allowed");
        return Task.FromResult(true);
    }
}
