using Xunit;

namespace SmaTrendFollower.Tests;

/// <summary>
/// Test collection for tests that modify environment variables.
/// These tests cannot run in parallel with each other to avoid race conditions.
/// </summary>
[CollectionDefinition("EnvironmentVariableTests", DisableParallelization = true)]
public class EnvironmentVariableTestCollection
{
}

/// <summary>
/// Test collection for tests that access the file system.
/// These tests cannot run in parallel with each other to avoid file conflicts.
/// </summary>
[CollectionDefinition("FileSystemTests", DisableParallelization = true)]
public class FileSystemTestCollection
{
}

/// <summary>
/// Test collection for tests that use SQLite databases.
/// These tests cannot run in parallel with each other to avoid database conflicts.
/// </summary>
[CollectionDefinition("DatabaseTests", DisableParallelization = true)]
public class DatabaseTestCollection
{
}

/// <summary>
/// Test collection for integration tests that make real network calls.
/// These tests should run sequentially to avoid overwhelming external services.
/// </summary>
[CollectionDefinition("IntegrationTests", DisableParallelization = true)]
public class IntegrationTestCollection
{
}
