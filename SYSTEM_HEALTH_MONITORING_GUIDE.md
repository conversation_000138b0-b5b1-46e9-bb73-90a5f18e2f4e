# System Health Monitoring Guide

## 🏥 Overview

SmaTrendFollower includes comprehensive system health monitoring to ensure reliable operation during live trading. This guide covers the implementation, configuration, and usage of the health monitoring system.

## 🎯 Health Monitoring Objectives

### Primary Goals
- **Proactive Issue Detection**: Identify problems before they impact trading
- **System Reliability**: Ensure consistent system performance
- **Automated Recovery**: Automatic recovery from common issues
- **Performance Optimization**: Identify and resolve performance bottlenecks

### Monitored Components
- **Market Data Connectivity**: API response times and error rates
- **Database Performance**: SQLite and Redis performance metrics
- **Memory and CPU Usage**: System resource consumption
- **Trading System Health**: Order execution and position management
- **Network Connectivity**: Internet and API endpoint availability

## 🔧 Configuration

### Environment Variables
```bash
# Health monitoring settings
ENABLE_SYSTEM_HEALTH_MONITORING=true
HEALTH_CHECK_INTERVAL=60000  # 1 minute
HEALTH_ALERT_THRESHOLD=WARNING
HEALTH_RETENTION_HOURS=168  # 7 days

# Alert thresholds
MEMORY_USAGE_THRESHOLD=0.80  # 80%
CPU_USAGE_THRESHOLD=0.75     # 75%
API_RESPONSE_TIME_THRESHOLD=5000  # 5 seconds
ERROR_RATE_THRESHOLD=0.05    # 5%
DISK_USAGE_THRESHOLD=0.90    # 90%

# Database performance thresholds
SQLITE_QUERY_TIME_THRESHOLD=1000  # 1 second
REDIS_RESPONSE_TIME_THRESHOLD=100 # 100ms
DATABASE_CONNECTION_TIMEOUT=30000 # 30 seconds

# Network monitoring
NETWORK_TIMEOUT=10000        # 10 seconds
PING_INTERVAL=300000         # 5 minutes
DNS_RESOLUTION_TIMEOUT=5000  # 5 seconds
```

### Service Registration
```csharp
// In Program.cs
if (configuration.GetValue<bool>("ENABLE_SYSTEM_HEALTH_MONITORING"))
{
    services.AddSingleton<ISystemHealthService, SystemHealthService>();
    services.AddSingleton<IHealthMetricsCollector, HealthMetricsCollector>();
    services.AddSingleton<IHealthAlertManager, HealthAlertManager>();
    
    // Register as hosted service for background monitoring
    services.AddHostedService<SystemHealthService>();
    
    // Configure health check options
    services.Configure<HealthMonitoringConfiguration>(
        configuration.GetSection("HealthMonitoring"));
}
```

## 📊 Health Check Categories

### 1. Market Data Service Health
```csharp
private async Task<HealthCheckResult> CheckMarketDataServiceAsync()
{
    var stopwatch = Stopwatch.StartNew();
    try
    {
        // Test Alpaca API connectivity
        var account = await _marketDataService.GetAccountAsync();
        var alpacaResponseTime = stopwatch.ElapsedMilliseconds;
        
        // Test Polygon API connectivity
        stopwatch.Restart();
        var vixValue = await _marketDataService.GetIndexValueAsync("I:VIX");
        var polygonResponseTime = stopwatch.ElapsedMilliseconds;
        
        // Evaluate response times
        var maxResponseTime = Math.Max(alpacaResponseTime, polygonResponseTime);
        var status = maxResponseTime > _config.ApiResponseTimeThreshold 
            ? HealthStatus.Warning 
            : HealthStatus.Healthy;
            
        return new HealthCheckResult(status, 
            $"Alpaca: {alpacaResponseTime}ms, Polygon: {polygonResponseTime}ms");
    }
    catch (Exception ex)
    {
        return new HealthCheckResult(HealthStatus.Critical, 
            $"Market data service error: {ex.Message}");
    }
}
```

### 2. Database Performance Health
```csharp
private async Task<HealthCheckResult> CheckDatabasePerformanceAsync()
{
    var results = new List<string>();
    var overallStatus = HealthStatus.Healthy;
    
    // SQLite performance check
    var sqliteStopwatch = Stopwatch.StartNew();
    try
    {
        await _stockBarCache.GetCachedBarsAsync("SPY", "Day", 
            DateTime.UtcNow.AddDays(-1), DateTime.UtcNow);
        var sqliteTime = sqliteStopwatch.ElapsedMilliseconds;
        
        if (sqliteTime > _config.SqliteQueryTimeThreshold)
        {
            overallStatus = HealthStatus.Warning;
        }
        results.Add($"SQLite: {sqliteTime}ms");
    }
    catch (Exception ex)
    {
        overallStatus = HealthStatus.Critical;
        results.Add($"SQLite Error: {ex.Message}");
    }
    
    // Redis performance check (if enabled)
    if (_redisCache != null)
    {
        var redisStopwatch = Stopwatch.StartNew();
        try
        {
            await _redisCache.GetAsync("health:check");
            var redisTime = redisStopwatch.ElapsedMilliseconds;
            
            if (redisTime > _config.RedisResponseTimeThreshold)
            {
                overallStatus = HealthStatus.Warning;
            }
            results.Add($"Redis: {redisTime}ms");
        }
        catch (Exception ex)
        {
            overallStatus = HealthStatus.Warning;  // Redis is optional
            results.Add($"Redis Warning: {ex.Message}");
        }
    }
    
    return new HealthCheckResult(overallStatus, string.Join(", ", results));
}
```

### 3. System Resources Health
```csharp
private async Task<HealthCheckResult> CheckSystemResourcesAsync()
{
    var process = Process.GetCurrentProcess();
    var results = new List<string>();
    var status = HealthStatus.Healthy;
    
    // Memory usage check
    var memoryUsage = GC.GetTotalMemory(false);
    var memoryUsageMB = memoryUsage / (1024 * 1024);
    var availableMemory = GC.GetTotalMemory(false);
    var memoryPercentage = (double)memoryUsage / (availableMemory * 1024 * 1024);
    
    if (memoryPercentage > _config.MemoryUsageThreshold)
    {
        status = HealthStatus.Warning;
    }
    results.Add($"Memory: {memoryUsageMB}MB ({memoryPercentage:P1})");
    
    // CPU usage check
    var cpuUsage = await GetCpuUsageAsync();
    if (cpuUsage > _config.CpuUsageThreshold)
    {
        status = HealthStatus.Warning;
    }
    results.Add($"CPU: {cpuUsage:P1}");
    
    // Disk usage check
    var diskUsage = GetDiskUsagePercentage();
    if (diskUsage > _config.DiskUsageThreshold)
    {
        status = HealthStatus.Critical;
    }
    results.Add($"Disk: {diskUsage:P1}");
    
    return new HealthCheckResult(status, string.Join(", ", results));
}
```

### 4. Trading System Health
```csharp
private async Task<HealthCheckResult> CheckTradingSystemAsync()
{
    try
    {
        // Check account status
        var account = await _marketDataService.GetAccountAsync();
        if (account.TradingBlocked)
        {
            return new HealthCheckResult(HealthStatus.Critical, 
                "Trading is blocked on account");
        }
        
        // Check position consistency
        var positions = await _marketDataService.GetPositionsAsync();
        var positionCount = positions.Count;
        
        // Check recent order execution
        var recentFills = await _marketDataService.GetRecentFillsAsync(10);
        var lastFillAge = recentFills.Any() 
            ? DateTime.UtcNow - recentFills.First().FilledAt?.ToUniversalTime()
            : TimeSpan.MaxValue;
        
        var status = HealthStatus.Healthy;
        var details = new List<string>
        {
            $"Positions: {positionCount}",
            $"Account Equity: {account.Equity:C}",
            $"Buying Power: {account.BuyingPower:C}"
        };
        
        if (lastFillAge < TimeSpan.FromHours(24))
        {
            details.Add($"Last Fill: {lastFillAge.TotalHours:F1}h ago");
        }
        
        return new HealthCheckResult(status, string.Join(", ", details));
    }
    catch (Exception ex)
    {
        return new HealthCheckResult(HealthStatus.Critical, 
            $"Trading system error: {ex.Message}");
    }
}
```

### 5. Network Connectivity Health
```csharp
private async Task<HealthCheckResult> CheckNetworkConnectivityAsync()
{
    var results = new List<string>();
    var status = HealthStatus.Healthy;
    
    // Test internet connectivity
    try
    {
        using var httpClient = new HttpClient();
        httpClient.Timeout = TimeSpan.FromMilliseconds(_config.NetworkTimeout);
        
        var response = await httpClient.GetAsync("https://www.google.com");
        if (response.IsSuccessStatusCode)
        {
            results.Add("Internet: OK");
        }
        else
        {
            status = HealthStatus.Warning;
            results.Add($"Internet: {response.StatusCode}");
        }
    }
    catch (Exception ex)
    {
        status = HealthStatus.Critical;
        results.Add($"Internet: Error - {ex.Message}");
    }
    
    // Test API endpoints
    var endpoints = new[]
    {
        ("Alpaca", "https://paper-api.alpaca.markets/v2/account"),
        ("Polygon", "https://api.polygon.io/v1/meta/symbols/AAPL/company")
    };
    
    foreach (var (name, url) in endpoints)
    {
        try
        {
            using var httpClient = new HttpClient();
            httpClient.Timeout = TimeSpan.FromMilliseconds(_config.NetworkTimeout);
            
            var response = await httpClient.GetAsync(url);
            results.Add($"{name}: {response.StatusCode}");
            
            if (!response.IsSuccessStatusCode && status == HealthStatus.Healthy)
            {
                status = HealthStatus.Warning;
            }
        }
        catch (Exception ex)
        {
            status = HealthStatus.Warning;
            results.Add($"{name}: Error");
        }
    }
    
    return new HealthCheckResult(status, string.Join(", ", results));
}
```

## 🚨 Health Events and Alerting

### Health Event Types
```csharp
public enum HealthEventSeverity
{
    Info,        // Informational events
    Warning,     // Warning conditions that need attention
    Critical,    // Critical issues requiring immediate action
    Emergency    // System-threatening emergencies
}

public class HealthEvent
{
    public DateTime Timestamp { get; set; }
    public string Component { get; set; }
    public string Message { get; set; }
    public HealthEventSeverity Severity { get; set; }
    public Dictionary<string, object> Metadata { get; set; }
}
```

### Alert Management
```csharp
public class HealthAlertManager
{
    private readonly IDiscordNotificationService _discordService;
    private readonly Dictionary<string, DateTime> _lastAlertTimes = new();
    private readonly TimeSpan _alertCooldown = TimeSpan.FromMinutes(15);
    
    public async Task ProcessHealthEventAsync(HealthEvent healthEvent)
    {
        // Check if we should send an alert
        if (ShouldSendAlert(healthEvent))
        {
            await SendHealthAlertAsync(healthEvent);
            _lastAlertTimes[healthEvent.Component] = DateTime.UtcNow;
        }
        
        // Always log the event
        await LogHealthEventAsync(healthEvent);
    }
    
    private bool ShouldSendAlert(HealthEvent healthEvent)
    {
        // Always alert for critical and emergency events
        if (healthEvent.Severity >= HealthEventSeverity.Critical)
            return true;
            
        // Check cooldown for warning events
        if (_lastAlertTimes.TryGetValue(healthEvent.Component, out var lastAlert))
        {
            return DateTime.UtcNow - lastAlert > _alertCooldown;
        }
        
        return true;
    }
}
```

### Discord Integration
```csharp
private async Task SendHealthAlertAsync(HealthEvent healthEvent)
{
    var color = healthEvent.Severity switch
    {
        HealthEventSeverity.Info => 0x00FF00,      // Green
        HealthEventSeverity.Warning => 0xFFFF00,   // Yellow
        HealthEventSeverity.Critical => 0xFF0000,  // Red
        HealthEventSeverity.Emergency => 0x800080, // Purple
        _ => 0x808080                              // Gray
    };
    
    var embed = new
    {
        title = $"🏥 System Health Alert - {healthEvent.Severity}",
        description = healthEvent.Message,
        color = color,
        fields = new[]
        {
            new { name = "Component", value = healthEvent.Component, inline = true },
            new { name = "Timestamp", value = healthEvent.Timestamp.ToString("yyyy-MM-dd HH:mm:ss UTC"), inline = true },
            new { name = "Severity", value = healthEvent.Severity.ToString(), inline = true }
        },
        footer = new { text = "SmaTrendFollower Health Monitor" }
    };
    
    await _discordService.SendEmbedAsync(embed);
}
```

## 📈 Health Metrics and Reporting

### Metrics Collection
```csharp
public class HealthMetrics
{
    public DateTime Timestamp { get; set; }
    public double CpuUsagePercent { get; set; }
    public long MemoryUsageBytes { get; set; }
    public double DiskUsagePercent { get; set; }
    public int ActiveConnections { get; set; }
    public long ApiRequestsPerMinute { get; set; }
    public double AverageResponseTime { get; set; }
    public int ErrorCount { get; set; }
}

// Metrics collection service
public async Task RecordHealthMetricsAsync()
{
    var metrics = new HealthMetrics
    {
        Timestamp = DateTime.UtcNow,
        CpuUsagePercent = await GetCpuUsageAsync(),
        MemoryUsageBytes = GC.GetTotalMemory(false),
        DiskUsagePercent = GetDiskUsagePercentage(),
        ActiveConnections = GetActiveConnectionCount(),
        ApiRequestsPerMinute = GetApiRequestRate(),
        AverageResponseTime = GetAverageResponseTime(),
        ErrorCount = GetRecentErrorCount()
    };
    
    await _metricsStore.SaveMetricsAsync(metrics);
}
```

### Health Dashboard
```csharp
public async Task<HealthDashboard> GetHealthDashboardAsync()
{
    var currentHealth = await GetCurrentHealthAsync();
    var recentMetrics = await GetRecentMetricsAsync(TimeSpan.FromHours(24));
    var recentEvents = await GetRecentEventsAsync(TimeSpan.FromHours(24));
    
    return new HealthDashboard
    {
        OverallStatus = currentHealth.OverallStatus,
        ComponentHealth = currentHealth.ComponentHealth,
        RecentMetrics = recentMetrics,
        RecentEvents = recentEvents,
        Uptime = GetSystemUptime(),
        LastUpdated = DateTime.UtcNow
    };
}
```

## 🔄 Automated Recovery

### Self-Healing Capabilities
```csharp
public class AutoRecoveryManager
{
    public async Task HandleHealthIssueAsync(string component, HealthStatus status)
    {
        switch (component.ToLower())
        {
            case "marketdata":
                if (status == HealthStatus.Critical)
                {
                    await RestartMarketDataConnectionAsync();
                }
                break;
                
            case "database":
                if (status == HealthStatus.Warning)
                {
                    await OptimizeDatabaseAsync();
                }
                break;
                
            case "memory":
                if (status == HealthStatus.Warning)
                {
                    await ForceGarbageCollectionAsync();
                }
                break;
                
            case "network":
                if (status == HealthStatus.Critical)
                {
                    await RestartNetworkConnectionsAsync();
                }
                break;
        }
    }
    
    private async Task RestartMarketDataConnectionAsync()
    {
        _logger.LogInformation("Attempting to restart market data connection");
        
        try
        {
            await _marketDataService.DisconnectAsync();
            await Task.Delay(5000);  // Wait 5 seconds
            await _marketDataService.ConnectAsync();
            
            _logger.LogInformation("Market data connection restarted successfully");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to restart market data connection");
        }
    }
}
```

## 🎯 Best Practices

### Monitoring Strategy
- **Proactive Monitoring**: Monitor leading indicators, not just failures
- **Appropriate Thresholds**: Set thresholds that balance sensitivity with noise
- **Escalation Procedures**: Define clear escalation paths for different severity levels

### Performance Optimization
- **Efficient Checks**: Keep health checks lightweight and fast
- **Batch Operations**: Batch multiple checks when possible
- **Caching**: Cache health check results appropriately

### Alert Management
- **Alert Fatigue**: Implement cooldowns and intelligent filtering
- **Actionable Alerts**: Ensure all alerts have clear remediation steps
- **Documentation**: Maintain runbooks for common health issues

This comprehensive guide provides all necessary information for implementing and managing system health monitoring in the SmaTrendFollower system.
