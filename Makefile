# SmaTrendFollower Makefile
# Common operations for Augment Agent

.PHONY: help setup build test run clean restore validate

# Default target
help:
	@echo "SmaTrendFollower - Available Commands"
	@echo "===================================="
	@echo ""
	@echo "Setup Commands:"
	@echo "  setup      - Run environment setup script"
	@echo "  validate   - Validate environment setup"
	@echo ""
	@echo "Build Commands:"
	@echo "  restore    - Restore NuGet packages"
	@echo "  build      - Build the solution"
	@echo "  clean      - Clean build artifacts"
	@echo ""
	@echo "Test Commands:"
	@echo "  test       - Run all tests"
	@echo "  test-unit  - Run unit tests only"
	@echo ""
	@echo "Run Commands:"
	@echo "  run        - Run the console application"
	@echo "  run-help   - Show application help"
	@echo ""
	@echo "Development Commands:"
	@echo "  dev-build  - Build in Debug mode"
	@echo "  dev-test   - Run tests with detailed output"
	@echo ""

# Setup commands
setup:
ifeq ($(OS),Windows_NT)
	@powershell -ExecutionPolicy Bypass -File setup-remote-environment.ps1
else
	@chmod +x setup-remote-environment.sh
	@./setup-remote-environment.sh
endif

validate:
ifeq ($(OS),Windows_NT)
	@powershell -ExecutionPolicy Bypass -File validate-environment.ps1
else
	@echo "Validation script currently Windows-only. Use manual checks:"
	@echo "  dotnet --version"
	@echo "  dotnet build"
	@echo "  dotnet test"
endif

# Build commands
restore:
	@echo "🔄 Restoring NuGet packages..."
	@dotnet restore

build: restore
	@echo "🔨 Building solution..."
	@dotnet build --configuration Release --no-restore

clean:
	@echo "🧹 Cleaning build artifacts..."
	@dotnet clean

# Test commands
test: build
	@echo "🧪 Running tests..."
	@dotnet test --configuration Release --no-build

test-unit: build
	@echo "🧪 Running unit tests..."
	@dotnet test SmaTrendFollower.Tests --configuration Release --no-build

# Run commands
run: build
	@echo "🚀 Running SmaTrendFollower..."
	@dotnet run --project SmaTrendFollower.Console --configuration Release

run-help: build
	@echo "📚 SmaTrendFollower Help:"
	@dotnet run --project SmaTrendFollower.Console --configuration Release -- --help

# Development commands
dev-build:
	@echo "🔨 Building solution (Debug)..."
	@dotnet build --configuration Debug

dev-test: dev-build
	@echo "🧪 Running tests (Debug, Verbose)..."
	@dotnet test --configuration Debug --no-build --verbosity normal

# Quick validation
check:
	@echo "🔍 Quick Environment Check:"
	@echo "  .NET Version: $$(dotnet --version)"
	@echo "  Solution File: $$(if [ -f SmaTrendFollower.sln ]; then echo '✅ Found'; else echo '❌ Missing'; fi)"
	@echo "  Environment File: $$(if [ -f .env ]; then echo '✅ Found'; else echo '❌ Missing'; fi)"
	@echo "  Console Project: $$(if [ -f SmaTrendFollower.Console/SmaTrendFollower.Console.csproj ]; then echo '✅ Found'; else echo '❌ Missing'; fi)"
	@echo "  Test Project: $$(if [ -f SmaTrendFollower.Tests/SmaTrendFollower.Tests.csproj ]; then echo '✅ Found'; else echo '❌ Missing'; fi)"

# CI/CD friendly commands
ci-build:
	@dotnet restore
	@dotnet build --configuration Release --no-restore --verbosity minimal

ci-test:
	@dotnet test --configuration Release --no-build --verbosity minimal --logger trx

# Package and publish (for deployment)
package: build
	@echo "📦 Creating deployment package..."
	@dotnet publish SmaTrendFollower.Console --configuration Release --output ./publish

# Database operations (if needed)
db-migrate:
	@echo "🗄️  Running database migrations..."
	@dotnet run --project SmaTrendFollower.Console -- --migrate

# Logs and cleanup
logs:
	@echo "📋 Recent log files:"
	@ls -la logs/ 2>/dev/null || echo "No logs directory found"

clean-logs:
	@echo "🧹 Cleaning old log files..."
	@find logs/ -name "*.log" -mtime +7 -delete 2>/dev/null || echo "No old logs to clean"

clean-cache:
	@echo "🧹 Cleaning cache files..."
	@rm -f *.db 2>/dev/null || echo "No cache files to clean"

# Full reset
reset: clean clean-logs clean-cache
	@echo "🔄 Full reset completed"
	@echo "Run 'make setup' to reinitialize environment"
