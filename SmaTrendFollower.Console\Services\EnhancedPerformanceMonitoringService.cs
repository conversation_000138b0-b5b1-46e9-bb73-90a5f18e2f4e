using Microsoft.Extensions.Logging;
using System.Collections.Concurrent;
using System.Diagnostics;

namespace SmaTrendFollower.Services;

/// <summary>
/// Enhanced performance monitoring service with bottleneck detection and alerting
/// </summary>
public interface IEnhancedPerformanceMonitoringService : IDisposable
{
    /// <summary>
    /// Tracks operation performance with detailed metrics
    /// </summary>
    Task<T> TrackOperationAsync<T>(string operationName, Func<Task<T>> operation, PerformanceThresholds? thresholds = null);

    /// <summary>
    /// Records a performance metric
    /// </summary>
    void RecordMetric(string metricName, double value, string? unit = null);

    /// <summary>
    /// Detects performance bottlenecks
    /// </summary>
    Task<BottleneckAnalysis> DetectBottlenecksAsync();

    /// <summary>
    /// Gets comprehensive performance report
    /// </summary>
    EnhancedPerformanceReport GetPerformanceReport();

    /// <summary>
    /// Sets up performance alerts
    /// </summary>
    void ConfigureAlerts(PerformanceAlertConfig config);

    /// <summary>
    /// Gets real-time performance dashboard data
    /// </summary>
    PerformanceDashboard GetDashboard();
}

/// <summary>
/// Enhanced performance monitoring service implementation
/// </summary>
public sealed class EnhancedPerformanceMonitoringService : IEnhancedPerformanceMonitoringService
{
    private readonly ILogger<EnhancedPerformanceMonitoringService> _logger;
    private readonly ConcurrentDictionary<string, EnhancedOperationMetrics> _operationMetrics;
    private readonly ConcurrentDictionary<string, MetricHistory> _metricHistory;
    private readonly PerformanceAlertManager _alertManager;
    private readonly Timer _monitoringTimer;
    private readonly object _lockObject = new();
    private bool _disposed;

    public EnhancedPerformanceMonitoringService(ILogger<EnhancedPerformanceMonitoringService> logger)
    {
        _logger = logger;
        _operationMetrics = new ConcurrentDictionary<string, EnhancedOperationMetrics>();
        _metricHistory = new ConcurrentDictionary<string, MetricHistory>();
        _alertManager = new PerformanceAlertManager(logger);
        
        // Start monitoring timer for continuous analysis
        _monitoringTimer = new Timer(PerformContinuousMonitoring, null, TimeSpan.FromMinutes(1), TimeSpan.FromMinutes(1));
    }

    public async Task<T> TrackOperationAsync<T>(string operationName, Func<Task<T>> operation, PerformanceThresholds? thresholds = null)
    {
        var stopwatch = Stopwatch.StartNew();
        var startTime = DateTime.UtcNow;
        var memoryBefore = GC.GetTotalMemory(false);

        try
        {
            var result = await operation().ConfigureAwait(false);
            stopwatch.Stop();

            var memoryAfter = GC.GetTotalMemory(false);
            var memoryUsed = memoryAfter - memoryBefore;

            RecordOperationSuccess(operationName, stopwatch.Elapsed, memoryUsed, thresholds);
            return result;
        }
        catch (Exception ex)
        {
            stopwatch.Stop();
            RecordOperationError(operationName, stopwatch.Elapsed, ex);
            throw;
        }
    }

    public void RecordMetric(string metricName, double value, string? unit = null)
    {
        var timestamp = DateTime.UtcNow;
        
        _metricHistory.AddOrUpdate(metricName,
            new MetricHistory(metricName, unit),
            (key, existing) =>
            {
                existing.AddValue(value, timestamp);
                return existing;
            });

        // Check for alerts
        _alertManager.CheckMetricAlert(metricName, value);
    }

    public async Task<BottleneckAnalysis> DetectBottlenecksAsync()
    {
        var bottlenecks = new List<PerformanceBottleneck>();
        var analysisTime = DateTime.UtcNow;

        // Analyze operation performance
        foreach (var kvp in _operationMetrics)
        {
            var operationName = kvp.Key;
            var metrics = kvp.Value;

            // Check for slow operations
            if (metrics.AverageLatencyMs > 1000) // Operations taking more than 1 second
            {
                bottlenecks.Add(new PerformanceBottleneck(
                    BottleneckType.SlowOperation,
                    operationName,
                    $"Average latency: {metrics.AverageLatencyMs:F1}ms",
                    BottleneckSeverity.High,
                    new Dictionary<string, object>
                    {
                        ["AverageLatencyMs"] = metrics.AverageLatencyMs,
                        ["TotalOperations"] = metrics.TotalOperations,
                        ["ErrorRate"] = metrics.ErrorRate
                    }));
            }

            // Check for high error rates
            if (metrics.ErrorRate > 0.1) // More than 10% error rate
            {
                bottlenecks.Add(new PerformanceBottleneck(
                    BottleneckType.HighErrorRate,
                    operationName,
                    $"Error rate: {metrics.ErrorRate:P1}",
                    BottleneckSeverity.Critical,
                    new Dictionary<string, object>
                    {
                        ["ErrorRate"] = metrics.ErrorRate,
                        ["TotalErrors"] = metrics.TotalErrors,
                        ["TotalOperations"] = metrics.TotalOperations
                    }));
            }

            // Check for memory leaks
            if (metrics.AverageMemoryUsageBytes > 50 * 1024 * 1024) // More than 50MB average
            {
                bottlenecks.Add(new PerformanceBottleneck(
                    BottleneckType.MemoryLeak,
                    operationName,
                    $"Average memory usage: {metrics.AverageMemoryUsageBytes / 1024.0 / 1024.0:F1}MB",
                    BottleneckSeverity.Medium,
                    new Dictionary<string, object>
                    {
                        ["AverageMemoryUsageBytes"] = metrics.AverageMemoryUsageBytes,
                        ["PeakMemoryUsageBytes"] = metrics.PeakMemoryUsageBytes
                    }));
            }
        }

        // Analyze system metrics
        await AnalyzeSystemBottlenecksAsync(bottlenecks);

        return new BottleneckAnalysis(
            analysisTime,
            bottlenecks,
            GenerateRecommendations(bottlenecks));
    }

    public EnhancedPerformanceReport GetPerformanceReport()
    {
        var reportTime = DateTime.UtcNow;
        var operations = _operationMetrics.Values.ToList();
        var metrics = _metricHistory.Values.ToList();

        var summary = new PerformanceReportSummary(
            operations.Sum(o => o.TotalOperations),
            operations.Where(o => o.TotalOperations > 0).Average(o => o.AverageLatencyMs),
            operations.Sum(o => o.TotalErrors),
            operations.Where(o => o.TotalOperations > 0).Average(o => o.ErrorRate),
            operations.Sum(o => o.TotalMemoryUsageBytes),
            GetSystemPerformanceSnapshot()
        );

        return new EnhancedPerformanceReport(
            reportTime,
            summary,
            operations,
            metrics,
            _alertManager.GetRecentAlerts());
    }

    public void ConfigureAlerts(PerformanceAlertConfig config)
    {
        _alertManager.ConfigureAlerts(config);
    }

    public PerformanceDashboard GetDashboard()
    {
        var currentTime = DateTime.UtcNow;
        var recentOperations = _operationMetrics.Values
            .Where(o => o.LastOperationTime > currentTime.AddMinutes(-5))
            .ToList();

        var recentMetrics = _metricHistory.Values
            .SelectMany(m => m.GetRecentValues(TimeSpan.FromMinutes(5)))
            .ToList();

        return new PerformanceDashboard(
            currentTime,
            recentOperations,
            recentMetrics,
            GetSystemPerformanceSnapshot(),
            _alertManager.GetActiveAlerts());
    }

    private void RecordOperationSuccess(string operationName, TimeSpan elapsed, long memoryUsed, PerformanceThresholds? thresholds)
    {
        _operationMetrics.AddOrUpdate(operationName,
            new EnhancedOperationMetrics(operationName, 1, 0, elapsed.TotalMilliseconds, elapsed.TotalMilliseconds, memoryUsed, memoryUsed, DateTime.UtcNow),
            (key, existing) => existing.RecordSuccess(elapsed.TotalMilliseconds, memoryUsed));

        // Check thresholds
        if (thresholds != null)
        {
            _alertManager.CheckOperationThresholds(operationName, elapsed.TotalMilliseconds, thresholds);
        }

        _logger.LogDebug("Operation {OperationName} completed in {ElapsedMs}ms, memory: {MemoryKB}KB",
            operationName, elapsed.TotalMilliseconds, memoryUsed / 1024);
    }

    private void RecordOperationError(string operationName, TimeSpan elapsed, Exception exception)
    {
        _operationMetrics.AddOrUpdate(operationName,
            new EnhancedOperationMetrics(operationName, 1, 1, elapsed.TotalMilliseconds, elapsed.TotalMilliseconds, 0, 0, DateTime.UtcNow),
            (key, existing) => existing.RecordError(elapsed.TotalMilliseconds));

        _alertManager.CheckOperationError(operationName, exception);

        _logger.LogWarning(exception, "Operation {OperationName} failed after {ElapsedMs}ms",
            operationName, elapsed.TotalMilliseconds);
    }

    private async Task AnalyzeSystemBottlenecksAsync(List<PerformanceBottleneck> bottlenecks)
    {
        // Analyze CPU usage
        var cpuUsage = await GetCpuUsageAsync();
        if (cpuUsage > 80)
        {
            bottlenecks.Add(new PerformanceBottleneck(
                BottleneckType.HighCpuUsage,
                "System",
                $"CPU usage: {cpuUsage:F1}%",
                BottleneckSeverity.High,
                new Dictionary<string, object> { ["CpuUsage"] = cpuUsage }));
        }

        // Analyze memory usage
        var memoryUsage = GC.GetTotalMemory(false);
        if (memoryUsage > 500 * 1024 * 1024) // More than 500MB
        {
            bottlenecks.Add(new PerformanceBottleneck(
                BottleneckType.HighMemoryUsage,
                "System",
                $"Memory usage: {memoryUsage / 1024.0 / 1024.0:F1}MB",
                BottleneckSeverity.Medium,
                new Dictionary<string, object> { ["MemoryUsageBytes"] = memoryUsage }));
        }
    }

    private async Task<double> GetCpuUsageAsync()
    {
        // Simple CPU usage calculation
        var startTime = DateTime.UtcNow;
        var startCpuUsage = Process.GetCurrentProcess().TotalProcessorTime;
        await Task.Delay(100);
        var endTime = DateTime.UtcNow;
        var endCpuUsage = Process.GetCurrentProcess().TotalProcessorTime;

        var cpuUsedMs = (endCpuUsage - startCpuUsage).TotalMilliseconds;
        var totalMsPassed = (endTime - startTime).TotalMilliseconds;
        var cpuUsageTotal = cpuUsedMs / (Environment.ProcessorCount * totalMsPassed);

        return cpuUsageTotal * 100;
    }

    private SystemPerformanceSnapshot GetSystemPerformanceSnapshot()
    {
        var process = Process.GetCurrentProcess();
        return new SystemPerformanceSnapshot(
            DateTime.UtcNow,
            process.WorkingSet64,
            GC.GetTotalMemory(false),
            process.Threads.Count,
            Environment.ProcessorCount);
    }

    private List<string> GenerateRecommendations(List<PerformanceBottleneck> bottlenecks)
    {
        var recommendations = new List<string>();

        if (bottlenecks.Any(b => b.Type == BottleneckType.SlowOperation))
        {
            recommendations.Add("Consider optimizing slow operations with caching or parallel processing");
        }

        if (bottlenecks.Any(b => b.Type == BottleneckType.HighErrorRate))
        {
            recommendations.Add("Investigate and fix operations with high error rates");
        }

        if (bottlenecks.Any(b => b.Type == BottleneckType.MemoryLeak))
        {
            recommendations.Add("Review memory usage patterns and implement proper disposal");
        }

        if (bottlenecks.Any(b => b.Type == BottleneckType.HighCpuUsage))
        {
            recommendations.Add("Consider reducing CPU-intensive operations or adding more processing power");
        }

        return recommendations;
    }

    private void PerformContinuousMonitoring(object? state)
    {
        try
        {
            // Perform background analysis
            var bottlenecks = DetectBottlenecksAsync().Result;
            
            if (bottlenecks.Bottlenecks.Any(b => b.Severity == BottleneckSeverity.Critical))
            {
                _logger.LogWarning("Critical performance bottlenecks detected: {Count}", 
                    bottlenecks.Bottlenecks.Count(b => b.Severity == BottleneckSeverity.Critical));
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error during continuous performance monitoring");
        }
    }

    public void Dispose()
    {
        if (_disposed)
            return;

        _disposed = true;
        _monitoringTimer?.Dispose();
        _alertManager?.Dispose();
    }
}

// Supporting types and classes would be defined here...
public record PerformanceThresholds(double MaxLatencyMs, double MaxMemoryMB, double MaxErrorRate);
public record PerformanceBottleneck(BottleneckType Type, string Component, string Description, BottleneckSeverity Severity, Dictionary<string, object> Metrics);
public record BottleneckAnalysis(DateTime AnalysisTime, List<PerformanceBottleneck> Bottlenecks, List<string> Recommendations);

public enum BottleneckType { SlowOperation, HighErrorRate, MemoryLeak, HighCpuUsage, HighMemoryUsage }
public enum BottleneckSeverity { Low, Medium, High, Critical }

// Additional supporting classes would be implemented here...
internal class PerformanceAlertManager
{
    private readonly ILogger _logger;
    public PerformanceAlertManager(ILogger logger) => _logger = logger;
    public void CheckMetricAlert(string metricName, double value) { }
    public void CheckOperationThresholds(string operationName, double latencyMs, PerformanceThresholds thresholds) { }
    public void CheckOperationError(string operationName, Exception exception) { }
    public void ConfigureAlerts(PerformanceAlertConfig config) { }
    public List<PerformanceAlert> GetRecentAlerts() => new();
    public List<PerformanceAlert> GetActiveAlerts() => new();
    public void Dispose() { }
}

public class EnhancedOperationMetrics
{
    public string OperationName { get; }
    public int TotalOperations { get; private set; }
    public int TotalErrors { get; private set; }
    public double AverageLatencyMs { get; private set; }
    public double PeakLatencyMs { get; private set; }
    public long AverageMemoryUsageBytes { get; private set; }
    public long PeakMemoryUsageBytes { get; private set; }
    public long TotalMemoryUsageBytes { get; private set; }
    public DateTime LastOperationTime { get; private set; }
    public double ErrorRate => TotalOperations > 0 ? (double)TotalErrors / TotalOperations : 0;

    public EnhancedOperationMetrics(string operationName, int totalOps, int totalErrors, double avgLatency, double peakLatency, long avgMemory, long peakMemory, DateTime lastOpTime)
    {
        OperationName = operationName;
        TotalOperations = totalOps;
        TotalErrors = totalErrors;
        AverageLatencyMs = avgLatency;
        PeakLatencyMs = peakLatency;
        AverageMemoryUsageBytes = avgMemory;
        PeakMemoryUsageBytes = peakMemory;
        TotalMemoryUsageBytes = avgMemory;
        LastOperationTime = lastOpTime;
    }

    public EnhancedOperationMetrics RecordSuccess(double latencyMs, long memoryUsed)
    {
        var newTotal = TotalOperations + 1;
        var newAvgLatency = (AverageLatencyMs * TotalOperations + latencyMs) / newTotal;
        var newPeakLatency = Math.Max(PeakLatencyMs, latencyMs);
        var newTotalMemory = TotalMemoryUsageBytes + memoryUsed;
        var newAvgMemory = newTotalMemory / newTotal;
        var newPeakMemory = Math.Max(PeakMemoryUsageBytes, memoryUsed);

        return new EnhancedOperationMetrics(OperationName, newTotal, TotalErrors, newAvgLatency, newPeakLatency, newAvgMemory, newPeakMemory, DateTime.UtcNow)
        {
            TotalMemoryUsageBytes = newTotalMemory
        };
    }

    public EnhancedOperationMetrics RecordError(double latencyMs)
    {
        var newTotal = TotalOperations + 1;
        var newErrors = TotalErrors + 1;
        var newAvgLatency = (AverageLatencyMs * TotalOperations + latencyMs) / newTotal;
        var newPeakLatency = Math.Max(PeakLatencyMs, latencyMs);

        return new EnhancedOperationMetrics(OperationName, newTotal, newErrors, newAvgLatency, newPeakLatency, AverageMemoryUsageBytes, PeakMemoryUsageBytes, DateTime.UtcNow)
        {
            TotalMemoryUsageBytes = TotalMemoryUsageBytes
        };
    }
}

// Additional record types
public record PerformanceAlertConfig();
public record PerformanceAlert();
public record MetricHistory(string Name, string? Unit)
{
    public void AddValue(double value, DateTime timestamp) { }
    public List<MetricValue> GetRecentValues(TimeSpan timeSpan) => new();
}
public record MetricValue(double Value, DateTime Timestamp);
public record EnhancedPerformanceReport(DateTime ReportTime, PerformanceReportSummary Summary, List<EnhancedOperationMetrics> Operations, List<MetricHistory> Metrics, List<PerformanceAlert> Alerts);
public record PerformanceReportSummary(int TotalOperations, double AverageLatencyMs, int TotalErrors, double AverageErrorRate, long TotalMemoryUsage, SystemPerformanceSnapshot SystemSnapshot);
public record PerformanceDashboard(DateTime Timestamp, List<EnhancedOperationMetrics> RecentOperations, List<MetricValue> RecentMetrics, SystemPerformanceSnapshot SystemSnapshot, List<PerformanceAlert> ActiveAlerts);
public record SystemPerformanceSnapshot(DateTime Timestamp, long WorkingSetBytes, long ManagedMemoryBytes, int ThreadCount, int ProcessorCount);
