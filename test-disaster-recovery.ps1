# SmaTrendFollower Disaster Recovery Testing Script
# Automated testing of backup and recovery procedures

param(
    [ValidateSet("All", "ConfigCorruption", "DatabaseCorruption", "StateCorruption", "CompleteSystemFailure")]
    [string]$Scenario = "All",
    [switch]$FullDrill,
    [switch]$GenerateReport,
    [switch]$Verbose
)

# Color output functions
function Write-Info { param($Message) Write-Host $Message -ForegroundColor Cyan }
function Write-Success { param($Message) Write-Host $Message -ForegroundColor Green }
function Write-Warning { param($Message) Write-Host $Message -ForegroundColor Yellow }
function Write-Error { param($Message) Write-Host $Message -ForegroundColor Red }

$startTime = Get-Date
$testResults = @{
    Overall = $true
    Scenarios = @()
    StartTime = $startTime
}

function Add-ScenarioResult {
    param($ScenarioName, $Passed, $Duration, $Details)
    
    $result = @{
        ScenarioName = $ScenarioName
        Passed = $Passed
        Duration = $Duration
        Details = $Details
        Timestamp = Get-Date
    }
    
    $testResults.Scenarios += $result
    if (-not $Passed) { $testResults.Overall = $false }
    
    if ($Passed) {
        Write-Success "✅ $ScenarioName completed successfully ($($Duration.TotalSeconds.ToString('F1'))s)"
    } else {
        Write-Error "❌ $ScenarioName failed ($($Duration.TotalSeconds.ToString('F1'))s)"
    }
}

function Test-ConfigurationCorruption {
    Write-Info "`n🧪 Testing Configuration Corruption Recovery"
    $scenarioStart = Get-Date
    $details = @()
    
    try {
        # 1. Create backup of current configuration
        Write-Info "  📋 Creating configuration backup..."
        if (Test-Path ".env") {
            Copy-Item ".env" ".env.dr_test_backup"
            $details += "Original .env backed up"
        }
        
        # 2. Create a backup using our backup system
        Write-Info "  💾 Creating system backup..."
        $backupPath = ".\backups\dr_test_$(Get-Date -Format 'yyyyMMdd_HHmmss')"
        & .\backup-system.ps1 -BackupPath $backupPath -Verbose:$false
        if ($LASTEXITCODE -ne 0) { throw "Backup creation failed" }
        $details += "System backup created at $backupPath"
        
        # 3. Corrupt configuration
        Write-Info "  💥 Simulating configuration corruption..."
        "CORRUPTED_CONFIG=true`nINVALID_SYNTAX=" | Out-File ".env" -Encoding UTF8
        $details += "Configuration corrupted"
        
        # 4. Verify corruption causes failure
        Write-Info "  🔍 Verifying system fails with corrupted config..."
        $validateResult = dotnet run --project SmaTrendFollower.Console -- validate 2>&1
        if ($LASTEXITCODE -eq 0) { 
            throw "System should have failed with corrupted config"
        }
        $details += "System correctly failed with corrupted config"
        
        # 5. Perform recovery
        Write-Info "  🔧 Performing configuration recovery..."
        & .\restore-system.ps1 -BackupPath $backupPath -ConfigOnly -Force -Verbose:$false
        if ($LASTEXITCODE -ne 0) { throw "Configuration recovery failed" }
        $details += "Configuration recovery completed"
        
        # 6. Verify recovery success
        Write-Info "  ✅ Verifying recovery success..."
        $validateResult = dotnet run --project SmaTrendFollower.Console -- validate 2>&1
        if ($LASTEXITCODE -ne 0) { 
            throw "System validation failed after recovery"
        }
        $details += "System validation passed after recovery"
        
        # 7. Cleanup
        Remove-Item $backupPath -Recurse -Force -ErrorAction SilentlyContinue
        Remove-Item ".env.dr_test_backup" -ErrorAction SilentlyContinue
        
        $duration = (Get-Date) - $scenarioStart
        Add-ScenarioResult "Configuration Corruption Recovery" $true $duration $details
        
    } catch {
        # Restore original configuration
        if (Test-Path ".env.dr_test_backup") {
            Copy-Item ".env.dr_test_backup" ".env" -Force
            Remove-Item ".env.dr_test_backup" -ErrorAction SilentlyContinue
        }
        
        # Cleanup
        if (Test-Path $backupPath) {
            Remove-Item $backupPath -Recurse -Force -ErrorAction SilentlyContinue
        }
        
        $duration = (Get-Date) - $scenarioStart
        $details += "ERROR: $($_.Exception.Message)"
        Add-ScenarioResult "Configuration Corruption Recovery" $false $duration $details
    }
}

function Test-DatabaseCorruption {
    Write-Info "`n🧪 Testing Database Corruption Recovery"
    $scenarioStart = Get-Date
    $details = @()
    
    try {
        # 1. Create backup of current database
        Write-Info "  📋 Creating database backup..."
        $dbBackups = @()
        if (Test-Path "stock_cache.db") {
            Copy-Item "stock_cache.db" "stock_cache.dr_test_backup.db"
            $dbBackups += "stock_cache.db"
        }
        if (Test-Path "index_cache.db") {
            Copy-Item "index_cache.db" "index_cache.dr_test_backup.db"
            $dbBackups += "index_cache.db"
        }
        $details += "Original databases backed up: $($dbBackups -join ', ')"
        
        # 2. Create system backup
        Write-Info "  💾 Creating system backup..."
        $backupPath = ".\backups\dr_test_db_$(Get-Date -Format 'yyyyMMdd_HHmmss')"
        & .\backup-system.ps1 -BackupPath $backupPath -Verbose:$false
        if ($LASTEXITCODE -ne 0) { throw "Backup creation failed" }
        $details += "System backup created"
        
        # 3. Corrupt database
        Write-Info "  💥 Simulating database corruption..."
        foreach ($db in $dbBackups) {
            "CORRUPTED_DATABASE_FILE" | Out-File $db -Encoding UTF8
        }
        $details += "Databases corrupted"
        
        # 4. Perform recovery
        Write-Info "  🔧 Performing database recovery..."
        & .\restore-system.ps1 -BackupPath $backupPath -DatabaseOnly -Force -Verbose:$false
        if ($LASTEXITCODE -ne 0) { throw "Database recovery failed" }
        $details += "Database recovery completed"
        
        # 5. Verify recovery success
        Write-Info "  ✅ Verifying database integrity..."
        foreach ($db in $dbBackups) {
            if (Test-Path $db) {
                try {
                    $integrityCheck = sqlite3 $db "PRAGMA integrity_check;" 2>$null
                    if ($integrityCheck -ne "ok") {
                        throw "Database $db integrity check failed"
                    }
                } catch {
                    # If sqlite3 command not available, just check file size
                    $size = (Get-Item $db).Length
                    if ($size -lt 1000) { # Corrupted file would be very small
                        throw "Database $db appears to still be corrupted"
                    }
                }
            }
        }
        $details += "Database integrity verified"
        
        # 6. Cleanup
        Remove-Item $backupPath -Recurse -Force -ErrorAction SilentlyContinue
        foreach ($db in $dbBackups) {
            Remove-Item "$($db.Replace('.db', '.dr_test_backup.db'))" -ErrorAction SilentlyContinue
        }
        
        $duration = (Get-Date) - $scenarioStart
        Add-ScenarioResult "Database Corruption Recovery" $true $duration $details
        
    } catch {
        # Restore original databases
        foreach ($db in $dbBackups) {
            $backupFile = $db.Replace('.db', '.dr_test_backup.db')
            if (Test-Path $backupFile) {
                Copy-Item $backupFile $db -Force
                Remove-Item $backupFile -ErrorAction SilentlyContinue
            }
        }
        
        # Cleanup
        if (Test-Path $backupPath) {
            Remove-Item $backupPath -Recurse -Force -ErrorAction SilentlyContinue
        }
        
        $duration = (Get-Date) - $scenarioStart
        $details += "ERROR: $($_.Exception.Message)"
        Add-ScenarioResult "Database Corruption Recovery" $false $duration $details
    }
}

function Test-StateCorruption {
    Write-Info "`n🧪 Testing State Corruption Recovery"
    $scenarioStart = Get-Date
    $details = @()
    
    try {
        # 1. Create backup of current state
        Write-Info "  📋 Creating state backup..."
        if (Test-Path "Data\state_backup.json") {
            Copy-Item "Data\state_backup.json" "Data\state_backup.dr_test_backup.json"
            $details += "Original state backup preserved"
        }
        
        # 2. Create system backup
        Write-Info "  💾 Creating system backup..."
        $backupPath = ".\backups\dr_test_state_$(Get-Date -Format 'yyyyMMdd_HHmmss')"
        & .\backup-system.ps1 -BackupPath $backupPath -Verbose:$false
        if ($LASTEXITCODE -ne 0) { throw "Backup creation failed" }
        $details += "System backup created"
        
        # 3. Corrupt state
        Write-Info "  💥 Simulating state corruption..."
        if (-not (Test-Path "Data")) { New-Item -ItemType Directory -Path "Data" -Force | Out-Null }
        "CORRUPTED_STATE_FILE" | Out-File "Data\state_backup.json" -Encoding UTF8
        $details += "State file corrupted"
        
        # 4. Perform recovery
        Write-Info "  🔧 Performing state recovery..."
        & .\restore-system.ps1 -BackupPath $backupPath -StateOnly -Force -Verbose:$false
        if ($LASTEXITCODE -ne 0) { throw "State recovery failed" }
        $details += "State recovery completed"
        
        # 5. Verify recovery success
        Write-Info "  ✅ Verifying state file integrity..."
        if (Test-Path "Data\state_backup.json") {
            try {
                $content = Get-Content "Data\state_backup.json" -Raw
                if ($content -eq "CORRUPTED_STATE_FILE") {
                    throw "State file was not properly recovered"
                }
                # Try to parse as JSON if it's not empty
                if ($content.Trim()) {
                    $content | ConvertFrom-Json | Out-Null
                }
                $details += "State file integrity verified"
            } catch {
                throw "State file is not valid JSON after recovery"
            }
        } else {
            $details += "State file not found after recovery (acceptable if backup was empty)"
        }
        
        # 6. Cleanup
        Remove-Item $backupPath -Recurse -Force -ErrorAction SilentlyContinue
        Remove-Item "Data\state_backup.dr_test_backup.json" -ErrorAction SilentlyContinue
        
        $duration = (Get-Date) - $scenarioStart
        Add-ScenarioResult "State Corruption Recovery" $true $duration $details
        
    } catch {
        # Restore original state
        if (Test-Path "Data\state_backup.dr_test_backup.json") {
            Copy-Item "Data\state_backup.dr_test_backup.json" "Data\state_backup.json" -Force
            Remove-Item "Data\state_backup.dr_test_backup.json" -ErrorAction SilentlyContinue
        }
        
        # Cleanup
        if (Test-Path $backupPath) {
            Remove-Item $backupPath -Recurse -Force -ErrorAction SilentlyContinue
        }
        
        $duration = (Get-Date) - $scenarioStart
        $details += "ERROR: $($_.Exception.Message)"
        Add-ScenarioResult "State Corruption Recovery" $false $duration $details
    }
}

function Test-CompleteSystemRecovery {
    Write-Info "`n🧪 Testing Complete System Recovery"
    $scenarioStart = Get-Date
    $details = @()
    
    try {
        # 1. Create complete system backup
        Write-Info "  💾 Creating complete system backup..."
        $backupPath = ".\backups\dr_test_complete_$(Get-Date -Format 'yyyyMMdd_HHmmss')"
        & .\backup-system.ps1 -BackupPath $backupPath -IncludeLogs -Verbose:$false
        if ($LASTEXITCODE -ne 0) { throw "Complete backup creation failed" }
        $details += "Complete system backup created"
        
        # 2. Verify backup integrity
        Write-Info "  🔍 Verifying backup integrity..."
        & .\verify-backup.ps1 -BackupPath $backupPath -Detailed -Verbose:$false
        if ($LASTEXITCODE -ne 0) { throw "Backup verification failed" }
        $details += "Backup integrity verified"
        
        # 3. Test complete recovery (dry run)
        Write-Info "  🔧 Testing complete recovery (dry run)..."
        & .\restore-system.ps1 -BackupPath $backupPath -DryRun -Verbose:$false
        if ($LASTEXITCODE -ne 0) { throw "Dry run recovery failed" }
        $details += "Dry run recovery successful"
        
        # 4. Cleanup
        Remove-Item $backupPath -Recurse -Force -ErrorAction SilentlyContinue
        
        $duration = (Get-Date) - $scenarioStart
        Add-ScenarioResult "Complete System Recovery" $true $duration $details
        
    } catch {
        # Cleanup
        if (Test-Path $backupPath) {
            Remove-Item $backupPath -Recurse -Force -ErrorAction SilentlyContinue
        }
        
        $duration = (Get-Date) - $scenarioStart
        $details += "ERROR: $($_.Exception.Message)"
        Add-ScenarioResult "Complete System Recovery" $false $duration $details
    }
}

# Main execution
Write-Success "🧪 Starting Disaster Recovery Testing"
Write-Info "Scenario: $Scenario"
Write-Info "Full Drill: $FullDrill"

try {
    # Run selected scenarios
    switch ($Scenario) {
        "ConfigCorruption" { Test-ConfigurationCorruption }
        "DatabaseCorruption" { Test-DatabaseCorruption }
        "StateCorruption" { Test-StateCorruption }
        "CompleteSystemFailure" { Test-CompleteSystemRecovery }
        "All" {
            Test-ConfigurationCorruption
            Test-DatabaseCorruption
            Test-StateCorruption
            Test-CompleteSystemRecovery
        }
    }
    
    # Generate report if requested
    if ($GenerateReport) {
        Write-Info "`n📋 Generating disaster recovery test report..."
        
        $testResults.EndTime = Get-Date
        $testResults.TotalDuration = $testResults.EndTime - $testResults.StartTime
        
        $report = @{
            TestInfo = @{
                TestDate = Get-Date -Format "yyyy-MM-dd HH:mm:ss"
                Scenario = $Scenario
                FullDrill = $FullDrill
                TotalDuration = $testResults.TotalDuration.ToString()
                OverallResult = $testResults.Overall
            }
            Results = $testResults.Scenarios
            Summary = @{
                TotalScenarios = $testResults.Scenarios.Count
                PassedScenarios = ($testResults.Scenarios | Where-Object { $_.Passed }).Count
                FailedScenarios = ($testResults.Scenarios | Where-Object { -not $_.Passed }).Count
                AverageDuration = if ($testResults.Scenarios.Count -gt 0) {
                    ($testResults.Scenarios | Measure-Object -Property Duration -Average).Average.TotalSeconds.ToString('F1') + "s"
                } else { "N/A" }
            }
        }
        
        $reportPath = ".\backups\dr_test_report_$(Get-Date -Format 'yyyyMMdd_HHmmss').json"
        if (-not (Test-Path ".\backups")) { New-Item -ItemType Directory -Path ".\backups" -Force | Out-Null }
        
        $report | ConvertTo-Json -Depth 4 | Out-File $reportPath -Encoding UTF8
        Write-Success "✅ Test report saved to: $reportPath"
    }
    
} catch {
    Write-Error "❌ DISASTER RECOVERY TEST FAILED: $($_.Exception.Message)"
    $testResults.Overall = $false
} finally {
    # Final summary
    $endTime = Get-Date
    $totalDuration = $endTime - $startTime
    
    Write-Info "`n" + "="*60
    if ($testResults.Overall) {
        Write-Success "🎉 DISASTER RECOVERY TESTING PASSED!"
    } else {
        Write-Error "❌ DISASTER RECOVERY TESTING FAILED!"
    }
    
    Write-Info "📊 Test Summary:"
    Write-Info "  Scenarios Tested: $($testResults.Scenarios.Count)"
    Write-Info "  Passed: $(($testResults.Scenarios | Where-Object { $_.Passed }).Count)"
    Write-Info "  Failed: $(($testResults.Scenarios | Where-Object { -not $_.Passed }).Count)"
    Write-Info "  Total Duration: $($totalDuration.ToString('mm\:ss'))"
    
    if (-not $testResults.Overall) {
        Write-Info "`n❌ Failed Scenarios:"
        $testResults.Scenarios | Where-Object { -not $_.Passed } | ForEach-Object {
            Write-Error "  - $($_.ScenarioName)"
        }
    }
    
    exit $(if ($testResults.Overall) { 0 } else { 1 })
}
