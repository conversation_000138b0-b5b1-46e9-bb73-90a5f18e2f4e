#!/usr/bin/env pwsh
<#
.SYNOPSIS
    Setup script for SmaTrendFollower remote environment for Augment Agent
.DESCRIPTION
    This script prepares the remote environment by:
    - Checking prerequisites (.NET 8 SDK)
    - Restoring NuGet packages
    - Building the solution
    - Running tests
    - Setting up environment variables
    - Creating necessary directories
    - Validating the setup
.PARAMETER SkipTests
    Skip running tests during setup
.PARAMETER Verbose
    Enable verbose output
.EXAMPLE
    .\setup-remote-environment.ps1
.EXAMPLE
    .\setup-remote-environment.ps1 -SkipTests -Verbose
#>

param(
    [switch]$SkipTests,
    [switch]$Verbose
)

# Set error action preference
$ErrorActionPreference = "Stop"

# Enable verbose output if requested
if ($Verbose) {
    $VerbosePreference = "Continue"
}

Write-Host "Setting up SmaTrendFollower remote environment for Augment Agent..." -ForegroundColor Green

# Function to check if command exists
function Test-Command {
    param([string]$Command)
    try {
        Get-Command $Command -ErrorAction Stop | Out-Null
        return $true
    }
    catch {
        return $false
    }
}

# Function to run command with error handling
function Invoke-SafeCommand {
    param(
        [string]$Command,
        [string]$Arguments = "",
        [string]$Description = "",
        [switch]$ContinueOnError
    )
    
    Write-Host "📋 $Description" -ForegroundColor Cyan
    Write-Verbose "Executing: $Command $Arguments"
    
    try {
        if ($Arguments) {
            $result = & $Command $Arguments.Split(' ')
        } else {
            $result = & $Command
        }
        
        if ($LASTEXITCODE -ne 0 -and -not $ContinueOnError) {
            throw "Command failed with exit code $LASTEXITCODE"
        }
        
        Write-Host "✅ $Description completed successfully" -ForegroundColor Green
        return $result
    }
    catch {
        Write-Host "❌ $Description failed: $($_.Exception.Message)" -ForegroundColor Red
        if (-not $ContinueOnError) {
            throw
        }
    }
}

# Step 1: Check Prerequisites
Write-Host "`n🔍 Checking Prerequisites..." -ForegroundColor Yellow

# Check .NET 8 SDK
if (-not (Test-Command "dotnet")) {
    Write-Host "❌ .NET SDK not found. Please install .NET 8 SDK from https://dotnet.microsoft.com/download" -ForegroundColor Red
    exit 1
}

$dotnetVersion = dotnet --version
Write-Host "✅ .NET SDK version: $dotnetVersion" -ForegroundColor Green

# Verify .NET 8 is available
$dotnetInfo = dotnet --info
if ($dotnetInfo -notmatch "8\.0\.\d+") {
    Write-Host "⚠️  .NET 8 SDK not detected. Current version: $dotnetVersion" -ForegroundColor Yellow
    Write-Host "Please ensure .NET 8 SDK is installed for optimal compatibility" -ForegroundColor Yellow
}

# Check PowerShell version
Write-Host "✅ PowerShell version: $($PSVersionTable.PSVersion)" -ForegroundColor Green

# Step 2: Validate Project Structure
Write-Host "`n📁 Validating Project Structure..." -ForegroundColor Yellow

$requiredFiles = @(
    "SmaTrendFollower.sln",
    "SmaTrendFollower.Console/SmaTrendFollower.Console.csproj",
    "SmaTrendFollower.Tests/SmaTrendFollower.Tests.csproj",
    ".env"
)

foreach ($file in $requiredFiles) {
    if (Test-Path $file) {
        Write-Host "✅ Found: $file" -ForegroundColor Green
    } else {
        Write-Host "❌ Missing: $file" -ForegroundColor Red
        exit 1
    }
}

# Step 3: Create Required Directories
Write-Host "`n📂 Creating Required Directories..." -ForegroundColor Yellow

$directories = @("logs", "data", "cache")
foreach ($dir in $directories) {
    if (-not (Test-Path $dir)) {
        New-Item -ItemType Directory -Path $dir -Force | Out-Null
        Write-Host "✅ Created directory: $dir" -ForegroundColor Green
    } else {
        Write-Host "✅ Directory exists: $dir" -ForegroundColor Green
    }
}

# Step 4: Environment Variables Setup
Write-Host "`n🔧 Setting up Environment Variables..." -ForegroundColor Yellow

if (Test-Path ".env") {
    Write-Host "✅ .env file found" -ForegroundColor Green
    
    # Load and validate .env file
    $envContent = Get-Content ".env"
    $requiredEnvVars = @("APCA_API_KEY_ID", "APCA_API_SECRET_KEY", "APCA_API_ENV", "POLY_API_KEY")
    
    foreach ($envVar in $requiredEnvVars) {
        $found = $envContent | Where-Object { $_ -match "^$envVar=" }
        if ($found) {
            Write-Host "✅ Environment variable configured: $envVar" -ForegroundColor Green
        } else {
            Write-Host "⚠️  Environment variable missing: $envVar" -ForegroundColor Yellow
        }
    }
} else {
    Write-Host "⚠️  .env file not found. Creating template..." -ForegroundColor Yellow
    $envTemplate = @"
# Alpaca API Configuration
APCA_API_KEY_ID=your_alpaca_key_here
APCA_API_SECRET_KEY=your_alpaca_secret_here
APCA_API_ENV=paper

# Polygon API Configuration
POLY_API_KEY=your_polygon_key_here

# Trading Configuration (optional)
# MAX_POSITION_SIZE_PERCENT=0.05
# STOP_LOSS_PERCENT=0.02
# TAKE_PROFIT_PERCENT=0.06
"@
    $envTemplate | Out-File -FilePath ".env" -Encoding UTF8
    Write-Host "📝 Created .env template. Please configure with your API keys." -ForegroundColor Yellow
}

# Step 5: Clean Previous Builds
Write-Host "`n🧹 Cleaning Previous Builds..." -ForegroundColor Yellow
Invoke-SafeCommand -Command "dotnet" -Arguments "clean" -Description "Cleaning solution"

# Step 6: Restore NuGet Packages
Write-Host "`n📦 Restoring NuGet Packages..." -ForegroundColor Yellow
Invoke-SafeCommand -Command "dotnet" -Arguments "restore" -Description "Restoring NuGet packages"

# Step 7: Build Solution
Write-Host "`n🔨 Building Solution..." -ForegroundColor Yellow
Invoke-SafeCommand -Command "dotnet" -Arguments "build --configuration Release --no-restore" -Description "Building solution in Release mode"

# Step 8: Run Tests (if not skipped)
if (-not $SkipTests) {
    Write-Host "`n🧪 Running Tests..." -ForegroundColor Yellow
    Invoke-SafeCommand -Command "dotnet" -Arguments "test --configuration Release --no-build --verbosity normal" -Description "Running unit tests" -ContinueOnError
} else {
    Write-Host "`n⏭️  Skipping tests as requested" -ForegroundColor Yellow
}

# Step 9: Validate Setup
Write-Host "`n✅ Validating Setup..." -ForegroundColor Yellow

# Check if executables were built
$consoleExe = "SmaTrendFollower.Console/bin/Release/net8.0/SmaTrendFollower.Console.dll"
if (Test-Path $consoleExe) {
    Write-Host "✅ Console application built successfully" -ForegroundColor Green
} else {
    Write-Host "❌ Console application build failed" -ForegroundColor Red
}

# Test basic functionality (dry run)
Write-Host "`n🔍 Testing Basic Functionality..." -ForegroundColor Yellow
try {
    $testResult = dotnet run --project SmaTrendFollower.Console --configuration Release -- --help 2>&1
    if ($LASTEXITCODE -eq 0 -or $testResult -match "help|usage|options") {
        Write-Host "✅ Application can start successfully" -ForegroundColor Green
    } else {
        Write-Host "⚠️  Application may have startup issues" -ForegroundColor Yellow
    }
} catch {
    Write-Host "⚠️  Could not test application startup: $($_.Exception.Message)" -ForegroundColor Yellow
}

# Step 10: Setup Summary
Write-Host "`n📋 Setup Summary" -ForegroundColor Magenta
Write-Host "===========================================" -ForegroundColor Magenta
Write-Host "✅ .NET SDK: Available" -ForegroundColor Green
Write-Host "✅ Project Structure: Valid" -ForegroundColor Green
Write-Host "✅ Dependencies: Restored" -ForegroundColor Green
Write-Host "✅ Build: Successful" -ForegroundColor Green
Write-Host "✅ Directories: Created" -ForegroundColor Green

if (-not $SkipTests) {
    Write-Host "✅ Tests: Executed" -ForegroundColor Green
}

Write-Host "`n🎉 Remote environment setup completed successfully!" -ForegroundColor Green
Write-Host "`n📚 Available Commands for Augment Agent:" -ForegroundColor Cyan
Write-Host "  • Build:           dotnet build" -ForegroundColor White
Write-Host "  • Test:            dotnet test" -ForegroundColor White
Write-Host "  • Run Console:     dotnet run --project SmaTrendFollower.Console" -ForegroundColor White
Write-Host "  • Clean:           dotnet clean" -ForegroundColor White
Write-Host "  • Restore:         dotnet restore" -ForegroundColor White

Write-Host "`n📁 Project Structure:" -ForegroundColor Cyan
Write-Host "  • SmaTrendFollower.Console/    - Main trading application" -ForegroundColor White
Write-Host "  • SmaTrendFollower.Tests/      - Unit and integration tests" -ForegroundColor White
Write-Host "  • logs/                        - Application logs" -ForegroundColor White
Write-Host "  • data/                        - Data files" -ForegroundColor White
Write-Host "  • cache/                       - Cache files" -ForegroundColor White

Write-Host "`n🔧 Environment Configuration:" -ForegroundColor Cyan
Write-Host "  • .env file contains API keys and configuration" -ForegroundColor White
Write-Host "  • Ensure API keys are properly configured before trading" -ForegroundColor White

Write-Host "`n⚠️  Important Notes:" -ForegroundColor Yellow
Write-Host "  • This is a live trading system - use with caution" -ForegroundColor Red
Write-Host "  • Always test changes thoroughly before deployment" -ForegroundColor Yellow
Write-Host "  • Monitor logs for any issues during operation" -ForegroundColor Yellow

Write-Host "`n🚀 Environment is ready for Augment Agent!" -ForegroundColor Green
