# SmaTrendFollower Monitoring Startup Script
# Starts all monitoring services and opens dashboard

param(
    [switch]$WebDashboard,
    [switch]$ConsoleDashboard,
    [switch]$Both,
    [int]$Port = 8080,
    [switch]$OpenBrowser
)

Write-Host "🚀 SmaTrendFollower Monitoring Startup" -ForegroundColor Green
Write-Host "=======================================" -ForegroundColor Green

# Function to check if port is available
function Test-Port {
    param([int]$Port)
    try {
        $listener = [System.Net.Sockets.TcpListener]::new([System.Net.IPAddress]::Any, $Port)
        $listener.Start()
        $listener.Stop()
        return $true
    } catch {
        return $false
    }
}

# Function to start web dashboard
function Start-WebDashboard {
    Write-Host "`n🌐 Starting Web Dashboard..." -ForegroundColor Cyan
    
    # Check if port is available
    if (-not (Test-Port -Port $Port)) {
        Write-Host "❌ Port $Port is already in use" -ForegroundColor Red
        $Port = $Port + 1
        Write-Host "🔄 Trying port $Port instead..." -ForegroundColor Yellow
    }
    
    # Set environment variable for metrics API port
    $env:METRICS_API_PORT = $Port
    
    # Start the metrics API service
    Write-Host "Starting metrics API service on port $Port..." -ForegroundColor Yellow
    $webProcess = Start-Process -FilePath "dotnet" -ArgumentList @(
        "run", 
        "--project", "SmaTrendFollower.Console",
        "--", "metrics-api"
    ) -PassThru -WindowStyle Hidden
    
    if ($webProcess) {
        Write-Host "✅ Web dashboard started (PID: $($webProcess.Id))" -ForegroundColor Green
        Write-Host "📊 Dashboard URL: http://localhost:$Port/dashboard" -ForegroundColor Cyan
        Write-Host "🔗 API Docs: http://localhost:$Port/" -ForegroundColor Cyan
        
        # Wait a moment for service to start
        Start-Sleep -Seconds 3
        
        # Test if service is responding
        try {
            $response = Invoke-WebRequest -Uri "http://localhost:$Port/health" -TimeoutSec 5 -UseBasicParsing
            if ($response.StatusCode -eq 200) {
                Write-Host "✅ Web dashboard is responding" -ForegroundColor Green
            }
        } catch {
            Write-Host "⚠️  Web dashboard may still be starting..." -ForegroundColor Yellow
        }
        
        if ($OpenBrowser) {
            Write-Host "🌐 Opening dashboard in browser..." -ForegroundColor Cyan
            Start-Process "http://localhost:$Port/dashboard"
        }
        
        return $webProcess
    } else {
        Write-Host "❌ Failed to start web dashboard" -ForegroundColor Red
        return $null
    }
}

# Function to start console dashboard
function Start-ConsoleDashboard {
    Write-Host "`n💻 Starting Console Dashboard..." -ForegroundColor Cyan
    
    $consoleProcess = Start-Process -FilePath "powershell" -ArgumentList @(
        "-File", "monitoring-dashboard.ps1",
        "-Continuous",
        "-RefreshInterval", "30"
    ) -PassThru
    
    if ($consoleProcess) {
        Write-Host "✅ Console dashboard started (PID: $($consoleProcess.Id))" -ForegroundColor Green
        return $consoleProcess
    } else {
        Write-Host "❌ Failed to start console dashboard" -ForegroundColor Red
        return $null
    }
}

# Function to show monitoring options
function Show-MonitoringOptions {
    Write-Host "`n📊 Monitoring Options:" -ForegroundColor Yellow
    Write-Host "1. Web Dashboard - Modern web interface with real-time updates" -ForegroundColor White
    Write-Host "2. Console Dashboard - PowerShell-based terminal interface" -ForegroundColor White
    Write-Host "3. Both - Start both web and console dashboards" -ForegroundColor White
    Write-Host ""
    
    $choice = Read-Host "Select option (1-3)"
    
    switch ($choice) {
        "1" { return "web" }
        "2" { return "console" }
        "3" { return "both" }
        default { 
            Write-Host "Invalid choice. Starting web dashboard by default." -ForegroundColor Yellow
            return "web" 
        }
    }
}

# Main execution
try {
    # Determine what to start
    $startMode = "web"  # Default
    
    if ($WebDashboard) { $startMode = "web" }
    elseif ($ConsoleDashboard) { $startMode = "console" }
    elseif ($Both) { $startMode = "both" }
    else {
        # Interactive mode
        $startMode = Show-MonitoringOptions
    }
    
    $processes = @()
    
    # Start requested dashboards
    switch ($startMode) {
        "web" {
            $webProc = Start-WebDashboard
            if ($webProc) { $processes += $webProc }
        }
        "console" {
            $consoleProc = Start-ConsoleDashboard
            if ($consoleProc) { $processes += $consoleProc }
        }
        "both" {
            $webProc = Start-WebDashboard
            if ($webProc) { $processes += $webProc }
            
            Start-Sleep -Seconds 2
            
            $consoleProc = Start-ConsoleDashboard
            if ($consoleProc) { $processes += $consoleProc }
        }
    }
    
    if ($processes.Count -eq 0) {
        Write-Host "❌ No monitoring services started successfully" -ForegroundColor Red
        exit 1
    }
    
    # Show status
    Write-Host "`n✅ Monitoring Services Started:" -ForegroundColor Green
    foreach ($proc in $processes) {
        Write-Host "   PID $($proc.Id): $($proc.ProcessName)" -ForegroundColor White
    }
    
    Write-Host "`n🎮 Controls:" -ForegroundColor Cyan
    Write-Host "   Press 'Q' to quit all monitoring services" -ForegroundColor White
    Write-Host "   Press 'S' to show status" -ForegroundColor White
    Write-Host "   Press 'R' to restart services" -ForegroundColor White
    Write-Host "   Press 'B' to open browser (web dashboard)" -ForegroundColor White
    
    # Monitor processes
    do {
        if ([Console]::KeyAvailable) {
            $key = [Console]::ReadKey($true)
            switch ($key.KeyChar.ToString().ToUpper()) {
                'Q' {
                    Write-Host "`n🛑 Stopping all monitoring services..." -ForegroundColor Yellow
                    foreach ($proc in $processes) {
                        try {
                            if (-not $proc.HasExited) {
                                $proc.Kill()
                                Write-Host "✅ Stopped PID $($proc.Id)" -ForegroundColor Green
                            }
                        } catch {
                            Write-Host "⚠️  Could not stop PID $($proc.Id)" -ForegroundColor Yellow
                        }
                    }
                    Write-Host "👋 Monitoring stopped" -ForegroundColor Green
                    exit 0
                }
                'S' {
                    Write-Host "`n📊 Service Status:" -ForegroundColor Cyan
                    foreach ($proc in $processes) {
                        try {
                            $proc.Refresh()
                            $status = if ($proc.HasExited) { "❌ Stopped" } else { "✅ Running" }
                            Write-Host "   PID $($proc.Id): $status" -ForegroundColor White
                        } catch {
                            Write-Host "   PID $($proc.Id): ❓ Unknown" -ForegroundColor Yellow
                        }
                    }
                }
                'R' {
                    Write-Host "`n🔄 Restarting services..." -ForegroundColor Yellow
                    # Stop existing processes
                    foreach ($proc in $processes) {
                        try {
                            if (-not $proc.HasExited) {
                                $proc.Kill()
                            }
                        } catch { }
                    }
                    
                    # Restart
                    Start-Sleep -Seconds 2
                    & $MyInvocation.MyCommand.Path @PSBoundParameters
                    exit 0
                }
                'B' {
                    if ($startMode -eq "web" -or $startMode -eq "both") {
                        Write-Host "`n🌐 Opening browser..." -ForegroundColor Cyan
                        Start-Process "http://localhost:$Port/dashboard"
                    } else {
                        Write-Host "`n⚠️  Web dashboard not running" -ForegroundColor Yellow
                    }
                }
            }
        }
        
        # Check if any process has exited
        $runningProcesses = $processes | Where-Object { -not $_.HasExited }
        if ($runningProcesses.Count -eq 0) {
            Write-Host "`n⚠️  All monitoring processes have stopped" -ForegroundColor Yellow
            break
        }
        
        Start-Sleep -Milliseconds 100
    } while ($true)
    
} catch {
    Write-Host "`n❌ Error: $($_.Exception.Message)" -ForegroundColor Red
    exit 1
} finally {
    # Cleanup
    foreach ($proc in $processes) {
        try {
            if (-not $proc.HasExited) {
                $proc.Kill()
            }
        } catch { }
    }
}
