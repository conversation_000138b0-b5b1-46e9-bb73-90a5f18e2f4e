using SmaTrendFollower.Models;

namespace SmaTrendFollower.Services;

/// <summary>
/// Service for dynamically building and caching tradeable symbol universes based on liquidity, volatility, and price criteria
/// </summary>
public interface IDynamicUniverseProvider
{
    /// <summary>
    /// Builds a new universe by filtering symbols based on criteria like price, volume, and volatility
    /// </summary>
    /// <param name="candidateSymbols">Optional list of candidate symbols to filter. If null, uses default candidates</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>List of qualified symbols that meet all criteria</returns>
    Task<IEnumerable<string>> BuildUniverseAsync(IEnumerable<string>? candidateSymbols = null, CancellationToken cancellationToken = default);
    
    /// <summary>
    /// Gets the cached universe from Redis
    /// </summary>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Cached universe symbols, or empty list if not found</returns>
    Task<IEnumerable<string>> GetCachedUniverseAsync(CancellationToken cancellationToken = default);
    
    /// <summary>
    /// Gets detailed universe information including metadata
    /// </summary>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Detailed universe information with metadata</returns>
    Task<RedisUniverse?> GetUniverseDetailsAsync(CancellationToken cancellationToken = default);
    
    /// <summary>
    /// Forces a universe rebuild and cache update
    /// </summary>
    /// <param name="candidateSymbols">Optional list of candidate symbols to filter</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Newly built universe</returns>
    Task<IEnumerable<string>> RefreshUniverseAsync(IEnumerable<string>? candidateSymbols = null, CancellationToken cancellationToken = default);
    
    /// <summary>
    /// Checks if the cached universe is still valid (not expired)
    /// </summary>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>True if cache is valid, false if expired or missing</returns>
    Task<bool> IsCacheValidAsync(CancellationToken cancellationToken = default);
    
    /// <summary>
    /// Gets the default candidate symbols for universe building
    /// </summary>
    /// <returns>Default candidate symbol list</returns>
    IEnumerable<string> GetDefaultCandidates();
}
