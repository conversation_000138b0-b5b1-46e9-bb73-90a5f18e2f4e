<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>SmaTrendFollower Live Trading Dashboard</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%);
            color: #ffffff;
            min-height: 100vh;
        }

        .dashboard-container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 20px;
        }

        .header {
            text-align: center;
            margin-bottom: 30px;
            padding: 20px;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 15px;
            backdrop-filter: blur(10px);
        }

        .header h1 {
            font-size: 2.5em;
            margin-bottom: 10px;
            text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
        }

        .status-indicator {
            display: inline-block;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            margin-right: 8px;
            animation: pulse 2s infinite;
        }

        .status-live { background-color: #00ff00; }
        .status-paper { background-color: #ffaa00; }
        .status-error { background-color: #ff0000; }

        @keyframes pulse {
            0% { opacity: 1; }
            50% { opacity: 0.5; }
            100% { opacity: 1; }
        }

        .metrics-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }

        .metric-card {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 15px;
            padding: 20px;
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            transition: transform 0.3s ease;
        }

        .metric-card:hover {
            transform: translateY(-5px);
        }

        .metric-card h3 {
            font-size: 1.2em;
            margin-bottom: 15px;
            color: #ffd700;
            border-bottom: 2px solid #ffd700;
            padding-bottom: 5px;
        }

        .metric-value {
            font-size: 2em;
            font-weight: bold;
            margin: 10px 0;
        }

        .metric-change {
            font-size: 0.9em;
            opacity: 0.8;
        }

        .positive { color: #00ff88; }
        .negative { color: #ff4444; }
        .neutral { color: #ffffff; }

        .chart-container {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 15px;
            padding: 20px;
            margin-bottom: 20px;
            backdrop-filter: blur(10px);
        }

        .alerts-section {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 15px;
            padding: 20px;
            backdrop-filter: blur(10px);
        }

        .alert-item {
            padding: 10px;
            margin: 5px 0;
            border-radius: 8px;
            border-left: 4px solid;
        }

        .alert-high { 
            background: rgba(255, 68, 68, 0.2);
            border-left-color: #ff4444;
        }

        .alert-medium { 
            background: rgba(255, 170, 0, 0.2);
            border-left-color: #ffaa00;
        }

        .alert-low { 
            background: rgba(0, 255, 136, 0.2);
            border-left-color: #00ff88;
        }

        .controls {
            display: flex;
            justify-content: center;
            gap: 15px;
            margin-top: 20px;
        }

        .btn {
            padding: 12px 24px;
            border: none;
            border-radius: 8px;
            font-size: 1em;
            cursor: pointer;
            transition: all 0.3s ease;
            font-weight: bold;
        }

        .btn-primary {
            background: linear-gradient(45deg, #00ff88, #00cc6a);
            color: #000;
        }

        .btn-warning {
            background: linear-gradient(45deg, #ffaa00, #ff8800);
            color: #000;
        }

        .btn-danger {
            background: linear-gradient(45deg, #ff4444, #cc0000);
            color: #fff;
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3);
        }

        .timestamp {
            text-align: center;
            opacity: 0.7;
            margin-top: 20px;
        }

        .loading {
            text-align: center;
            padding: 20px;
            font-style: italic;
            opacity: 0.7;
        }

        .error {
            background: rgba(255, 68, 68, 0.2);
            border: 1px solid #ff4444;
            border-radius: 8px;
            padding: 15px;
            margin: 10px 0;
        }
    </style>
</head>
<body>
    <div class="dashboard-container">
        <div class="header">
            <h1>🚀 SmaTrendFollower Live Trading Dashboard</h1>
            <div id="system-status">
                <span class="status-indicator status-paper"></span>
                <span id="status-text">Initializing...</span>
            </div>
            <div id="last-update" class="timestamp">Last Updated: --</div>
        </div>

        <div class="metrics-grid">
            <div class="metric-card">
                <h3>📊 Account Status</h3>
                <div class="metric-value" id="account-equity">$--</div>
                <div class="metric-change" id="account-change">--</div>
            </div>

            <div class="metric-card">
                <h3>💰 Today's P&L</h3>
                <div class="metric-value" id="daily-pnl">$--</div>
                <div class="metric-change" id="pnl-change">--</div>
            </div>

            <div class="metric-card">
                <h3>📈 Active Positions</h3>
                <div class="metric-value" id="active-positions">--</div>
                <div class="metric-change" id="positions-change">--</div>
            </div>

            <div class="metric-card">
                <h3>🎯 Win Rate</h3>
                <div class="metric-value" id="win-rate">--%</div>
                <div class="metric-change" id="winrate-change">--</div>
            </div>

            <div class="metric-card">
                <h3>⚡ Signals Today</h3>
                <div class="metric-value" id="signals-today">--</div>
                <div class="metric-change" id="signals-change">--</div>
            </div>

            <div class="metric-card">
                <h3>🛡️ Risk Level</h3>
                <div class="metric-value" id="risk-level">--</div>
                <div class="metric-change" id="risk-change">--</div>
            </div>
        </div>

        <div class="chart-container">
            <h3>📈 Market Overview</h3>
            <div id="market-data" class="loading">Loading market data...</div>
        </div>

        <div class="alerts-section">
            <h3>🚨 Recent Alerts</h3>
            <div id="alerts-container" class="loading">Loading alerts...</div>
        </div>

        <div class="controls">
            <button class="btn btn-primary" onclick="refreshDashboard()">🔄 Refresh</button>
            <button class="btn btn-warning" onclick="showMetrics()">📊 Detailed Metrics</button>
            <button class="btn btn-danger" onclick="emergencyStop()">🛑 Emergency Stop</button>
        </div>

        <div class="timestamp" id="footer-timestamp">
            Dashboard v1.0 | Auto-refresh: <span id="auto-refresh-status">ON</span>
        </div>
    </div>

    <script>
        let autoRefresh = true;
        let refreshInterval;

        // Initialize dashboard
        document.addEventListener('DOMContentLoaded', function() {
            refreshDashboard();
            startAutoRefresh();
        });

        function startAutoRefresh() {
            if (refreshInterval) clearInterval(refreshInterval);
            refreshInterval = setInterval(refreshDashboard, 30000); // 30 seconds
            document.getElementById('auto-refresh-status').textContent = 'ON';
        }

        function stopAutoRefresh() {
            if (refreshInterval) clearInterval(refreshInterval);
            document.getElementById('auto-refresh-status').textContent = 'OFF';
        }

        async function refreshDashboard() {
            try {
                updateTimestamp();
                await Promise.all([
                    updateSystemStatus(),
                    updateMetrics(),
                    updateMarketData(),
                    updateAlerts()
                ]);
            } catch (error) {
                console.error('Dashboard refresh failed:', error);
                showError('Failed to refresh dashboard: ' + error.message);
            }
        }

        function updateTimestamp() {
            const now = new Date();
            document.getElementById('last-update').textContent = 
                `Last Updated: ${now.toLocaleString()}`;
            document.getElementById('footer-timestamp').innerHTML = 
                `Dashboard v1.0 | Auto-refresh: <span id="auto-refresh-status">${autoRefresh ? 'ON' : 'OFF'}</span>`;
        }

        async function updateSystemStatus() {
            // Simulate API call - replace with actual metrics API
            const status = await fetchWithFallback('/api/health', {
                status: 'paper',
                message: 'Paper Trading Active'
            });

            const statusElement = document.querySelector('.status-indicator');
            const statusText = document.getElementById('status-text');

            if (status.status === 'live') {
                statusElement.className = 'status-indicator status-live';
                statusText.textContent = '🟢 Live Trading Active';
            } else if (status.status === 'paper') {
                statusElement.className = 'status-indicator status-paper';
                statusText.textContent = '🟡 Paper Trading Active';
            } else {
                statusElement.className = 'status-indicator status-error';
                statusText.textContent = '🔴 System Error';
            }
        }

        async function updateMetrics() {
            const metrics = await fetchWithFallback('/api/metrics', {
                accountEquity: 100000,
                dailyPnL: 250.50,
                activePositions: 3,
                winRate: 65.5,
                signalsToday: 8,
                riskLevel: 'Low'
            });

            document.getElementById('account-equity').textContent = 
                `$${metrics.accountEquity.toLocaleString()}`;
            document.getElementById('daily-pnl').textContent = 
                `$${metrics.dailyPnL.toFixed(2)}`;
            document.getElementById('daily-pnl').className = 
                `metric-value ${metrics.dailyPnL >= 0 ? 'positive' : 'negative'}`;
            document.getElementById('active-positions').textContent = metrics.activePositions;
            document.getElementById('win-rate').textContent = `${metrics.winRate}%`;
            document.getElementById('signals-today').textContent = metrics.signalsToday;
            document.getElementById('risk-level').textContent = metrics.riskLevel;
        }

        async function updateMarketData() {
            const marketData = await fetchWithFallback('/api/market', {
                SPY: { price: 445.50, change: 1.25 },
                VIX: { price: 18.50, change: -0.75 }
            });

            const marketHtml = Object.entries(marketData).map(([symbol, data]) => 
                `<div style="display: inline-block; margin: 10px; padding: 10px; background: rgba(255,255,255,0.1); border-radius: 8px;">
                    <strong>${symbol}</strong>: $${data.price} 
                    <span class="${data.change >= 0 ? 'positive' : 'negative'}">
                        ${data.change >= 0 ? '+' : ''}${data.change}
                    </span>
                </div>`
            ).join('');

            document.getElementById('market-data').innerHTML = marketHtml;
        }

        async function updateAlerts() {
            const alerts = await fetchWithFallback('/api/alerts', [
                { type: 'info', message: 'Signal generated for AAPL', time: '10:30 AM' },
                { type: 'warning', message: 'VIX spike detected', time: '10:25 AM' },
                { type: 'success', message: 'Position closed: +$125', time: '10:20 AM' }
            ]);

            const alertsHtml = alerts.map(alert => 
                `<div class="alert-item alert-${alert.type === 'info' ? 'low' : alert.type === 'warning' ? 'medium' : 'high'}">
                    <strong>${alert.time}</strong>: ${alert.message}
                </div>`
            ).join('');

            document.getElementById('alerts-container').innerHTML = 
                alertsHtml || '<div class="loading">No recent alerts</div>';
        }

        async function fetchWithFallback(url, fallbackData) {
            try {
                const response = await fetch(url);
                if (response.ok) {
                    return await response.json();
                }
            } catch (error) {
                console.warn(`API call failed for ${url}, using fallback data`);
            }
            return fallbackData;
        }

        function showError(message) {
            const errorDiv = document.createElement('div');
            errorDiv.className = 'error';
            errorDiv.textContent = message;
            document.querySelector('.dashboard-container').insertBefore(
                errorDiv, 
                document.querySelector('.metrics-grid')
            );
            setTimeout(() => errorDiv.remove(), 5000);
        }

        function showMetrics() {
            window.open('/metrics.html', '_blank');
        }

        function emergencyStop() {
            if (confirm('⚠️ EMERGENCY STOP\n\nThis will halt all trading immediately.\nAre you sure?')) {
                fetch('/api/emergency-stop', { method: 'POST' })
                    .then(() => alert('Emergency stop activated'))
                    .catch(() => alert('Emergency stop failed - check system manually'));
            }
        }

        // Keyboard shortcuts
        document.addEventListener('keydown', function(e) {
            if (e.key === 'r' || e.key === 'R') {
                e.preventDefault();
                refreshDashboard();
            } else if (e.key === 'e' || e.key === 'E') {
                e.preventDefault();
                emergencyStop();
            } else if (e.key === ' ') {
                e.preventDefault();
                autoRefresh = !autoRefresh;
                if (autoRefresh) {
                    startAutoRefresh();
                } else {
                    stopAutoRefresh();
                }
            }
        });
    </script>
</body>
</html>
