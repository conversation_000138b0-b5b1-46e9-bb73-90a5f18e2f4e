# SmaTrendFollower Simple Backup Verification Script
# Basic verification without complex Unicode handling

param(
    [Parameter(Mandatory=$true)]
    [string]$BackupPath,
    [switch]$Detailed,
    [switch]$ShowDetails
)

function Write-Info { param($Message) Write-Host $Message -ForegroundColor Cyan }
function Write-Success { param($Message) Write-Host $Message -ForegroundColor Green }
function Write-Warning { param($Message) Write-Host $Message -ForegroundColor Yellow }
function Write-Error { param($Message) Write-Host $Message -ForegroundColor Red }

$startTime = Get-Date
$allTestsPassed = $true

function Test-BackupComponent {
    param($ComponentName, $TestResult, $Message)
    
    if ($TestResult) {
        Write-Success "PASS: $ComponentName - $Message"
    } else {
        Write-Error "FAIL: $ComponentName - $Message"
        $script:allTestsPassed = $false
    }
}

Write-Success "Starting SmaTrendFollower Backup Verification"
Write-Info "Backup path: $BackupPath"

try {
    # 1. Basic Existence Check
    Write-Info "`nBasic Existence Checks"
    
    if (Test-Path $BackupPath) {
        Test-BackupComponent "Backup Exists" $true "Backup path found"
    } else {
        Test-BackupComponent "Backup Exists" $false "Backup path not found"
        throw "Backup path does not exist: $BackupPath"
    }
    
    # Handle compressed backups
    $isCompressed = $BackupPath.EndsWith('.zip')
    $workingPath = $BackupPath
    $tempExtractPath = $null
    
    if ($isCompressed) {
        Write-Info "Extracting compressed backup for verification..."
        $tempExtractPath = "$env:TEMP\SmaTrendFollower_Verify_$(Get-Date -Format 'yyyyMMdd_HHmmss')"
        
        try {
            Expand-Archive -Path $BackupPath -DestinationPath $tempExtractPath -Force
            $workingPath = $tempExtractPath
            Test-BackupComponent "Archive Extraction" $true "Successfully extracted compressed backup"
        } catch {
            Test-BackupComponent "Archive Extraction" $false "Failed to extract: $($_.Exception.Message)"
            throw
        }
    }

    # 2. Manifest Verification
    Write-Info "`nManifest Verification"
    
    $manifestPath = "$workingPath\backup_manifest.json"
    if (Test-Path $manifestPath) {
        try {
            $manifest = Get-Content $manifestPath | ConvertFrom-Json
            Test-BackupComponent "Manifest Exists" $true "Backup manifest found and parsed"
            
            if ($ShowDetails -and $manifest.BackupInfo) {
                Write-Info "   Backup Date: $($manifest.BackupInfo.BackupDate)"
                Write-Info "   Total Files: $($manifest.BackupInfo.TotalFiles)"
                Write-Info "   Total Size: $($manifest.BackupInfo.TotalSizeMB) MB"
            }
            
        } catch {
            Test-BackupComponent "Manifest Parse" $false "Failed to parse manifest: $($_.Exception.Message)"
        }
    } else {
        Test-BackupComponent "Manifest Exists" $false "Backup manifest not found"
    }

    # 3. File Structure Verification
    Write-Info "`nFile Structure Verification"
    
    $expectedDirs = @('config', 'database', 'state')
    foreach ($dir in $expectedDirs) {
        $dirPath = "$workingPath\$dir"
        if (Test-Path $dirPath) {
            $fileCount = (Get-ChildItem $dirPath -File -ErrorAction SilentlyContinue).Count
            Test-BackupComponent "Directory $dir" $true "Found with $fileCount files"
        } else {
            Test-BackupComponent "Directory $dir" $false "Directory missing"
        }
    }

    # 4. Configuration File Verification
    Write-Info "`nConfiguration File Verification"
    
    $configFiles = @(
        @{Name = ".env.backup"; Path = "$workingPath\config\.env.backup"; Critical = $true},
        @{Name = "appsettings.backup.json"; Path = "$workingPath\config\appsettings.backup.json"; Critical = $false},
        @{Name = ".env.live.backup"; Path = "$workingPath\config\.env.live.backup"; Critical = $false}
    )
    
    foreach ($config in $configFiles) {
        if (Test-Path $config.Path) {
            $size = (Get-Item $config.Path).Length
            if ($size -gt 0) {
                Test-BackupComponent "Config $($config.Name)" $true "Found ($size bytes)"
                
                # Basic content validation for .env files
                if ($config.Name.Contains('.env') -and $Detailed) {
                    $content = Get-Content $config.Path -Raw
                    $hasApiKeys = $content -match 'APCA_API_KEY_ID|POLY_API_KEY'
                    if ($hasApiKeys) {
                        Test-BackupComponent "Config Content $($config.Name)" $true "Contains API configuration"
                    } else {
                        Test-BackupComponent "Config Content $($config.Name)" $false "Missing API configuration"
                    }
                }
            } else {
                Test-BackupComponent "Config $($config.Name)" $false "File is empty"
            }
        } else {
            $severity = $config.Critical
            $message = if ($config.Critical) { "Critical file missing" } else { "Optional file missing" }
            Test-BackupComponent "Config $($config.Name)" (-not $config.Critical) $message
        }
    }

    # 5. Database File Verification
    Write-Info "`nDatabase File Verification"
    
    $dbFiles = @(
        @{Name = "index_cache.backup.db"; Path = "$workingPath\database\index_cache.backup.db"},
        @{Name = "stock_cache.backup.db"; Path = "$workingPath\database\stock_cache.backup.db"}
    )
    
    foreach ($db in $dbFiles) {
        if (Test-Path $db.Path) {
            $size = (Get-Item $db.Path).Length
            $sizeMB = [math]::Round($size/1MB, 2)
            Test-BackupComponent "Database $($db.Name)" $true "Found ($sizeMB MB)"
            
            # SQLite integrity check if detailed verification requested
            if ($Detailed) {
                try {
                    # Try to open the database file to verify it's not corrupted
                    $testQuery = "SELECT name FROM sqlite_master WHERE type='table' LIMIT 1;"
                    $result = sqlite3 $db.Path $testQuery 2>$null
                    Test-BackupComponent "DB Integrity $($db.Name)" $true "SQLite integrity check passed"
                } catch {
                    Test-BackupComponent "DB Integrity $($db.Name)" $false "Could not perform integrity check"
                }
            }
        } else {
            Test-BackupComponent "Database $($db.Name)" $false "Database file missing"
        }
    }

    # 6. State File Verification
    Write-Info "`nState File Verification"
    
    $stateFiles = @(
        @{Name = "state_backup.json"; Path = "$workingPath\state\state_backup.json"; Type = "JSON"}
    )
    
    foreach ($state in $stateFiles) {
        if (Test-Path $state.Path) {
            $size = (Get-Item $state.Path).Length
            $sizeKB = [math]::Round($size/1KB, 2)
            Test-BackupComponent "State $($state.Name)" $true "Found ($sizeKB KB)"
            
            # JSON validation for JSON files
            if ($state.Type -eq "JSON" -and $Detailed) {
                try {
                    $content = Get-Content $state.Path -Raw | ConvertFrom-Json
                    Test-BackupComponent "State JSON $($state.Name)" $true "Valid JSON structure"
                } catch {
                    Test-BackupComponent "State JSON $($state.Name)" $false "Invalid JSON: $($_.Exception.Message)"
                }
            }
        } else {
            Test-BackupComponent "State $($state.Name)" $true "State file missing (acceptable if no state to backup)"
        }
    }

    # 7. File Count Verification
    Write-Info "`nFile Count Verification"
    
    $actualFiles = Get-ChildItem $workingPath -Recurse -File
    $actualCount = $actualFiles.Count
    
    if ($manifest -and $manifest.BackupInfo.TotalFiles) {
        $expectedCount = $manifest.BackupInfo.TotalFiles
        if ($actualCount -eq $expectedCount) {
            Test-BackupComponent "File Count" $true "Matches manifest ($actualCount files)"
        } else {
            Test-BackupComponent "File Count" $false "Mismatch - Expected: $expectedCount, Found: $actualCount"
        }
    } else {
        Test-BackupComponent "File Count" $true "Found $actualCount files (no manifest to compare)"
    }

    # 8. Size Verification
    Write-Info "`nSize Verification"
    
    $actualSize = ($actualFiles | Measure-Object -Property Length -Sum).Sum
    $actualSizeMB = [math]::Round($actualSize / 1MB, 2)
    
    if ($manifest -and $manifest.BackupInfo.TotalSizeMB) {
        $expectedSizeMB = $manifest.BackupInfo.TotalSizeMB
        $sizeDifference = [math]::Abs($actualSizeMB - $expectedSizeMB)
        
        if ($sizeDifference -lt 1) { # Allow 1MB tolerance
            Test-BackupComponent "Size Verification" $true "Matches manifest ($actualSizeMB MB)"
        } else {
            Test-BackupComponent "Size Verification" $false "Size mismatch - Expected: $expectedSizeMB MB, Found: $actualSizeMB MB"
        }
    } else {
        Test-BackupComponent "Size Verification" $true "Total size: $actualSizeMB MB (no manifest to compare)"
    }

} catch {
    Write-Error "VERIFICATION FAILED: $($_.Exception.Message)"
    $allTestsPassed = $false
} finally {
    # Cleanup temporary extraction
    if ($tempExtractPath -and (Test-Path $tempExtractPath)) {
        Remove-Item $tempExtractPath -Recurse -Force -ErrorAction SilentlyContinue
    }
    
    # Final Summary
    $endTime = Get-Date
    $duration = $endTime - $startTime
    
    Write-Info "`n" + "="*60
    if ($allTestsPassed) {
        Write-Success "BACKUP VERIFICATION PASSED!"
    } else {
        Write-Error "BACKUP VERIFICATION FAILED!"
    }
    
    Write-Info "Duration: $($duration.ToString('mm\:ss'))"
    
    exit $(if ($allTestsPassed) { 0 } else { 1 })
}
