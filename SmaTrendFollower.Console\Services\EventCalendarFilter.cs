using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Configuration;
using System.Text.Json;
using System.Text;
using Alpaca.Markets;

namespace SmaTrendFollower.Services;

/// <summary>
/// Event Calendar Filter - Monitors economic events to pause trading during high-impact periods
/// Uses free economic calendar APIs to track FOMC meetings, earnings, and major announcements
/// </summary>
public sealed class EventCalendarFilter : IEventCalendarFilter
{
    private readonly ILogger<EventCalendarFilter> _logger;
    private readonly ILiveStateStore _stateStore;
    private readonly HttpClient _httpClient;
    private readonly EventCalendarConfig _config;

    public EventCalendarFilter(
        ILogger<EventCalendarFilter> logger,
        ILiveStateStore stateStore,
        HttpClient httpClient,
        IConfiguration configuration)
    {
        _logger = logger;
        _stateStore = stateStore;
        _httpClient = httpClient;
        _config = new EventCalendarConfig
        {
            CacheHours = configuration.GetValue("EVENT_CACHE_HOURS", 4),
            EconomicCalendarApiKey = configuration.GetValue<string>("ECONOMIC_CALENDAR_API_KEY") ?? "",
            LookAheadDays = configuration.GetValue("EVENT_LOOKAHEAD_DAYS", 3),
            HighImpactEvents = configuration.GetSection("EVENT_HIGH_IMPACT").Get<string[]>() ?? 
                new[] { "FOMC", "NFP", "CPI", "GDP", "Earnings", "Fed Speech" },
            EnableEconomicCalendar = configuration.GetValue("EVENT_ENABLE_ECONOMIC_CALENDAR", true),
            EnableEarningsCalendar = configuration.GetValue("EVENT_ENABLE_EARNINGS_CALENDAR", true)
        };
    }

    /// <summary>
    /// Determines if trading is allowed based on upcoming economic events
    /// </summary>
    public async Task<bool> IsEligibleAsync(string symbol, IReadOnlyList<IBar> bars)
    {
        try
        {
            var analysis = await AnalyzeUpcomingEventsAsync(symbol);
            
            var isEligible = analysis.RiskLevel != EventRiskLevel.High && 
                           analysis.RiskLevel != EventRiskLevel.Critical;

            if (!isEligible)
            {
                _logger.LogWarning("Event Calendar filter BLOCKED {Symbol}: {Reason} (Risk: {Risk})",
                    symbol, analysis.Reasoning, analysis.RiskLevel);
            }
            else
            {
                _logger.LogDebug("Event Calendar filter PASSED for {Symbol}: {Reason}",
                    symbol, analysis.Reasoning);
            }

            return isEligible;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error in event calendar analysis for {Symbol}, allowing trade", symbol);
            return true; // Fail open
        }
    }

    /// <summary>
    /// Analyzes upcoming economic events for a symbol
    /// </summary>
    private async Task<EventCalendarAnalysis> AnalyzeUpcomingEventsAsync(string symbol)
    {
        var cacheKey = $"event_calendar_analysis_{symbol}";
        
        // Try cache first
        var cached = await _stateStore.GetMarketStateAsync<EventCalendarAnalysis>(cacheKey);
        if (cached != null && cached.AnalyzedAt > DateTime.UtcNow.AddHours(-_config.CacheHours))
        {
            return cached;
        }

        try
        {
            var events = await GetUpcomingEventsAsync(symbol);
            var analysis = AnalyzeEvents(symbol, events);

            // Cache the result
            await _stateStore.SetMarketStateAsync(cacheKey, analysis, TimeSpan.FromHours(_config.CacheHours));

            return analysis;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to analyze upcoming events for {Symbol}", symbol);
            return new EventCalendarAnalysis(
                symbol,
                EventRiskLevel.Unknown,
                "Error retrieving event calendar",
                new List<EconomicEvent>(),
                DateTime.UtcNow
            );
        }
    }

    /// <summary>
    /// Gets upcoming economic events for a symbol
    /// </summary>
    private async Task<List<EconomicEvent>> GetUpcomingEventsAsync(string symbol)
    {
        var events = new List<EconomicEvent>();

        if (_config.EnableEconomicCalendar)
        {
            var economicEvents = await GetEconomicCalendarEventsAsync();
            events.AddRange(economicEvents);
        }

        if (_config.EnableEarningsCalendar)
        {
            var earningsEvents = await GetEarningsCalendarEventsAsync(symbol);
            events.AddRange(earningsEvents);
        }

        // Filter to upcoming events within lookahead window
        var cutoffDate = DateTime.UtcNow.AddDays(_config.LookAheadDays);
        return events
            .Where(e => e.EventDate >= DateTime.UtcNow && e.EventDate <= cutoffDate)
            .OrderBy(e => e.EventDate)
            .ToList();
    }

    /// <summary>
    /// Gets economic calendar events from free APIs
    /// </summary>
    private async Task<List<EconomicEvent>> GetEconomicCalendarEventsAsync()
    {
        try
        {
            // Use a free economic calendar API (example: investing.com or similar)
            var events = new List<EconomicEvent>();

            // Add known recurring events (FOMC meetings, etc.)
            events.AddRange(GetKnownRecurringEvents());

            // Try to fetch from external API if available
            if (!string.IsNullOrEmpty(_config.EconomicCalendarApiKey))
            {
                var apiEvents = await FetchFromEconomicCalendarApiAsync();
                events.AddRange(apiEvents);
            }

            return events;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error fetching economic calendar events");
            return new List<EconomicEvent>();
        }
    }

    /// <summary>
    /// Gets earnings calendar events for a specific symbol
    /// </summary>
    private Task<List<EconomicEvent>> GetEarningsCalendarEventsAsync(string symbol)
    {
        try
        {
            // For now, return empty list - could integrate with earnings calendar APIs
            // like Alpha Vantage, IEX Cloud, or Yahoo Finance
            return Task.FromResult(new List<EconomicEvent>());
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error fetching earnings calendar for {Symbol}", symbol);
            return Task.FromResult(new List<EconomicEvent>());
        }
    }

    /// <summary>
    /// Gets known recurring economic events
    /// </summary>
    private List<EconomicEvent> GetKnownRecurringEvents()
    {
        var events = new List<EconomicEvent>();
        var now = DateTime.UtcNow;

        // FOMC meetings (8 times per year, roughly every 6 weeks)
        var fomcDates = GetFomcMeetingDates(now.Year);
        foreach (var date in fomcDates.Where(d => d >= now && d <= now.AddDays(_config.LookAheadDays)))
        {
            events.Add(new EconomicEvent(
                "FOMC_MEETING",
                "FOMC Meeting",
                "Federal Open Market Committee Meeting",
                date,
                EventImpact.High,
                "USD",
                "Federal Reserve"
            ));
        }

        // NFP (First Friday of each month)
        var nfpDates = GetNfpDates(now, _config.LookAheadDays);
        foreach (var date in nfpDates)
        {
            events.Add(new EconomicEvent(
                "NFP",
                "Non-Farm Payrolls",
                "Monthly employment report",
                date,
                EventImpact.High,
                "USD",
                "Bureau of Labor Statistics"
            ));
        }

        // CPI (Mid-month)
        var cpiDates = GetCpiDates(now, _config.LookAheadDays);
        foreach (var date in cpiDates)
        {
            events.Add(new EconomicEvent(
                "CPI",
                "Consumer Price Index",
                "Monthly inflation report",
                date,
                EventImpact.High,
                "USD",
                "Bureau of Labor Statistics"
            ));
        }

        return events;
    }

    /// <summary>
    /// Fetches events from external economic calendar API
    /// </summary>
    private Task<List<EconomicEvent>> FetchFromEconomicCalendarApiAsync()
    {
        try
        {
            // Placeholder for external API integration
            // Could integrate with APIs like:
            // - Alpha Vantage Economic Indicators
            // - FRED API for scheduled releases
            // - Trading Economics API
            return Task.FromResult(new List<EconomicEvent>());
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error fetching from economic calendar API");
            return Task.FromResult(new List<EconomicEvent>());
        }
    }

    /// <summary>
    /// Analyzes events to determine risk level
    /// </summary>
    private EventCalendarAnalysis AnalyzeEvents(string symbol, List<EconomicEvent> events)
    {
        if (!events.Any())
        {
            return new EventCalendarAnalysis(
                symbol,
                EventRiskLevel.Low,
                "No high-impact events in the next few days",
                events,
                DateTime.UtcNow
            );
        }

        var highImpactEvents = events.Where(e => e.Impact == EventImpact.High).ToList();
        var todayEvents = events.Where(e => e.EventDate.Date == DateTime.UtcNow.Date).ToList();
        var tomorrowEvents = events.Where(e => e.EventDate.Date == DateTime.UtcNow.Date.AddDays(1)).ToList();

        var riskLevel = DetermineRiskLevel(events, highImpactEvents, todayEvents, tomorrowEvents);
        var reasoning = GenerateReasoning(events, highImpactEvents, todayEvents, tomorrowEvents, riskLevel);

        return new EventCalendarAnalysis(symbol, riskLevel, reasoning, events, DateTime.UtcNow);
    }

    /// <summary>
    /// Determines risk level based on events
    /// </summary>
    private EventRiskLevel DetermineRiskLevel(
        List<EconomicEvent> allEvents,
        List<EconomicEvent> highImpactEvents,
        List<EconomicEvent> todayEvents,
        List<EconomicEvent> tomorrowEvents)
    {
        // Critical: High-impact event today
        if (todayEvents.Any(e => e.Impact == EventImpact.High))
        {
            return EventRiskLevel.Critical;
        }

        // High: High-impact event tomorrow or multiple events today
        if (tomorrowEvents.Any(e => e.Impact == EventImpact.High) || todayEvents.Count >= 3)
        {
            return EventRiskLevel.High;
        }

        // Medium: Any events today or high-impact events within 2 days
        if (todayEvents.Any() || highImpactEvents.Any(e => e.EventDate <= DateTime.UtcNow.AddDays(2)))
        {
            return EventRiskLevel.Medium;
        }

        // Low: High-impact events within lookahead window
        if (highImpactEvents.Any())
        {
            return EventRiskLevel.Low;
        }

        return EventRiskLevel.Low;
    }

    /// <summary>
    /// Generates reasoning for the risk assessment
    /// </summary>
    private string GenerateReasoning(
        List<EconomicEvent> allEvents,
        List<EconomicEvent> highImpactEvents,
        List<EconomicEvent> todayEvents,
        List<EconomicEvent> tomorrowEvents,
        EventRiskLevel riskLevel)
    {
        var sb = new StringBuilder();

        if (todayEvents.Any())
        {
            sb.Append($"{todayEvents.Count} event(s) today");
            var highImpactToday = todayEvents.Where(e => e.Impact == EventImpact.High).ToList();
            if (highImpactToday.Any())
            {
                sb.Append($" (including {string.Join(", ", highImpactToday.Select(e => e.Name))})");
            }
        }

        if (tomorrowEvents.Any())
        {
            if (sb.Length > 0) sb.Append(", ");
            sb.Append($"{tomorrowEvents.Count} event(s) tomorrow");
            var highImpactTomorrow = tomorrowEvents.Where(e => e.Impact == EventImpact.High).ToList();
            if (highImpactTomorrow.Any())
            {
                sb.Append($" (including {string.Join(", ", highImpactTomorrow.Select(e => e.Name))})");
            }
        }

        if (sb.Length == 0 && highImpactEvents.Any())
        {
            var nextEvent = highImpactEvents.OrderBy(e => e.EventDate).First();
            var daysUntil = (nextEvent.EventDate.Date - DateTime.UtcNow.Date).Days;
            sb.Append($"Next high-impact event: {nextEvent.Name} in {daysUntil} day(s)");
        }

        if (sb.Length == 0)
        {
            sb.Append("No significant events in the near term");
        }

        return sb.ToString();
    }

    /// <summary>
    /// Gets FOMC meeting dates for a given year
    /// </summary>
    private List<DateTime> GetFomcMeetingDates(int year)
    {
        // FOMC typically meets 8 times per year
        // These are approximate dates - in practice, you'd want to maintain an updated calendar
        return new List<DateTime>
        {
            new DateTime(year, 1, 31),  // January
            new DateTime(year, 3, 21),  // March
            new DateTime(year, 5, 2),   // May
            new DateTime(year, 6, 13),  // June
            new DateTime(year, 7, 25),  // July
            new DateTime(year, 9, 19),  // September
            new DateTime(year, 11, 7),  // November
            new DateTime(year, 12, 12)  // December
        };
    }

    /// <summary>
    /// Gets NFP release dates (first Friday of each month)
    /// </summary>
    private List<DateTime> GetNfpDates(DateTime startDate, int lookAheadDays)
    {
        var dates = new List<DateTime>();
        var endDate = startDate.AddDays(lookAheadDays);
        var current = new DateTime(startDate.Year, startDate.Month, 1);

        while (current <= endDate)
        {
            // Find first Friday of the month
            while (current.DayOfWeek != DayOfWeek.Friday)
            {
                current = current.AddDays(1);
            }

            if (current >= startDate && current <= endDate)
            {
                dates.Add(current.AddHours(8).AddMinutes(30)); // 8:30 AM ET
            }

            // Move to next month
            current = current.AddMonths(1);
            current = new DateTime(current.Year, current.Month, 1);
        }

        return dates;
    }

    /// <summary>
    /// Gets CPI release dates (typically mid-month)
    /// </summary>
    private List<DateTime> GetCpiDates(DateTime startDate, int lookAheadDays)
    {
        var dates = new List<DateTime>();
        var endDate = startDate.AddDays(lookAheadDays);
        var current = new DateTime(startDate.Year, startDate.Month, 13); // Typically around 13th

        while (current <= endDate)
        {
            if (current >= startDate)
            {
                dates.Add(current.AddHours(8).AddMinutes(30)); // 8:30 AM ET
            }

            // Move to next month
            current = current.AddMonths(1);
            current = new DateTime(current.Year, current.Month, 13);
        }

        return dates;
    }

    public async Task<EventCalendarAnalysis> GetEventAnalysisAsync(string symbol)
    {
        return await AnalyzeUpcomingEventsAsync(symbol);
    }

    public async Task<List<EconomicEvent>> GetUpcomingEventsAsync(string symbol, int days = 7)
    {
        return await GetUpcomingEventsAsync(symbol);
    }
}

/// <summary>
/// Interface for event calendar filter
/// </summary>
public interface IEventCalendarFilter
{
    Task<bool> IsEligibleAsync(string symbol, IReadOnlyList<IBar> bars);
    Task<EventCalendarAnalysis> GetEventAnalysisAsync(string symbol);
    Task<List<EconomicEvent>> GetUpcomingEventsAsync(string symbol, int days = 7);
}

/// <summary>
/// Event risk levels
/// </summary>
public enum EventRiskLevel : byte
{
    Unknown = 0,
    Low = 1,
    Medium = 2,
    High = 3,
    Critical = 4
}

/// <summary>
/// Event impact levels
/// </summary>
public enum EventImpact
{
    Low,
    Medium,
    High
}

/// <summary>
/// Event calendar analysis result
/// </summary>
public record EventCalendarAnalysis(
    string Symbol,
    EventRiskLevel RiskLevel,
    string Reasoning,
    List<EconomicEvent> UpcomingEvents,
    DateTime AnalyzedAt
);

/// <summary>
/// Economic event record
/// </summary>
public record EconomicEvent(
    string EventId,
    string Name,
    string Description,
    DateTime EventDate,
    EventImpact Impact,
    string Currency,
    string Source
);

/// <summary>
/// Configuration for event calendar filter
/// </summary>
public record EventCalendarConfig
{
    public int CacheHours { get; init; } = 4;
    public string EconomicCalendarApiKey { get; init; } = "";
    public int LookAheadDays { get; init; } = 3;
    public string[] HighImpactEvents { get; init; } = Array.Empty<string>();
    public bool EnableEconomicCalendar { get; init; } = true;
    public bool EnableEarningsCalendar { get; init; } = true;
}
