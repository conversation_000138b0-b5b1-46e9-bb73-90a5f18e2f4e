namespace SmaTrendFollower.Services.ErrorHandling;

/// <summary>
/// Enhanced retry service with comprehensive retry policies and circuit breaker integration
/// </summary>
public interface IEnhancedRetryService
{
    /// <summary>
    /// Execute an operation with retry policy
    /// </summary>
    /// <typeparam name="T">Return type</typeparam>
    /// <param name="operation">Operation to execute</param>
    /// <param name="operationName">Name of the operation for logging</param>
    /// <param name="serviceName">Name of the service for circuit breaker</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Result of the operation</returns>
    Task<T> ExecuteAsync<T>(
        Func<Task<T>> operation,
        string operationName,
        string? serviceName = null,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Execute an operation with retry policy (no return value)
    /// </summary>
    /// <param name="operation">Operation to execute</param>
    /// <param name="operationName">Name of the operation for logging</param>
    /// <param name="serviceName">Name of the service for circuit breaker</param>
    /// <param name="cancellationToken">Cancellation token</param>
    Task ExecuteAsync(
        Func<Task> operation,
        string operationName,
        string? serviceName = null,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Execute an operation with custom retry policy
    /// </summary>
    /// <typeparam name="T">Return type</typeparam>
    /// <param name="operation">Operation to execute</param>
    /// <param name="retryPolicy">Custom retry policy</param>
    /// <param name="operationName">Name of the operation for logging</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Result of the operation</returns>
    Task<T> ExecuteWithPolicyAsync<T>(
        Func<Task<T>> operation,
        RetryPolicy retryPolicy,
        string operationName,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Execute an HTTP operation with retry policy
    /// </summary>
    /// <param name="httpOperation">HTTP operation to execute</param>
    /// <param name="operationName">Name of the operation for logging</param>
    /// <param name="serviceName">Name of the service for circuit breaker</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>HTTP response</returns>
    Task<HttpResponseMessage> ExecuteHttpAsync(
        Func<Task<HttpResponseMessage>> httpOperation,
        string operationName,
        string? serviceName = null,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Get retry statistics for monitoring
    /// </summary>
    /// <returns>Retry statistics</returns>
    RetryStatistics GetStatistics();

    /// <summary>
    /// Reset retry statistics
    /// </summary>
    void ResetStatistics();
}

/// <summary>
/// Retry policy configuration
/// </summary>
public sealed class RetryPolicy
{
    /// <summary>
    /// Maximum number of retry attempts
    /// </summary>
    public int MaxAttempts { get; set; } = 3;

    /// <summary>
    /// Base delay for exponential backoff
    /// </summary>
    public TimeSpan BaseDelay { get; set; } = TimeSpan.FromSeconds(1);

    /// <summary>
    /// Maximum delay between retries
    /// </summary>
    public TimeSpan MaxDelay { get; set; } = TimeSpan.FromMinutes(5);

    /// <summary>
    /// Backoff strategy
    /// </summary>
    public BackoffStrategy BackoffStrategy { get; set; } = BackoffStrategy.Exponential;

    /// <summary>
    /// Whether to add jitter to delays
    /// </summary>
    public bool UseJitter { get; set; } = true;

    /// <summary>
    /// Maximum jitter amount
    /// </summary>
    public TimeSpan MaxJitter { get; set; } = TimeSpan.FromSeconds(1);

    /// <summary>
    /// Custom predicate to determine if an exception should be retried
    /// </summary>
    public Func<Exception, bool>? ShouldRetryPredicate { get; set; }

    /// <summary>
    /// Custom delay calculator
    /// </summary>
    public Func<int, TimeSpan, TimeSpan>? DelayCalculator { get; set; }

    /// <summary>
    /// Whether to enable circuit breaker integration
    /// </summary>
    public bool EnableCircuitBreaker { get; set; } = true;

    /// <summary>
    /// Timeout for individual operations
    /// </summary>
    public TimeSpan? OperationTimeout { get; set; }

    /// <summary>
    /// Create a default retry policy for API operations
    /// </summary>
    public static RetryPolicy ForApi() => new()
    {
        MaxAttempts = 3,
        BaseDelay = TimeSpan.FromSeconds(1),
        MaxDelay = TimeSpan.FromSeconds(30),
        BackoffStrategy = BackoffStrategy.Exponential,
        UseJitter = true,
        EnableCircuitBreaker = true,
        OperationTimeout = TimeSpan.FromSeconds(30)
    };

    /// <summary>
    /// Create a retry policy for database operations
    /// </summary>
    public static RetryPolicy ForDatabase() => new()
    {
        MaxAttempts = 5,
        BaseDelay = TimeSpan.FromMilliseconds(500),
        MaxDelay = TimeSpan.FromSeconds(10),
        BackoffStrategy = BackoffStrategy.Linear,
        UseJitter = true,
        EnableCircuitBreaker = false,
        OperationTimeout = TimeSpan.FromSeconds(15)
    };

    /// <summary>
    /// Create a retry policy for critical operations
    /// </summary>
    public static RetryPolicy ForCritical() => new()
    {
        MaxAttempts = 5,
        BaseDelay = TimeSpan.FromSeconds(2),
        MaxDelay = TimeSpan.FromMinutes(2),
        BackoffStrategy = BackoffStrategy.Exponential,
        UseJitter = true,
        EnableCircuitBreaker = true,
        OperationTimeout = TimeSpan.FromMinutes(1)
    };

    /// <summary>
    /// Create a retry policy for rate-limited operations
    /// </summary>
    public static RetryPolicy ForRateLimit() => new()
    {
        MaxAttempts = 10,
        BaseDelay = TimeSpan.FromSeconds(5),
        MaxDelay = TimeSpan.FromMinutes(10),
        BackoffStrategy = BackoffStrategy.Linear,
        UseJitter = true,
        EnableCircuitBreaker = false,
        OperationTimeout = TimeSpan.FromMinutes(2)
    };
}

/// <summary>
/// Backoff strategies for retry delays
/// </summary>
public enum BackoffStrategy
{
    /// <summary>
    /// Fixed delay between retries
    /// </summary>
    Fixed,

    /// <summary>
    /// Linear increase in delay
    /// </summary>
    Linear,

    /// <summary>
    /// Exponential increase in delay
    /// </summary>
    Exponential,

    /// <summary>
    /// Custom delay calculation
    /// </summary>
    Custom
}

/// <summary>
/// Retry statistics for monitoring
/// </summary>
public sealed class RetryStatistics
{
    public int TotalOperations { get; set; }
    public int SuccessfulOperations { get; set; }
    public int FailedOperations { get; set; }
    public int TotalRetries { get; set; }
    public TimeSpan TotalRetryTime { get; set; }
    public TimeSpan AverageOperationTime { get; set; }
    public Dictionary<string, int> OperationCounts { get; set; } = new();
    public Dictionary<string, int> ErrorCounts { get; set; } = new();
    public DateTime LastResetTime { get; set; } = DateTime.UtcNow;

    public double SuccessRate => TotalOperations > 0 ? (double)SuccessfulOperations / TotalOperations : 0;
    public double RetryRate => TotalOperations > 0 ? (double)TotalRetries / TotalOperations : 0;
}

/// <summary>
/// Retry attempt information
/// </summary>
public sealed class RetryAttemptInfo
{
    public int AttemptNumber { get; init; }
    public TimeSpan Delay { get; init; }
    public Exception Exception { get; init; } = null!;
    public DateTime Timestamp { get; init; } = DateTime.UtcNow;
    public string OperationName { get; init; } = string.Empty;
    public string? ServiceName { get; init; }
}

/// <summary>
/// Retry event arguments for monitoring and logging
/// </summary>
public sealed class RetryEventArgs : EventArgs
{
    public RetryAttemptInfo AttemptInfo { get; init; } = null!;
    public bool IsLastAttempt { get; init; }
    public bool WillRetry { get; init; }
}
