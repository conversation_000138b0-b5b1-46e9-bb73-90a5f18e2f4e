#!/usr/bin/env pwsh
<#
.SYNOPSIS
    Simple validation script for SmaTrendFollower environment
.DESCRIPTION
    This script performs quick validation checks to ensure the environment
    is properly set up and ready for Augment Agent operations.
.EXAMPLE
    .\validate-environment-simple.ps1
#>

$ErrorActionPreference = "Continue"

Write-Host "SmaTrendFollower Environment Validation" -ForegroundColor Green
Write-Host "=======================================" -ForegroundColor Green

$allChecks = @()

# Function to add check result
function Add-Check {
    param(
        [string]$Name,
        [bool]$Passed,
        [string]$Details = ""
    )
    
    $script:allChecks += [PSCustomObject]@{
        Name = $Name
        Passed = $Passed
        Details = $Details
    }
    
    if ($Passed) {
        Write-Host "SUCCESS: $Name" -ForegroundColor Green
        if ($Details) {
            Write-Host "   $Details" -ForegroundColor Gray
        }
    } else {
        Write-Host "FAILED: $Name" -ForegroundColor Red
        if ($Details) {
            Write-Host "   $Details" -ForegroundColor Yellow
        }
    }
}

# Check 1: .NET SDK
try {
    $dotnetVersion = dotnet --version
    Add-Check ".NET SDK Available" $true "Version: $dotnetVersion"
} catch {
    Add-Check ".NET SDK Available" $false "Not found or not accessible"
}

# Check 2: Project Files
$projectFiles = @(
    "SmaTrendFollower.sln",
    "SmaTrendFollower.Console/SmaTrendFollower.Console.csproj",
    "SmaTrendFollower.Tests/SmaTrendFollower.Tests.csproj"
)

foreach ($file in $projectFiles) {
    $exists = Test-Path $file
    Add-Check "Project File: $file" $exists
}

# Check 3: Environment File
$envExists = Test-Path ".env"
Add-Check "Environment File (.env)" $envExists

if ($envExists) {
    $envContent = Get-Content ".env" -ErrorAction SilentlyContinue
    $requiredVars = @("APCA_API_KEY_ID", "APCA_API_SECRET_KEY", "APCA_API_ENV", "POLY_API_KEY")
    
    foreach ($var in $requiredVars) {
        $found = $envContent | Where-Object { $_ -match "^$var=.+" }
        Add-Check "Environment Variable: $var" ($null -ne $found)
    }
}

# Check 4: Required Directories
$directories = @("logs", "data", "cache")
foreach ($dir in $directories) {
    $exists = Test-Path $dir -PathType Container
    Add-Check "Directory: $dir" $exists
}

# Check 5: Build Status
try {
    $buildResult = dotnet build --verbosity quiet 2>&1
    $buildSuccess = $LASTEXITCODE -eq 0
    Add-Check "Solution Builds Successfully" $buildSuccess
} catch {
    Add-Check "Solution Builds Successfully" $false "Build command failed"
}

# Check 6: Test Project
try {
    $testResult = dotnet test --no-build --verbosity quiet 2>&1
    $testSuccess = $LASTEXITCODE -eq 0
    Add-Check "Tests Can Run" $testSuccess
} catch {
    Add-Check "Tests Can Run" $false "Test command failed"
}

# Check 7: Console Application
$consoleExe = "SmaTrendFollower.Console/bin/Debug/net8.0/SmaTrendFollower.Console.dll"
if (-not (Test-Path $consoleExe)) {
    $consoleExe = "SmaTrendFollower.Console/bin/Release/net8.0/SmaTrendFollower.Console.dll"
}
$consoleExists = Test-Path $consoleExe
Add-Check "Console Application Built" $consoleExists

# Check 8: Basic Application Startup
if ($consoleExists) {
    try {
        $startupTest = dotnet run --project SmaTrendFollower.Console -- --help 2>&1
        $startupSuccess = $LASTEXITCODE -eq 0 -or $startupTest -match "help|usage|options"
        Add-Check "Application Starts Successfully" $startupSuccess
    } catch {
        Add-Check "Application Starts Successfully" $false "Startup test failed"
    }
} else {
    Add-Check "Application Starts Successfully" $false "Console application not built"
}

# Summary
Write-Host "`nValidation Summary" -ForegroundColor Cyan
Write-Host "=================" -ForegroundColor Cyan

$passedChecks = ($allChecks | Where-Object { $_.Passed }).Count
$totalChecks = $allChecks.Count
$passRate = [math]::Round(($passedChecks / $totalChecks) * 100, 1)

Write-Host "Passed: $passedChecks/$totalChecks ($passRate%)" -ForegroundColor $(if ($passRate -ge 80) { "Green" } elseif ($passRate -ge 60) { "Yellow" } else { "Red" })

if ($passRate -ge 90) {
    Write-Host "`nSUCCESS: Environment is ready for Augment Agent!" -ForegroundColor Green
} elseif ($passRate -ge 70) {
    Write-Host "`nWARNING: Environment has some issues but may work" -ForegroundColor Yellow
    Write-Host "Consider running setup script again or fixing failed checks" -ForegroundColor Yellow
} else {
    Write-Host "`nERROR: Environment needs attention" -ForegroundColor Red
    Write-Host "Please run setup script: .\setup-remote-environment-simple.ps1" -ForegroundColor Red
}

# Failed checks details
$failedChecks = $allChecks | Where-Object { -not $_.Passed }
if ($failedChecks.Count -gt 0) {
    Write-Host "`nFailed Checks:" -ForegroundColor Yellow
    foreach ($check in $failedChecks) {
        Write-Host "  - $($check.Name)" -ForegroundColor Red
        if ($check.Details) {
            Write-Host "    $($check.Details)" -ForegroundColor Gray
        }
    }
}

Write-Host "`nQuick Commands:" -ForegroundColor Cyan
Write-Host "  Setup:     .\setup-remote-environment-simple.ps1" -ForegroundColor White
Write-Host "  Build:     dotnet build" -ForegroundColor White
Write-Host "  Test:      dotnet test" -ForegroundColor White
Write-Host "  Run:       dotnet run --project SmaTrendFollower.Console" -ForegroundColor White
