using Microsoft.Extensions.Logging;
using System.Collections.Concurrent;
using System.Diagnostics;

namespace SmaTrendFollower.Services;

/// <summary>
/// Service for monitoring and optimizing system performance
/// </summary>
public interface IPerformanceOptimizationService
{
    void StartOperation(string operationName);
    void EndOperation(string operationName);
    void RecordMetric(string metricName, double value, string? unit = null);
    Task<PerformanceReport> GenerateReportAsync();
    void LogPerformanceWarning(string operation, TimeSpan duration, TimeSpan threshold);
}

public sealed class PerformanceOptimizationService : IPerformanceOptimizationService
{
    private readonly ILogger<PerformanceOptimizationService> _logger;
    private readonly ConcurrentDictionary<string, Stopwatch> _activeOperations = new();
    private readonly ConcurrentDictionary<string, List<OptimizationMetric>> _metrics = new();
    private readonly object _lockObject = new();

    public PerformanceOptimizationService(ILogger<PerformanceOptimizationService> logger)
    {
        _logger = logger;
    }

    public void StartOperation(string operationName)
    {
        var stopwatch = Stopwatch.StartNew();
        _activeOperations.AddOrUpdate(operationName, stopwatch, (key, existing) =>
        {
            existing.Stop();
            return stopwatch;
        });
        
        _logger.LogDebug("Started performance tracking for operation: {Operation}", operationName);
    }

    public void EndOperation(string operationName)
    {
        if (_activeOperations.TryRemove(operationName, out var stopwatch))
        {
            stopwatch.Stop();
            var duration = stopwatch.Elapsed;
            
            RecordMetric($"{operationName}_Duration", duration.TotalMilliseconds, "ms");
            
            // Log performance warnings for slow operations
            var thresholds = GetPerformanceThresholds();
            if (thresholds.TryGetValue(operationName, out var threshold) && duration > threshold)
            {
                LogPerformanceWarning(operationName, duration, threshold);
            }
            
            _logger.LogDebug("Completed operation {Operation} in {Duration}ms", 
                operationName, duration.TotalMilliseconds);
        }
        else
        {
            _logger.LogWarning("Attempted to end operation {Operation} that was not started", operationName);
        }
    }

    public void RecordMetric(string metricName, double value, string? unit = null)
    {
        var metric = new OptimizationMetric
        {
            Name = metricName,
            Value = value,
            Unit = unit ?? "count",
            Timestamp = DateTime.UtcNow
        };

        _metrics.AddOrUpdate(metricName,
            new List<OptimizationMetric> { metric },
            (key, existing) =>
            {
                lock (_lockObject)
                {
                    existing.Add(metric);
                    // Keep only last 1000 metrics per type to prevent memory bloat
                    if (existing.Count > 1000)
                    {
                        existing.RemoveRange(0, existing.Count - 1000);
                    }
                    return existing;
                }
            });
    }

    public async Task<PerformanceReport> GenerateReportAsync()
    {
        await Task.Yield(); // Make it async for future database operations
        
        var report = new PerformanceReport
        {
            GeneratedAt = DateTime.UtcNow,
            ActiveOperations = _activeOperations.Keys.ToList(),
            MetricSummaries = new Dictionary<string, MetricSummary>()
        };

        foreach (var kvp in _metrics)
        {
            var metricName = kvp.Key;
            var values = kvp.Value.ToList(); // Thread-safe copy
            
            if (values.Any())
            {
                var numericValues = values.Select(m => m.Value).ToList();
                var summary = new MetricSummary
                {
                    Name = metricName,
                    Count = values.Count,
                    Average = numericValues.Average(),
                    Min = numericValues.Min(),
                    Max = numericValues.Max(),
                    Unit = values.First().Unit,
                    LastRecorded = values.Max(v => v.Timestamp)
                };
                
                // Calculate percentiles
                var sortedValues = numericValues.OrderBy(v => v).ToList();
                summary.P50 = GetPercentile(sortedValues, 0.5);
                summary.P95 = GetPercentile(sortedValues, 0.95);
                summary.P99 = GetPercentile(sortedValues, 0.99);
                
                report.MetricSummaries[metricName] = summary;
            }
        }

        return report;
    }

    public void LogPerformanceWarning(string operation, TimeSpan duration, TimeSpan threshold)
    {
        _logger.LogWarning("Performance warning: {Operation} took {Duration}ms (threshold: {Threshold}ms)",
            operation, duration.TotalMilliseconds, threshold.TotalMilliseconds);
        
        RecordMetric($"{operation}_SlowOperations", 1);
    }

    private static Dictionary<string, TimeSpan> GetPerformanceThresholds()
    {
        return new Dictionary<string, TimeSpan>
        {
            ["UniverseBuilding"] = TimeSpan.FromSeconds(30),
            ["SignalGeneration"] = TimeSpan.FromSeconds(10),
            ["DatabaseQuery"] = TimeSpan.FromMilliseconds(500),
            ["ApiCall"] = TimeSpan.FromSeconds(5),
            ["CacheOperation"] = TimeSpan.FromMilliseconds(100),
            ["RiskCalculation"] = TimeSpan.FromMilliseconds(100),
            ["PortfolioAnalysis"] = TimeSpan.FromSeconds(2)
        };
    }

    private static double GetPercentile(List<double> sortedValues, double percentile)
    {
        if (!sortedValues.Any()) return 0;
        
        var index = percentile * (sortedValues.Count - 1);
        var lower = (int)Math.Floor(index);
        var upper = (int)Math.Ceiling(index);
        
        if (lower == upper)
        {
            return sortedValues[lower];
        }
        
        var weight = index - lower;
        return sortedValues[lower] * (1 - weight) + sortedValues[upper] * weight;
    }
}

public class OptimizationMetric
{
    public string Name { get; set; } = string.Empty;
    public double Value { get; set; }
    public string Unit { get; set; } = string.Empty;
    public DateTime Timestamp { get; set; }
}

public class MetricSummary
{
    public string Name { get; set; } = string.Empty;
    public int Count { get; set; }
    public double Average { get; set; }
    public double Min { get; set; }
    public double Max { get; set; }
    public double P50 { get; set; }
    public double P95 { get; set; }
    public double P99 { get; set; }
    public string Unit { get; set; } = string.Empty;
    public DateTime LastRecorded { get; set; }
}

public class PerformanceReport
{
    public DateTime GeneratedAt { get; set; }
    public List<string> ActiveOperations { get; set; } = new();
    public Dictionary<string, MetricSummary> MetricSummaries { get; set; } = new();
}
