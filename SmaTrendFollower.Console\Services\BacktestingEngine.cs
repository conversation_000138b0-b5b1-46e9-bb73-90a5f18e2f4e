using SmaTrendFollower.Models;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Configuration;
using System.Collections.Concurrent;
using Alpaca.Markets;

namespace SmaTrendFollower.Services;

/// <summary>
/// Simplified Backtesting Engine - Historical strategy performance testing
/// Tests trading strategies against historical data with realistic execution simulation
/// </summary>
public sealed class BacktestingEngine : IBacktestingEngine
{
    private readonly IBarStore _barStore;
    private readonly ILogger<BacktestingEngine> _logger;
    private readonly BacktestConfig _config;

    // Backtesting state
    private decimal _currentCash;
    private readonly Dictionary<string, BacktestPosition> _positions = new();
    private readonly List<SimpleBacktestTrade> _trades = new();
    private readonly Dictionary<string, decimal> _trailingStops = new();

    public BacktestingEngine(
        IBarStore barStore,
        ILogger<BacktestingEngine> logger,
        IConfiguration configuration)
    {
        _barStore = barStore;
        _logger = logger;
        _config = new BacktestConfig(
            configuration.GetValue("BACKTEST_INITIAL_CAPITAL", 100000m),
            configuration.GetValue("BACKTEST_COMMISSION_PER_TRADE", 1.0m),
            configuration.GetValue("BACKTEST_SLIPPAGE_BPS", 5),
            configuration.GetValue("BACKTEST_MAX_POSITION_SIZE_PCT", 10.0m),
            configuration.GetValue("BACKTEST_ENABLE_TRAILING_STOPS", true)
        );
    }

    /// <summary>
    /// Runs a backtest for the specified parameters
    /// </summary>
    public async Task<SimpleBacktestResult> RunBacktestAsync(
        SimpleBacktestParameters parameters,
        CancellationToken cancellationToken = default)
    {
        _logger.LogInformation("Starting backtest: {StartDate} to {EndDate} with {Symbols} symbols",
            parameters.StartDate, parameters.EndDate, parameters.Symbols.Count);

        try
        {
            // Initialize backtest state
            InitializeBacktest(parameters);

            // Load historical data
            var historicalData = await LoadHistoricalDataAsync(parameters, cancellationToken);
            if (!historicalData.Any())
            {
                throw new InvalidOperationException("No historical data available for backtest");
            }

            // Create unified timeline
            var timeline = CreateUnifiedTimeline(historicalData);
            _logger.LogInformation("Created timeline with {Count} data points", timeline.Count);

            // Run simulation
            var result = await RunSimulationAsync(timeline, parameters, cancellationToken);

            _logger.LogInformation("Backtest completed: {Trades} trades, {FinalValue:C} final value",
                result.TotalTrades, result.FinalPortfolioValue);

            return result;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error running backtest");
            throw;
        }
    }

    /// <summary>
    /// Initializes backtest state
    /// </summary>
    private void InitializeBacktest(SimpleBacktestParameters parameters)
    {
        _currentCash = parameters.InitialCapital ?? _config.InitialCapital;
        _positions.Clear();
        _trades.Clear();
        _trailingStops.Clear();

        _logger.LogDebug("Initialized backtest with {Cash:C} starting capital", _currentCash);
    }

    /// <summary>
    /// Loads historical data for all symbols
    /// </summary>
    private async Task<Dictionary<string, List<IBar>>> LoadHistoricalDataAsync(
        SimpleBacktestParameters parameters,
        CancellationToken cancellationToken)
    {
        var data = new Dictionary<string, List<IBar>>();

        foreach (var symbol in parameters.Symbols)
        {
            try
            {
                var bars = await _barStore.LoadBarsAsync(symbol, "Day", parameters.StartDate, parameters.EndDate, cancellationToken);
                if (bars.Any())
                {
                    data[symbol] = bars.OrderBy(b => b.TimeUtc).ToList();
                    _logger.LogDebug("Loaded {Count} bars for {Symbol}", bars.Count(), symbol);
                }
                else
                {
                    _logger.LogWarning("No historical data found for {Symbol}", symbol);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to load data for {Symbol}", symbol);
            }
        }

        return data;
    }

    /// <summary>
    /// Creates a unified timeline from all symbol data
    /// </summary>
    private List<KeyValuePair<DateTime, Dictionary<string, IBar>>> CreateUnifiedTimeline(
        Dictionary<string, List<IBar>> historicalData)
    {
        var timeline = new SortedDictionary<DateTime, Dictionary<string, IBar>>();

        foreach (var symbolData in historicalData)
        {
            foreach (var bar in symbolData.Value)
            {
                if (!timeline.ContainsKey(bar.TimeUtc))
                {
                    timeline[bar.TimeUtc] = new Dictionary<string, IBar>();
                }
                timeline[bar.TimeUtc][symbolData.Key] = bar;
            }
        }

        return timeline.ToList();
    }

    /// <summary>
    /// Runs the trading simulation
    /// </summary>
    private async Task<SimpleBacktestResult> RunSimulationAsync(
        List<KeyValuePair<DateTime, Dictionary<string, IBar>>> timeline,
        SimpleBacktestParameters parameters,
        CancellationToken cancellationToken)
    {
        var startTime = DateTime.UtcNow;
        var processedDays = 0;

        foreach (var timePoint in timeline)
        {
            if (cancellationToken.IsCancellationRequested)
                break;

            var currentDate = timePoint.Key;
            var currentBars = timePoint.Value;

            // Update position values and check stops
            UpdatePositionValues(currentBars);
            await CheckTrailingStopsAsync(currentBars);

            // Generate and process signals for each symbol
            foreach (var symbolBar in currentBars)
            {
                var symbol = symbolBar.Key;
                var bar = symbolBar.Value;

                // Simple signal generation (placeholder)
                var signal = await GenerateSimpleSignalAsync(symbol, bar, currentDate);
                if (signal.HasValue)
                {
                    await ProcessSignalAsync(signal.Value, bar, currentDate);
                }
            }

            processedDays++;
            if (processedDays % 100 == 0)
            {
                _logger.LogDebug("Processed {Days} days of backtest data", processedDays);
            }
        }

        // Calculate final results
        var finalEquity = CalculateCurrentEquity(timeline.LastOrDefault().Value ?? new Dictionary<string, IBar>());
        var duration = DateTime.UtcNow - startTime;

        return CreateBacktestResult(parameters, finalEquity, duration);
    }

    /// <summary>
    /// Generates a simple trading signal (placeholder implementation)
    /// </summary>
    private Task<TradingSignal?> GenerateSimpleSignalAsync(string symbol, IBar bar, DateTime timestamp)
    {
        try
        {
            // Simple momentum signal: buy if price > 20-day average
            // This is a placeholder - in real implementation, you'd use sophisticated signal generation
            
            // For now, generate random signals for demonstration
            if (Random.Shared.NextDouble() < 0.05) // 5% chance of signal
            {
                return Task.FromResult<TradingSignal?>(new TradingSignal(symbol, bar.Close, 0.02m, 0.1m)); // Simple signal
            }

            return Task.FromResult<TradingSignal?>(null);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error generating signal for {Symbol}", symbol);
            return Task.FromResult<TradingSignal?>(null);
        }
    }

    /// <summary>
    /// Processes a trading signal
    /// </summary>
    private Task ProcessSignalAsync(TradingSignal signal, IBar bar, DateTime timestamp)
    {
        try
        {
            // Check if we already have a position
            if (_positions.ContainsKey(signal.Symbol))
            {
                return Task.CompletedTask; // Skip if already have position
            }

            // Calculate position size
            var maxPositionValue = (CalculateCurrentEquity(new Dictionary<string, IBar> { { signal.Symbol, bar } }) * _config.MaxPositionSizePct) / 100m;
            var quantity = Math.Floor(maxPositionValue / bar.Close);

            if (quantity <= 0 || quantity * bar.Close > _currentCash)
            {
                return Task.CompletedTask; // Not enough cash
            }

            // Apply slippage
            var executionPrice = bar.Close * (1 + _config.SlippageBps / 10000m);
            var totalCost = quantity * executionPrice + _config.CommissionPerTrade;

            if (totalCost > _currentCash)
            {
                return Task.CompletedTask; // Not enough cash after slippage and commission
            }

            // Execute trade
            _currentCash -= totalCost;
            
            var position = new BacktestPosition(
                signal.Symbol,
                quantity,
                executionPrice,
                timestamp
            );
            
            _positions[signal.Symbol] = position;

            var trade = new SimpleBacktestTrade(
                Guid.NewGuid().ToString(),
                signal.Symbol,
                "BUY",
                quantity,
                executionPrice,
                timestamp,
                null,
                null,
                _config.CommissionPerTrade
            );
            
            _trades.Add(trade);

            // Set trailing stop if enabled
            if (_config.EnableTrailingStops)
            {
                var stopPrice = executionPrice * 0.95m; // 5% trailing stop
                _trailingStops[signal.Symbol] = stopPrice;
            }

            _logger.LogDebug("Opened position: {Symbol} {Quantity} @ {Price:C}", 
                signal.Symbol, quantity, executionPrice);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error processing signal for {Symbol}", signal.Symbol);
        }

        return Task.CompletedTask;
    }

    /// <summary>
    /// Updates position values based on current prices
    /// </summary>
    private void UpdatePositionValues(Dictionary<string, IBar> currentBars)
    {
        foreach (var position in _positions.Values.ToList())
        {
            if (currentBars.TryGetValue(position.Symbol, out var bar))
            {
                var currentValue = position.Quantity * bar.Close;
                var unrealizedPnL = currentValue - (position.Quantity * position.EntryPrice);
                
                // Update position (if we had a mutable position object)
                // For now, just log significant changes
                if (Math.Abs(unrealizedPnL) > position.Quantity * position.EntryPrice * 0.1m) // 10% change
                {
                    _logger.LogDebug("Position {Symbol}: {PnL:C} unrealized P&L", 
                        position.Symbol, unrealizedPnL);
                }
            }
        }
    }

    /// <summary>
    /// Checks and executes trailing stops
    /// </summary>
    private Task CheckTrailingStopsAsync(Dictionary<string, IBar> currentBars)
    {
        var stopsToExecute = new List<string>();

        foreach (var kvp in _trailingStops.ToList())
        {
            var symbol = kvp.Key;
            var stopPrice = kvp.Value;

            if (currentBars.TryGetValue(symbol, out var bar) && _positions.TryGetValue(symbol, out var position))
            {
                // Update trailing stop if price moved favorably
                var newStopPrice = bar.Close * 0.95m; // 5% trailing
                if (newStopPrice > stopPrice)
                {
                    _trailingStops[symbol] = newStopPrice;
                }

                // Check if stop was hit
                if (bar.Low <= _trailingStops[symbol])
                {
                    stopsToExecute.Add(symbol);
                }
            }
        }

        // Execute stops
        foreach (var symbol in stopsToExecute)
        {
            ExecuteStopAsync(symbol, currentBars[symbol]).Wait();
        }

        return Task.CompletedTask;
    }

    /// <summary>
    /// Executes a stop loss order
    /// </summary>
    private Task ExecuteStopAsync(string symbol, IBar bar)
    {
        if (!_positions.TryGetValue(symbol, out var position))
            return Task.CompletedTask;

        var executionPrice = _trailingStops[symbol] * (1 - _config.SlippageBps / 10000m); // Apply slippage
        var proceeds = position.Quantity * executionPrice - _config.CommissionPerTrade;

        _currentCash += proceeds;
        _positions.Remove(symbol);
        _trailingStops.Remove(symbol);

        var trade = new SimpleBacktestTrade(
            Guid.NewGuid().ToString(),
            symbol,
            "SELL",
            position.Quantity,
            executionPrice,
            DateTime.UtcNow,
            position.EntryPrice,
            proceeds - (position.Quantity * position.EntryPrice),
            _config.CommissionPerTrade
        );

        _trades.Add(trade);

        _logger.LogDebug("Executed stop: {Symbol} {Quantity} @ {Price:C} (P&L: {PnL:C})",
            symbol, position.Quantity, executionPrice, trade.PnL ?? 0);

        return Task.CompletedTask;
    }

    /// <summary>
    /// Calculates current total equity
    /// </summary>
    private decimal CalculateCurrentEquity(Dictionary<string, IBar> currentBars)
    {
        var equity = _currentCash;

        foreach (var position in _positions.Values)
        {
            if (currentBars.TryGetValue(position.Symbol, out var bar))
            {
                equity += position.Quantity * bar.Close;
            }
            else
            {
                // Use entry price if no current price available
                equity += position.Quantity * position.EntryPrice;
            }
        }

        return equity;
    }

    /// <summary>
    /// Creates the final backtest result
    /// </summary>
    private SimpleBacktestResult CreateBacktestResult(SimpleBacktestParameters parameters, decimal finalEquity, TimeSpan duration)
    {
        var initialCapital = parameters.InitialCapital ?? _config.InitialCapital;
        var totalReturn = (finalEquity - initialCapital) / initialCapital;
        var profitableTrades = _trades.Count(t => (t.PnL ?? 0) > 0);
        var winRate = _trades.Count > 0 ? (decimal)profitableTrades / _trades.Count : 0;

        return new SimpleBacktestResult(
            parameters,
            initialCapital,
            finalEquity,
            totalReturn,
            _trades.Count,
            profitableTrades,
            winRate,
            _trades.Where(t => t.PnL.HasValue).Sum(t => t.PnL!.Value),
            _trades.ToList(),
            duration,
            DateTime.UtcNow
        );
    }
}

/// <summary>
/// Interface for backtesting engine
/// </summary>
public interface IBacktestingEngine
{
    Task<SimpleBacktestResult> RunBacktestAsync(SimpleBacktestParameters parameters, CancellationToken cancellationToken = default);
}

/// <summary>
/// Backtesting configuration
/// </summary>
public record BacktestConfig(
    decimal InitialCapital = 100000m,
    decimal CommissionPerTrade = 1.0m,
    decimal SlippageBps = 5m,
    decimal MaxPositionSizePct = 10.0m,
    bool EnableTrailingStops = true
);

/// <summary>
/// Backtest position
/// </summary>
public record BacktestPosition(
    string Symbol,
    decimal Quantity,
    decimal EntryPrice,
    DateTime EntryTime
);

/// <summary>
/// Simple backtest trade for engine (different from comprehensive BacktestTrade in IBacktestingService)
/// </summary>
public record SimpleBacktestTrade(
    string Id,
    string Symbol,
    string Side,
    decimal Quantity,
    decimal Price,
    DateTime Timestamp,
    decimal? EntryPrice = null,
    decimal? PnL = null,
    decimal Commission = 0
);

/// <summary>
/// Simple backtest parameters for engine (different from comprehensive BacktestParameters in IBacktestingService)
/// </summary>
public record SimpleBacktestParameters(
    List<string> Symbols,
    DateTime StartDate,
    DateTime EndDate,
    decimal? InitialCapital = null
);

/// <summary>
/// Simple backtest result for engine (different from comprehensive BacktestResult in IBacktestingService)
/// </summary>
public record SimpleBacktestResult(
    SimpleBacktestParameters Parameters,
    decimal InitialCapital,
    decimal FinalPortfolioValue,
    decimal TotalReturn,
    int TotalTrades,
    int ProfitableTrades,
    decimal WinRate,
    decimal TotalPnL,
    List<SimpleBacktestTrade> Trades,
    TimeSpan Duration,
    DateTime CompletedAt
);
