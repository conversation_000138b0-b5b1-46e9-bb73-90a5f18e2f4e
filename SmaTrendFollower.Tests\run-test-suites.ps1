#!/usr/bin/env pwsh

<#
.SYNOPSIS
    Runs different test suites with optimized parallelization and performance
.DESCRIPTION
    This script runs tests in separate categories with appropriate parallelization
    settings to maximize performance while avoiding conflicts.
.EXAMPLE
    ./run-test-suites.ps1 -Suite Unit
    ./run-test-suites.ps1 -Suite All -Parallel
#>

param(
    [ValidateSet("Unit", "Integration", "Performance", "Database", "Network", "All")]
    [string]$Suite = "All",
    
    [switch]$Parallel = $false,
    [switch]$Verbose = $false,
    [switch]$Coverage = $false,
    [int]$MaxParallelThreads = 8,
    [int]$TimeoutMinutes = 10
)

# Test suite configurations
$TestSuites = @{
    "Unit" = @{
        Filter = "Category=Unit"
        MaxThreads = 8
        Timeout = 5
        Description = "Fast unit tests with mocks"
    }
    "Integration" = @{
        Filter = "Category=Integration"
        MaxThreads = 2
        Timeout = 15
        Description = "Integration tests with external dependencies"
    }
    "Performance" = @{
        Filter = "Category=Performance"
        MaxThreads = 1
        Timeout = 20
        Description = "Performance and benchmark tests"
    }
    "Database" = @{
        Filter = "Category=Database"
        MaxThreads = 1
        Timeout = 10
        Description = "Database and cache tests"
    }
    "Network" = @{
        Filter = "Category=Network"
        MaxThreads = 2
        Timeout = 15
        Description = "Network and API tests"
    }
}

function Write-TestHeader {
    param([string]$Title, [string]$Description)
    
    Write-Host ""
    Write-Host "=" * 80 -ForegroundColor Cyan
    Write-Host "  $Title" -ForegroundColor Yellow
    Write-Host "  $Description" -ForegroundColor Gray
    Write-Host "=" * 80 -ForegroundColor Cyan
    Write-Host ""
}

function Run-TestSuite {
    param(
        [string]$SuiteName,
        [hashtable]$Config,
        [bool]$UseParallel,
        [bool]$ShowVerbose,
        [bool]$CollectCoverage
    )
    
    Write-TestHeader $SuiteName $Config.Description
    
    $startTime = Get-Date
    
    # Build test command
    $testArgs = @(
        "test"
        "--filter", $Config.Filter
        "--logger", "console;verbosity=normal"
        "--configuration", "Release"
        "--no-build"
    )
    
    if ($ShowVerbose) {
        $testArgs += "--verbosity", "detailed"
    }
    
    if ($CollectCoverage) {
        $testArgs += "--collect", "XPlat Code Coverage"
    }
    
    # Configure parallelization
    if ($UseParallel) {
        $env:XUNIT_MAX_PARALLEL_THREADS = $Config.MaxThreads
        Write-Host "Running with $($Config.MaxThreads) parallel threads" -ForegroundColor Green
    } else {
        $env:XUNIT_MAX_PARALLEL_THREADS = 1
        Write-Host "Running sequentially (1 thread)" -ForegroundColor Yellow
    }
    
    # Set timeout
    $timeoutSeconds = $Config.Timeout * 60
    
    try {
        Write-Host "Command: dotnet $($testArgs -join ' ')" -ForegroundColor Gray
        Write-Host "Timeout: $($Config.Timeout) minutes" -ForegroundColor Gray
        Write-Host ""
        
        # Run the tests with timeout
        $process = Start-Process -FilePath "dotnet" -ArgumentList $testArgs -NoNewWindow -PassThru -Wait
        $exitCode = $process.ExitCode
        $duration = (Get-Date) - $startTime
        
        if ($exitCode -eq 0) {
            Write-Host "✅ $SuiteName tests completed successfully in $($duration.TotalSeconds.ToString('F1'))s" -ForegroundColor Green
            return $true
        } else {
            Write-Host "❌ $SuiteName tests failed with exit code $exitCode in $($duration.TotalSeconds.ToString('F1'))s" -ForegroundColor Red
            return $false
        }
    }
    catch {
        Write-Host "❌ Error running $SuiteName tests: $($_.Exception.Message)" -ForegroundColor Red
        return $false
    }
    finally {
        # Clean up environment
        Remove-Item Env:XUNIT_MAX_PARALLEL_THREADS -ErrorAction SilentlyContinue
    }
}

function Show-Summary {
    param([hashtable]$Results, [timespan]$TotalDuration)
    
    Write-Host ""
    Write-Host "=" * 80 -ForegroundColor Cyan
    Write-Host "  TEST EXECUTION SUMMARY" -ForegroundColor Yellow
    Write-Host "=" * 80 -ForegroundColor Cyan
    
    $passed = 0
    $failed = 0
    
    foreach ($suite in $Results.Keys) {
        $status = if ($Results[$suite]) { "✅ PASSED" } else { "❌ FAILED" }
        $color = if ($Results[$suite]) { "Green" } else { "Red" }
        
        Write-Host "  $suite`: $status" -ForegroundColor $color
        
        if ($Results[$suite]) { $passed++ } else { $failed++ }
    }
    
    Write-Host ""
    Write-Host "  Total Duration: $($TotalDuration.TotalSeconds.ToString('F1'))s" -ForegroundColor White
    Write-Host "  Suites Passed: $passed" -ForegroundColor Green
    Write-Host "  Suites Failed: $failed" -ForegroundColor $(if ($failed -eq 0) { "Green" } else { "Red" })
    Write-Host ""
    
    if ($failed -eq 0) {
        Write-Host "🎉 All test suites passed!" -ForegroundColor Green
        return $true
    } else {
        Write-Host "💥 $failed test suite(s) failed" -ForegroundColor Red
        return $false
    }
}

# Main execution
$overallStartTime = Get-Date
$results = @{}

Write-Host "SmaTrendFollower Test Suite Runner" -ForegroundColor Cyan
Write-Host "Suite: $Suite | Parallel: $Parallel | Threads: $MaxParallelThreads | Timeout: $TimeoutMinutes min" -ForegroundColor Gray

if ($Suite -eq "All") {
    # Run all test suites
    foreach ($suiteName in $TestSuites.Keys) {
        $config = $TestSuites[$suiteName]
        $results[$suiteName] = Run-TestSuite -SuiteName $suiteName -Config $config -UseParallel $Parallel -ShowVerbose $Verbose -CollectCoverage $Coverage
    }
} else {
    # Run specific test suite
    if ($TestSuites.ContainsKey($Suite)) {
        $config = $TestSuites[$Suite]
        $results[$Suite] = Run-TestSuite -SuiteName $Suite -Config $config -UseParallel $Parallel -ShowVerbose $Verbose -CollectCoverage $Coverage
    } else {
        Write-Host "❌ Unknown test suite: $Suite" -ForegroundColor Red
        Write-Host "Available suites: $($TestSuites.Keys -join ', ')" -ForegroundColor Yellow
        exit 1
    }
}

$totalDuration = (Get-Date) - $overallStartTime
$success = Show-Summary -Results $results -TotalDuration $totalDuration

# Exit with appropriate code
exit $(if ($success) { 0 } else { 1 })
