using SmaTrendFollower.Configuration;

namespace SmaTrendFollower.Services;

/// <summary>
/// Interface for managing trading cycle intervals with dynamic adjustments
/// </summary>
public interface ITradingCycleManager
{
    /// <summary>
    /// Gets the current cycle interval based on market conditions
    /// </summary>
    Task<TimeSpan> GetCurrentCycleIntervalAsync(CancellationToken cancellationToken = default);

    /// <summary>
    /// Gets a description of why the current interval was chosen
    /// </summary>
    Task<string> GetCurrentIntervalReasonAsync(CancellationToken cancellationToken = default);

    /// <summary>
    /// Updates the configuration (e.g., from command line arguments)
    /// </summary>
    void UpdateConfiguration(TradingCycleConfig config);

    /// <summary>
    /// Gets the current configuration
    /// </summary>
    TradingCycleConfig GetConfiguration();
}
