using Alpaca.Markets;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using SmaTrendFollower.Data;
using SmaTrendFollower.Models;

namespace SmaTrendFollower.Services;

/// <summary>
/// SQLite-based implementation of historical bar storage.
/// Wraps existing StockBarCacheService and IndexCacheService for unified access.
/// </summary>
public sealed class HistoricalBarStore : IBarStore
{
    private readonly IStockBarCacheService _stockBarCache;
    private readonly IIndexCacheService _indexCache;
    private readonly ILogger<HistoricalBarStore> _logger;
    private readonly BarStoreConfig _config;

    public HistoricalBarStore(
        IStockBarCacheService stockBarCache,
        IIndexCacheService indexCache,
        IConfiguration configuration,
        ILogger<HistoricalBarStore> logger)
    {
        _stockBarCache = stockBarCache;
        _indexCache = indexCache;
        _logger = logger;
        
        // Load configuration
        _config = new BarStoreConfig(
            DefaultTier: Enum.Parse<StorageTier>(configuration["STORAGE_DEFAULT_TIER"] ?? "Cold"),
            HotDataRetention: TimeSpan.FromDays(int.Parse(configuration["STORAGE_HOT_RETENTION_DAYS"] ?? "1")),
            ColdDataRetention: TimeSpan.FromDays(int.Parse(configuration["STORAGE_COLD_RETENTION_DAYS"] ?? "365")),
            EnableCompression: bool.Parse(configuration["STORAGE_ENABLE_COMPRESSION"] ?? "true"),
            CompressionThresholdDays: int.Parse(configuration["STORAGE_COMPRESSION_THRESHOLD_DAYS"] ?? "30")
        );

        _logger.LogDebug("Initialized HistoricalBarStore with config: {@Config}", _config);
    }

    public async Task SaveBarAsync(string symbol, string timeFrame, IBar bar, CancellationToken cancellationToken = default)
    {
        await SaveBarsAsync(symbol, timeFrame, new[] { bar }, cancellationToken);
    }

    public async Task SaveBarsAsync(string symbol, string timeFrame, IEnumerable<IBar> bars, CancellationToken cancellationToken = default)
    {
        try
        {
            var barsList = bars.ToList();
            if (!barsList.Any())
                return;

            if (IsIndexSymbol(symbol))
            {
                // Convert IBar to IndexBar for index cache
                var indexBars = barsList.Select(bar => new IndexBar(
                    bar.TimeUtc,
                    bar.Open,
                    bar.High,
                    bar.Low,
                    bar.Close,
                    (long)bar.Volume
                )).ToList();

                await _indexCache.CacheBarsAsync(symbol, indexBars);
            }
            else
            {
                // Use stock bar cache for regular symbols
                await _stockBarCache.CacheBarsAsync(symbol, timeFrame, barsList);
            }

            _logger.LogDebug("Saved {Count} bars for {Symbol} ({TimeFrame})", barsList.Count, symbol, timeFrame);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to save bars for {Symbol} ({TimeFrame})", symbol, timeFrame);
            throw;
        }
    }

    public async Task<List<IBar>> LoadBarsAsync(string symbol, string timeFrame, DateTime from, DateTime to, CancellationToken cancellationToken = default)
    {
        try
        {
            if (IsIndexSymbol(symbol))
            {
                var indexBars = await _indexCache.GetCachedBarsAsync(symbol, from, to);
                return indexBars.Select(ib => new IndexBarWrapper(ib)).Cast<IBar>().ToList();
            }
            else
            {
                var stockBars = await _stockBarCache.GetCachedBarsAsync(symbol, timeFrame, from, to);
                return stockBars.ToList();
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to load bars for {Symbol} ({TimeFrame}) from {From:yyyy-MM-dd} to {To:yyyy-MM-dd}", 
                symbol, timeFrame, from, to);
            return new List<IBar>();
        }
    }

    public async Task<DateTime?> GetLatestCachedDateAsync(string symbol, string timeFrame, CancellationToken cancellationToken = default)
    {
        try
        {
            if (IsIndexSymbol(symbol))
            {
                return await _indexCache.GetLatestCachedDateAsync(symbol);
            }
            else
            {
                return await _stockBarCache.GetLatestCachedDateAsync(symbol, timeFrame);
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to get latest cached date for {Symbol} ({TimeFrame})", symbol, timeFrame);
            return null;
        }
    }

    public async Task<DateTime?> GetEarliestCachedDateAsync(string symbol, string timeFrame, CancellationToken cancellationToken = default)
    {
        try
        {
            if (IsIndexSymbol(symbol))
            {
                // Index cache doesn't have GetEarliestCachedDateAsync, so we'll estimate
                var latestDate = await _indexCache.GetLatestCachedDateAsync(symbol);
                return latestDate?.AddDays(-365); // Estimate based on typical retention
            }
            else
            {
                return await _stockBarCache.GetEarliestCachedDateAsync(symbol, timeFrame);
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to get earliest cached date for {Symbol} ({TimeFrame})", symbol, timeFrame);
            return null;
        }
    }

    public async Task<bool> HasBarsAsync(string symbol, string timeFrame, DateTime from, DateTime to, CancellationToken cancellationToken = default)
    {
        try
        {
            var earliest = await GetEarliestCachedDateAsync(symbol, timeFrame, cancellationToken);
            var latest = await GetLatestCachedDateAsync(symbol, timeFrame, cancellationToken);

            if (!earliest.HasValue || !latest.HasValue)
                return false;

            return earliest.Value <= from && latest.Value >= to;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to check if bars exist for {Symbol} ({TimeFrame})", symbol, timeFrame);
            return false;
        }
    }

    public async Task<int> GetBarCountAsync(string symbol, string timeFrame, CancellationToken cancellationToken = default)
    {
        try
        {
            if (IsIndexSymbol(symbol))
            {
                // Index cache doesn't have GetBarCountAsync, so we'll get bars and count them
                var bars = await _indexCache.GetCachedBarsAsync(symbol, DateTime.MinValue, DateTime.MaxValue);
                return bars.Count();
            }
            else
            {
                // Stock cache doesn't have GetBarCountAsync either, so we'll use stats
                var stats = await _stockBarCache.GetCacheStatsAsync();
                var cacheKey = Models.StockCacheMetadata.CreateCacheKey(symbol, timeFrame);
                return stats.ContainsKey(cacheKey) ? stats[cacheKey].BarCount : 0;
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to get bar count for {Symbol} ({TimeFrame})", symbol, timeFrame);
            return 0;
        }
    }

    public async Task CleanupOldBarsAsync(DateTime olderThan, CancellationToken cancellationToken = default)
    {
        try
        {
            var retainDays = (int)(DateTime.UtcNow - olderThan).TotalDays;

            // Cleanup both stock and index caches
            await _stockBarCache.PerformMaintenanceAsync(retainDays);
            await _indexCache.PerformMaintenanceAsync(retainDays);

            _logger.LogInformation("Cleaned up bars older than {Date:yyyy-MM-dd} ({RetainDays} days)", olderThan, retainDays);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to cleanup old bars");
            throw;
        }
    }

    public async Task<BarStoreStats> GetStatsAsync(CancellationToken cancellationToken = default)
    {
        try
        {
            // Get stats from both caches
            var stockStats = await _stockBarCache.GetCacheStatsAsync();
            // Index cache doesn't have GetCacheStatsAsync, so we'll create empty stats
            var indexStats = new Dictionary<string, CacheStats>();

            var totalSymbols = stockStats.Count + indexStats.Count;
            var totalBars = stockStats.Values.Sum(s => s.BarCount) + indexStats.Values.Sum(s => s.BarCount);
            
            var allEarliestDates = stockStats.Values.Select(s => s.EarliestDate)
                .Concat(indexStats.Values.Select(s => s.EarliestDate))
                .Where(d => d.HasValue)
                .Select(d => d!.Value);

            var allLatestDates = stockStats.Values.Select(s => s.LatestDate)
                .Concat(indexStats.Values.Select(s => s.LatestDate))
                .Where(d => d.HasValue)
                .Select(d => d!.Value);

            var earliestBar = allEarliestDates.Any() ? allEarliestDates.Min() : (DateTime?)null;
            var latestBar = allLatestDates.Any() ? allLatestDates.Max() : (DateTime?)null;

            // Estimate storage size (rough calculation)
            var storageSizeBytes = totalBars * 100; // ~100 bytes per bar estimate

            var symbolCounts = new Dictionary<string, int>();
            foreach (var kvp in stockStats)
            {
                symbolCounts[kvp.Value.Symbol] = kvp.Value.BarCount;
            }
            foreach (var kvp in indexStats)
            {
                symbolCounts[kvp.Value.Symbol] = kvp.Value.BarCount;
            }

            return new BarStoreStats(
                totalSymbols,
                totalBars,
                earliestBar,
                latestBar,
                storageSizeBytes,
                symbolCounts
            );
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to get bar store stats");
            return new BarStoreStats(0, 0, null, null, 0, new Dictionary<string, int>());
        }
    }

    public async Task InitializeAsync(CancellationToken cancellationToken = default)
    {
        try
        {
            await _stockBarCache.InitializeCacheAsync();
            await _indexCache.InitializeCacheAsync();

            _logger.LogInformation("Initialized HistoricalBarStore");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to initialize HistoricalBarStore");
            throw;
        }
    }

    /// <summary>
    /// Determines if a symbol is an index symbol (starts with "I:")
    /// </summary>
    private static bool IsIndexSymbol(string symbol)
    {
        return symbol.StartsWith("I:", StringComparison.OrdinalIgnoreCase);
    }
}

/// <summary>
/// Wrapper class that implements IBar interface for IndexBar compatibility
/// </summary>
public class IndexBarWrapper : IBar
{
    private readonly IndexBar _indexBar;

    public IndexBarWrapper(IndexBar indexBar)
    {
        _indexBar = indexBar;
    }

    public string Symbol => ""; // IndexBar doesn't have symbol, will be provided by context
    public DateTime TimeUtc => _indexBar.TimeUtc;
    public decimal Open => _indexBar.Open;
    public decimal High => _indexBar.High;
    public decimal Low => _indexBar.Low;
    public decimal Close => _indexBar.Close;
    public decimal Volume => _indexBar.Volume;
    public decimal Vwap => (High + Low + Close) / 3; // Estimate VWAP
    public ulong TradeCount => (ulong)(Volume / 100); // Estimate trade count
}
