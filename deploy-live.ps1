# SmaTrendFollower Live Deployment Script
# ⚠️  LIVE TRADING DEPLOYMENT WITH REAL MONEY ⚠️

param(
    [switch]$DryRun,
    [switch]$Force,
    [string]$AlpacaKeyId,
    [string]$AlpacaSecret
)

Write-Host "🚀 SmaTrendFollower Live Deployment Script" -ForegroundColor Green
Write-Host "⚠️  WARNING: This will configure LIVE TRADING with REAL MONEY" -ForegroundColor Red

# Function to validate prerequisites
function Test-Prerequisites {
    Write-Host "`n📋 Checking Prerequisites..." -ForegroundColor Yellow
    
    # Check .NET
    try {
        $dotnetVersion = dotnet --version
        Write-Host "✅ .NET Version: $dotnetVersion" -ForegroundColor Green
    } catch {
        Write-Host "❌ .NET not found. Please install .NET 8.0+" -ForegroundColor Red
        return $false
    }
    
    # Check project builds
    Write-Host "🔨 Building project..."
    $buildResult = dotnet build --verbosity quiet
    if ($LASTEXITCODE -ne 0) {
        Write-Host "❌ Project build failed" -ForegroundColor Red
        return $false
    }
    Write-Host "✅ Project builds successfully" -ForegroundColor Green
    
    # Check tests pass
    Write-Host "🧪 Running tests..."
    $testResult = dotnet test --verbosity quiet --logger "console;verbosity=minimal"
    if ($LASTEXITCODE -ne 0) {
        Write-Host "❌ Tests failed. Fix tests before live deployment" -ForegroundColor Red
        return $false
    }
    Write-Host "✅ All tests passing" -ForegroundColor Green
    
    return $true
}

# Function to backup current configuration
function Backup-Configuration {
    Write-Host "`n💾 Backing up current configuration..." -ForegroundColor Yellow
    
    if (Test-Path ".env") {
        $timestamp = Get-Date -Format "yyyyMMdd-HHmmss"
        Copy-Item ".env" ".env.backup.$timestamp"
        Write-Host "✅ Configuration backed up to .env.backup.$timestamp" -ForegroundColor Green
    }
}

# Function to configure live environment
function Set-LiveEnvironment {
    param($KeyId, $Secret)
    
    Write-Host "`n🔧 Configuring live environment..." -ForegroundColor Yellow
    
    if (-not (Test-Path ".env.live")) {
        Write-Host "❌ .env.live template not found" -ForegroundColor Red
        return $false
    }
    
    # Copy live template
    Copy-Item ".env.live" ".env"
    
    # Update with live credentials if provided
    if ($KeyId -and $Secret) {
        Write-Host "🔑 Updating Alpaca credentials..." -ForegroundColor Yellow
        
        $envContent = Get-Content ".env"
        $envContent = $envContent -replace "REPLACE_WITH_LIVE_ALPACA_KEY_ID", $KeyId
        $envContent = $envContent -replace "REPLACE_WITH_LIVE_ALPACA_SECRET_KEY", $Secret
        $envContent | Set-Content ".env"
        
        Write-Host "✅ Live credentials configured" -ForegroundColor Green
    } else {
        Write-Host "⚠️  Manual credential update required in .env file" -ForegroundColor Yellow
        Write-Host "   Update APCA_API_KEY_ID and APCA_API_SECRET_KEY" -ForegroundColor Yellow
    }
    
    return $true
}

# Function to validate live configuration
function Test-LiveConfiguration {
    Write-Host "`n🔍 Validating live configuration..." -ForegroundColor Yellow
    
    # Test configuration validation
    Write-Host "Testing configuration validation..."
    $validateResult = dotnet run --project SmaTrendFollower.Console -- validate
    if ($LASTEXITCODE -ne 0) {
        Write-Host "❌ Configuration validation failed" -ForegroundColor Red
        return $false
    }
    
    # Test safety configuration
    Write-Host "Testing safety configuration..."
    $safetyResult = dotnet run --project SmaTrendFollower.Console -- --show-safety
    if ($LASTEXITCODE -ne 0) {
        Write-Host "❌ Safety configuration test failed" -ForegroundColor Red
        return $false
    }
    
    # Test API connectivity (dry run)
    Write-Host "Testing API connectivity..."
    $apiResult = dotnet run --project SmaTrendFollower.Console -- --dry-run account-status
    if ($LASTEXITCODE -ne 0) {
        Write-Host "❌ API connectivity test failed" -ForegroundColor Red
        return $false
    }
    
    Write-Host "✅ Live configuration validated" -ForegroundColor Green
    return $true
}

# Function to perform final safety check
function Confirm-LiveDeployment {
    Write-Host "`n⚠️  FINAL SAFETY CONFIRMATION" -ForegroundColor Red
    Write-Host "This will enable LIVE TRADING with REAL MONEY" -ForegroundColor Red
    Write-Host "Current safety limits:" -ForegroundColor Yellow
    Write-Host "  • Max Daily Loss: $120" -ForegroundColor Yellow
    Write-Host "  • Max Positions: 5" -ForegroundColor Yellow
    Write-Host "  • Max Trade Value: $1,200" -ForegroundColor Yellow
    Write-Host "  • Min Account Equity: $5,000" -ForegroundColor Yellow
    
    if (-not $Force) {
        $confirmation = Read-Host "`nType 'CONFIRM LIVE TRADING' to proceed"
        if ($confirmation -ne "CONFIRM LIVE TRADING") {
            Write-Host "❌ Deployment cancelled" -ForegroundColor Red
            return $false
        }
    }
    
    return $true
}

# Main deployment process
try {
    Write-Host "`n🚀 Starting Live Deployment Process..." -ForegroundColor Green
    
    # Step 1: Prerequisites
    if (-not (Test-Prerequisites)) {
        throw "Prerequisites check failed"
    }
    
    # Step 2: Backup
    Backup-Configuration
    
    # Step 3: Configure live environment
    if (-not (Set-LiveEnvironment -KeyId $AlpacaKeyId -Secret $AlpacaSecret)) {
        throw "Live environment configuration failed"
    }
    
    # Step 4: Validate configuration
    if (-not (Test-LiveConfiguration)) {
        throw "Live configuration validation failed"
    }
    
    # Step 5: Final confirmation (unless dry run)
    if (-not $DryRun) {
        if (-not (Confirm-LiveDeployment)) {
            throw "Live deployment cancelled by user"
        }
    }
    
    # Step 6: Success
    Write-Host "`n🎉 LIVE DEPLOYMENT SUCCESSFUL!" -ForegroundColor Green
    
    if ($DryRun) {
        Write-Host "✅ Dry run completed - no live trading enabled" -ForegroundColor Green
        Write-Host "To enable live trading, run: dotnet run -- --confirm run" -ForegroundColor Yellow
    } else {
        Write-Host "✅ Live trading environment configured and ready" -ForegroundColor Green
        Write-Host "To start live trading, run: dotnet run -- --confirm run" -ForegroundColor Yellow
    }
    
    Write-Host "`n📊 Next Steps:" -ForegroundColor Cyan
    Write-Host "1. Monitor first trades closely" -ForegroundColor White
    Write-Host "2. Check Discord notifications" -ForegroundColor White
    Write-Host "3. Review daily performance" -ForegroundColor White
    Write-Host "4. Run: dotnet run -- metrics" -ForegroundColor White
    
} catch {
    Write-Host "`n❌ DEPLOYMENT FAILED: $($_.Exception.Message)" -ForegroundColor Red
    Write-Host "Restoring backup configuration..." -ForegroundColor Yellow
    
    # Restore backup if it exists
    $latestBackup = Get-ChildItem ".env.backup.*" | Sort-Object LastWriteTime -Descending | Select-Object -First 1
    if ($latestBackup) {
        Copy-Item $latestBackup.FullName ".env"
        Write-Host "✅ Configuration restored from backup" -ForegroundColor Green
    }
    
    exit 1
}
