using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Configuration;
using System.Text.Json;
using System.Text;
using Alpaca.Markets;

namespace SmaTrendFollower.Services;

/// <summary>
/// SEC Filing Filter - Monitors SEC filings to pause trading during high-risk periods
/// Uses Brave Search API to find recent SEC filings and EDGAR for detailed analysis
/// </summary>
public sealed class SecFilingFilter : ISecFilingFilter
{
    private readonly ILogger<SecFilingFilter> _logger;
    private readonly ILiveStateStore _stateStore;
    private readonly HttpClient _httpClient;
    private readonly SecFilingConfig _config;

    public SecFilingFilter(
        ILogger<SecFilingFilter> logger,
        ILiveStateStore stateStore,
        HttpClient httpClient,
        IConfiguration configuration)
    {
        _logger = logger;
        _stateStore = stateStore;
        _httpClient = httpClient;
        _config = new SecFilingConfig
        {
            CacheHours = configuration.GetValue("SEC_CACHE_HOURS", 6),
            BraveApiKey = configuration.GetValue<string>("BRAVE_API_KEY") ?? "",
            MaxFilingsPerSymbol = configuration.GetValue("SEC_MAX_FILINGS_PER_SYMBOL", 5),
            HighRiskFormTypes = configuration.GetSection("SEC_HIGH_RISK_FORMS").Get<string[]>() ?? 
                new[] { "8-K", "10-K", "10-Q", "DEF 14A", "S-1", "S-3" },
            EnableBraveSearch = configuration.GetValue("SEC_ENABLE_BRAVE_SEARCH", true),
            EnableEdgarDirect = configuration.GetValue("SEC_ENABLE_EDGAR_DIRECT", false)
        };
    }

    /// <summary>
    /// Determines if trading is allowed based on recent SEC filings
    /// </summary>
    public async Task<bool> IsEligibleAsync(string symbol, IReadOnlyList<IBar> bars)
    {
        try
        {
            var analysis = await AnalyzeSecFilingsAsync(symbol);
            
            var isEligible = analysis.RiskLevel != FilingRiskLevel.High && 
                           analysis.RiskLevel != FilingRiskLevel.Critical;

            if (!isEligible)
            {
                _logger.LogWarning("SEC Filing filter BLOCKED {Symbol}: {Reason} (Risk: {Risk})",
                    symbol, analysis.Reasoning, analysis.RiskLevel);
            }
            else
            {
                _logger.LogDebug("SEC Filing filter PASSED for {Symbol}: {Reason}",
                    symbol, analysis.Reasoning);
            }

            return isEligible;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error in SEC filing analysis for {Symbol}, allowing trade", symbol);
            return true; // Fail open
        }
    }

    /// <summary>
    /// Analyzes recent SEC filings for a symbol
    /// </summary>
    private async Task<SecFilingAnalysis> AnalyzeSecFilingsAsync(string symbol)
    {
        var cacheKey = $"sec_filing_analysis_{symbol}";
        
        // Try cache first
        var cached = await _stateStore.GetMarketStateAsync<SecFilingAnalysis>(cacheKey);
        if (cached != null && cached.AnalyzedAt > DateTime.UtcNow.AddHours(-_config.CacheHours))
        {
            return cached;
        }

        try
        {
            var filings = await GetRecentFilingsAsync(symbol);
            var analysis = AnalyzeFilings(symbol, filings);

            // Cache the result
            await _stateStore.SetMarketStateAsync(cacheKey, analysis, TimeSpan.FromHours(_config.CacheHours));

            return analysis;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to analyze SEC filings for {Symbol}", symbol);
            return new SecFilingAnalysis(
                symbol,
                FilingRiskLevel.Unknown,
                "Error retrieving SEC filings",
                new List<SecFiling>(),
                DateTime.UtcNow
            );
        }
    }

    /// <summary>
    /// Gets recent SEC filings for a symbol
    /// </summary>
    private async Task<List<SecFiling>> GetRecentFilingsAsync(string symbol)
    {
        var filings = new List<SecFiling>();

        if (_config.EnableBraveSearch && !string.IsNullOrEmpty(_config.BraveApiKey))
        {
            var braveFilings = await SearchBraveForFilingsAsync(symbol);
            filings.AddRange(braveFilings);
        }

        if (_config.EnableEdgarDirect)
        {
            var edgarFilings = await SearchEdgarDirectAsync(symbol);
            filings.AddRange(edgarFilings);
        }

        // Remove duplicates and sort by date
        return filings
            .GroupBy(f => f.AccessionNumber)
            .Select(g => g.First())
            .OrderByDescending(f => f.FilingDate)
            .Take(_config.MaxFilingsPerSymbol)
            .ToList();
    }

    /// <summary>
    /// Searches Brave for recent SEC filings
    /// </summary>
    private async Task<List<SecFiling>> SearchBraveForFilingsAsync(string symbol)
    {
        try
        {
            var query = $"{symbol} SEC filing site:sec.gov";
            var url = $"https://api.search.brave.com/res/v1/web/search?q={Uri.EscapeDataString(query)}&count=10";

            var request = new HttpRequestMessage(HttpMethod.Get, url);
            request.Headers.Add("X-Subscription-Token", _config.BraveApiKey);

            var response = await _httpClient.SendAsync(request);
            response.EnsureSuccessStatusCode();

            var content = await response.Content.ReadAsStringAsync();
            var searchResult = JsonSerializer.Deserialize<BraveSearchResult>(content);

            var filings = new List<SecFiling>();
            
            if (searchResult?.web?.results != null)
            {
                foreach (var result in searchResult.web.results.Take(5))
                {
                    if (result.url?.Contains("sec.gov") == true && 
                        result.title?.Contains(symbol) == true)
                    {
                        var filing = ParseBraveResult(symbol, result);
                        if (filing != null)
                        {
                            filings.Add(filing);
                        }
                    }
                }
            }

            return filings;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error searching Brave for SEC filings for {Symbol}", symbol);
            return new List<SecFiling>();
        }
    }

    /// <summary>
    /// Searches EDGAR directly for filings
    /// </summary>
    private async Task<List<SecFiling>> SearchEdgarDirectAsync(string symbol)
    {
        try
        {
            // EDGAR API endpoint for company filings
            var url = $"https://data.sec.gov/submissions/CIK{symbol}.json";
            
            var request = new HttpRequestMessage(HttpMethod.Get, url);
            request.Headers.Add("User-Agent", "SmaTrendFollower/1.0 (<EMAIL>)");

            var response = await _httpClient.SendAsync(request);
            if (!response.IsSuccessStatusCode)
            {
                return new List<SecFiling>();
            }

            var content = await response.Content.ReadAsStringAsync();
            var edgarData = JsonSerializer.Deserialize<EdgarSubmissionData>(content);

            var filings = new List<SecFiling>();
            
            if (edgarData?.filings?.recent != null)
            {
                var recent = edgarData.filings.recent;
                for (int i = 0; i < Math.Min(recent.form?.Length ?? 0, _config.MaxFilingsPerSymbol); i++)
                {
                    if (recent.form?[i] != null && recent.filingDate?[i] != null)
                    {
                        var filing = new SecFiling(
                            recent.accessionNumber?[i] ?? "",
                            symbol,
                            recent.form[i],
                            recent.filingDate[i],
                            recent.reportDate?[i] ?? recent.filingDate[i],
                            $"https://www.sec.gov/Archives/edgar/data/{edgarData.cik}/{recent.accessionNumber?[i]?.Replace("-", "")}/{recent.primaryDocument?[i]}",
                            "EDGAR Direct"
                        );
                        filings.Add(filing);
                    }
                }
            }

            return filings;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error searching EDGAR for {Symbol}", symbol);
            return new List<SecFiling>();
        }
    }

    /// <summary>
    /// Parses a Brave search result into a SEC filing
    /// </summary>
    private SecFiling? ParseBraveResult(string symbol, BraveWebResult result)
    {
        try
        {
            // Extract form type from title or description
            var formType = ExtractFormType(result.title + " " + result.description);
            if (string.IsNullOrEmpty(formType))
            {
                return null;
            }

            // Try to extract date from description or use current date
            var filingDate = ExtractDateFromText(result.description) ?? DateTime.UtcNow.Date;

            return new SecFiling(
                ExtractAccessionNumber(result.url) ?? Guid.NewGuid().ToString(),
                symbol,
                formType,
                filingDate,
                filingDate,
                result.url ?? "",
                "Brave Search"
            );
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error parsing Brave result for {Symbol}", symbol);
            return null;
        }
    }

    /// <summary>
    /// Analyzes filings to determine risk level
    /// </summary>
    private SecFilingAnalysis AnalyzeFilings(string symbol, List<SecFiling> filings)
    {
        if (!filings.Any())
        {
            return new SecFilingAnalysis(
                symbol,
                FilingRiskLevel.Low,
                "No recent SEC filings found",
                filings,
                DateTime.UtcNow
            );
        }

        var recentFilings = filings.Where(f => f.FilingDate > DateTime.UtcNow.AddDays(-7)).ToList();
        var highRiskFilings = recentFilings.Where(f => _config.HighRiskFormTypes.Contains(f.FormType)).ToList();

        var riskLevel = DetermineRiskLevel(recentFilings, highRiskFilings);
        var reasoning = GenerateReasoning(recentFilings, highRiskFilings, riskLevel);

        return new SecFilingAnalysis(symbol, riskLevel, reasoning, filings, DateTime.UtcNow);
    }

    /// <summary>
    /// Determines risk level based on filings
    /// </summary>
    private FilingRiskLevel DetermineRiskLevel(List<SecFiling> recentFilings, List<SecFiling> highRiskFilings)
    {
        if (!recentFilings.Any())
        {
            return FilingRiskLevel.Low;
        }

        if (highRiskFilings.Count >= 3)
        {
            return FilingRiskLevel.Critical;
        }

        if (highRiskFilings.Count >= 2)
        {
            return FilingRiskLevel.High;
        }

        if (highRiskFilings.Count >= 1)
        {
            return FilingRiskLevel.Medium;
        }

        return FilingRiskLevel.Low;
    }

    /// <summary>
    /// Generates reasoning for the risk assessment
    /// </summary>
    private string GenerateReasoning(List<SecFiling> recentFilings, List<SecFiling> highRiskFilings, FilingRiskLevel riskLevel)
    {
        if (!recentFilings.Any())
        {
            return "No recent SEC filings in the past 7 days";
        }

        var sb = new StringBuilder();
        sb.Append($"{recentFilings.Count} recent filing(s) in past 7 days");

        if (highRiskFilings.Any())
        {
            sb.Append($", {highRiskFilings.Count} high-risk form(s): ");
            sb.Append(string.Join(", ", highRiskFilings.Select(f => f.FormType).Distinct()));
        }

        return sb.ToString();
    }

    /// <summary>
    /// Extracts form type from text
    /// </summary>
    private string? ExtractFormType(string text)
    {
        var formTypes = new[] { "8-K", "10-K", "10-Q", "DEF 14A", "S-1", "S-3", "4", "3", "5" };
        
        foreach (var formType in formTypes)
        {
            if (text.Contains(formType, StringComparison.OrdinalIgnoreCase))
            {
                return formType;
            }
        }

        return null;
    }

    /// <summary>
    /// Extracts accession number from URL
    /// </summary>
    private string? ExtractAccessionNumber(string? url)
    {
        if (string.IsNullOrEmpty(url))
            return null;

        // Look for pattern like 0000123456-21-000001
        var match = System.Text.RegularExpressions.Regex.Match(url, @"(\d{10}-\d{2}-\d{6})");
        return match.Success ? match.Groups[1].Value : null;
    }

    /// <summary>
    /// Extracts date from text
    /// </summary>
    private DateTime? ExtractDateFromText(string? text)
    {
        if (string.IsNullOrEmpty(text))
            return null;

        // Try to find date patterns
        var datePatterns = new[]
        {
            @"\b(\d{4}-\d{2}-\d{2})\b",
            @"\b(\d{2}/\d{2}/\d{4})\b",
            @"\b(\d{1,2}/\d{1,2}/\d{4})\b"
        };

        foreach (var pattern in datePatterns)
        {
            var match = System.Text.RegularExpressions.Regex.Match(text, pattern);
            if (match.Success && DateTime.TryParse(match.Groups[1].Value, out var date))
            {
                return date;
            }
        }

        return null;
    }

    public async Task<SecFilingAnalysis> GetFilingAnalysisAsync(string symbol)
    {
        return await AnalyzeSecFilingsAsync(symbol);
    }

    public async Task<List<SecFiling>> GetRecentFilingsAsync(string symbol, int maxCount = 10)
    {
        return await GetRecentFilingsAsync(symbol);
    }
}

/// <summary>
/// Interface for SEC filing filter
/// </summary>
public interface ISecFilingFilter
{
    Task<bool> IsEligibleAsync(string symbol, IReadOnlyList<IBar> bars);
    Task<SecFilingAnalysis> GetFilingAnalysisAsync(string symbol);
    Task<List<SecFiling>> GetRecentFilingsAsync(string symbol, int maxCount = 10);
}

/// <summary>
/// SEC filing risk levels
/// </summary>
public enum FilingRiskLevel : byte
{
    Unknown = 0,
    Low = 1,
    Medium = 2,
    High = 3,
    Critical = 4
}

/// <summary>
/// SEC filing analysis result
/// </summary>
public record SecFilingAnalysis(
    string Symbol,
    FilingRiskLevel RiskLevel,
    string Reasoning,
    List<SecFiling> RecentFilings,
    DateTime AnalyzedAt
);

/// <summary>
/// SEC filing record
/// </summary>
public record SecFiling(
    string AccessionNumber,
    string Symbol,
    string FormType,
    DateTime FilingDate,
    DateTime ReportDate,
    string Url,
    string Source
);

/// <summary>
/// Configuration for SEC filing filter
/// </summary>
public record SecFilingConfig
{
    public int CacheHours { get; init; } = 6;
    public string BraveApiKey { get; init; } = "";
    public int MaxFilingsPerSymbol { get; init; } = 5;
    public string[] HighRiskFormTypes { get; init; } = Array.Empty<string>();
    public bool EnableBraveSearch { get; init; } = true;
    public bool EnableEdgarDirect { get; init; } = false;
}

// Brave Search API models
public record BraveSearchResult(BraveWebResults? web);
public record BraveWebResults(BraveWebResult[]? results);
public record BraveWebResult(string? title, string? url, string? description);

// EDGAR API models
public record EdgarSubmissionData(string? cik, EdgarFilings? filings);
public record EdgarFilings(EdgarRecentFilings? recent);
public record EdgarRecentFilings(
    string[]? accessionNumber,
    string[]? form,
    DateTime[]? filingDate,
    DateTime[]? reportDate,
    string[]? primaryDocument
);
