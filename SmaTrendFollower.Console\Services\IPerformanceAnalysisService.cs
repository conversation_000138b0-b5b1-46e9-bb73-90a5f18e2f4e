using SmaTrendFollower.Models;

namespace SmaTrendFollower.Services;

/// <summary>
/// Industrial-grade performance analysis service for continuous algorithm optimization
/// </summary>
public interface IPerformanceAnalysisService
{
    /// <summary>
    /// Analyzes algorithm performance across different market regimes
    /// </summary>
    Task<PerformanceAttributionReport> AnalyzePerformanceAttributionAsync(
        DateTime startDate, DateTime endDate, CancellationToken cancellationToken = default);

    /// <summary>
    /// Runs walk-forward analysis to optimize strategy parameters
    /// </summary>
    Task<WalkForwardAnalysisResult> RunWalkForwardAnalysisAsync(
        WalkForwardConfiguration config, CancellationToken cancellationToken = default);

    /// <summary>
    /// Performs Monte Carlo simulation for strategy validation
    /// </summary>
    Task<MonteCarloResult> RunMonteCarloSimulationAsync(
        MonteCarloConfiguration config, CancellationToken cancellationToken = default);

    /// <summary>
    /// Calculates comprehensive risk-adjusted performance metrics
    /// </summary>
    Task<RiskAdjustedMetrics> CalculateRiskAdjustedMetricsAsync(
        IEnumerable<TradeMetric> trades, IEnumerable<decimal> benchmarkReturns);

    /// <summary>
    /// Detects performance degradation and suggests optimizations
    /// </summary>
    Task<PerformanceDegradationAlert> DetectPerformanceDegradationAsync(
        TimeSpan lookbackPeriod, CancellationToken cancellationToken = default);

    /// <summary>
    /// Generates strategy optimization recommendations
    /// </summary>
    Task<StrategyOptimizationRecommendations> GenerateOptimizationRecommendationsAsync(
        CancellationToken cancellationToken = default);
}

/// <summary>
/// Configuration for walk-forward analysis
/// </summary>
public record WalkForwardConfiguration(
    DateTime StartDate,
    DateTime EndDate,
    TimeSpan TrainingWindow,
    TimeSpan TestingWindow,
    TimeSpan StepSize,
    IReadOnlyList<ParameterRange> ParametersToOptimize,
    OptimizationObjective Objective = OptimizationObjective.SharpeRatio,
    int MinTradesRequired = 10
);

/// <summary>
/// Parameter range for optimization
/// </summary>
public record ParameterRange(
    string ParameterName,
    decimal MinValue,
    decimal MaxValue,
    decimal StepSize,
    ParameterType Type = ParameterType.Decimal
);

/// <summary>
/// Configuration for Monte Carlo simulation
/// </summary>
public record MonteCarloConfiguration(
    int NumberOfSimulations,
    TimeSpan SimulationPeriod,
    decimal InitialCapital,
    IReadOnlyList<string> Symbols,
    MonteCarloMethod Method = MonteCarloMethod.BootstrapResampling,
    decimal ConfidenceLevel = 0.95m
);

/// <summary>
/// Performance attribution report
/// </summary>
public record PerformanceAttributionReport(
    decimal TotalReturn,
    Dictionary<string, decimal> RegimeContributions,
    Dictionary<string, decimal> SignalQualityContributions,
    Dictionary<string, decimal> ExecutionContributions,
    Dictionary<string, decimal> RiskManagementContributions,
    IReadOnlyList<AttributionPeriod> PeriodBreakdown,
    DateTime GeneratedAt
);

/// <summary>
/// Attribution analysis for a specific period
/// </summary>
public record AttributionPeriod(
    DateTime StartDate,
    DateTime EndDate,
    string MarketRegime,
    decimal PeriodReturn,
    decimal AlphaGeneration,
    decimal ExecutionCost,
    decimal RiskAdjustment,
    int TradeCount
);

/// <summary>
/// Walk-forward analysis result
/// </summary>
public record WalkForwardAnalysisResult(
    IReadOnlyList<WalkForwardPeriod> Periods,
    OptimalParameterSet BestParameters,
    decimal AverageOutOfSampleReturn,
    decimal OutOfSampleSharpeRatio,
    decimal MaxDrawdown,
    decimal ParameterStability,
    DateTime AnalysisDate
);

/// <summary>
/// Individual walk-forward period result
/// </summary>
public record WalkForwardPeriod(
    DateTime TrainingStart,
    DateTime TrainingEnd,
    DateTime TestingStart,
    DateTime TestingEnd,
    OptimalParameterSet OptimalParameters,
    decimal InSampleReturn,
    decimal OutOfSampleReturn,
    decimal InSampleSharpe,
    decimal OutOfSampleSharpe
);

/// <summary>
/// Optimal parameter set
/// </summary>
public record OptimalParameterSet(
    Dictionary<string, decimal> Parameters,
    decimal OptimizationScore,
    OptimizationObjective Objective,
    DateTime OptimizedAt
);

/// <summary>
/// Monte Carlo simulation result
/// </summary>
public record MonteCarloResult(
    decimal ExpectedReturn,
    decimal StandardDeviation,
    decimal ValueAtRisk,
    decimal ConditionalValueAtRisk,
    decimal MaximumDrawdown,
    decimal ProbabilityOfLoss,
    IReadOnlyList<SimulationPath> Paths,
    MonteCarloStatistics Statistics,
    DateTime SimulatedAt
);

/// <summary>
/// Individual simulation path
/// </summary>
public record SimulationPath(
    int PathNumber,
    IReadOnlyList<decimal> Returns,
    IReadOnlyList<decimal> CumulativeReturns,
    decimal FinalReturn,
    decimal MaxDrawdown
);

/// <summary>
/// Monte Carlo statistics
/// </summary>
public record MonteCarloStatistics(
    decimal Percentile5,
    decimal Percentile25,
    decimal Percentile50,
    decimal Percentile75,
    decimal Percentile95,
    decimal WorstCase,
    decimal BestCase,
    decimal SuccessRate
);

/// <summary>
/// Risk-adjusted performance metrics
/// </summary>
public record RiskAdjustedMetrics(
    decimal SharpeRatio,
    decimal SortinoRatio,
    decimal CalmarRatio,
    decimal MaxDrawdown,
    decimal ValueAtRisk95,
    decimal ConditionalVaR95,
    decimal Beta,
    decimal Alpha,
    decimal InformationRatio,
    decimal TrackingError,
    decimal DownsideDeviation,
    DateTime CalculatedAt
);

/// <summary>
/// Performance degradation alert
/// </summary>
public record PerformanceDegradationAlert(
    bool IsDegraded,
    DegradationSeverity Severity,
    IReadOnlyList<DegradationFactor> Factors,
    IReadOnlyList<string> Recommendations,
    decimal CurrentPerformance,
    decimal ExpectedPerformance,
    decimal PerformanceGap,
    DateTime DetectedAt
);

/// <summary>
/// Factor contributing to performance degradation
/// </summary>
public record DegradationFactor(
    string FactorName,
    decimal Impact,
    string Description,
    FactorType Type
);

/// <summary>
/// Strategy optimization recommendations
/// </summary>
public record StrategyOptimizationRecommendations(
    IReadOnlyList<ParameterAdjustment> ParameterAdjustments,
    IReadOnlyList<RiskAdjustment> RiskAdjustments,
    IReadOnlyList<ExecutionImprovement> ExecutionImprovements,
    decimal ExpectedImpact,
    RecommendationConfidence Confidence,
    DateTime GeneratedAt
);

/// <summary>
/// Parameter adjustment recommendation
/// </summary>
public record ParameterAdjustment(
    string ParameterName,
    decimal CurrentValue,
    decimal RecommendedValue,
    decimal ExpectedImpact,
    string Rationale
);

/// <summary>
/// Risk adjustment recommendation
/// </summary>
public record RiskAdjustment(
    string RiskFactor,
    decimal CurrentLevel,
    decimal RecommendedLevel,
    string Rationale
);

/// <summary>
/// Execution improvement recommendation
/// </summary>
public record ExecutionImprovement(
    string Area,
    string CurrentApproach,
    string RecommendedApproach,
    decimal ExpectedCostReduction,
    string Implementation
);

/// <summary>
/// Enums for configuration
/// </summary>
public enum OptimizationObjective
{
    SharpeRatio,
    Return,
    CalmarRatio,
    SortinoRatio,
    MaxDrawdown,
    WinRate
}

public enum ParameterType
{
    Decimal,
    Integer,
    Boolean,
    Percentage
}

public enum MonteCarloMethod
{
    BootstrapResampling,
    ParametricSimulation,
    HistoricalSimulation
}

public enum DegradationSeverity
{
    Low,
    Medium,
    High,
    Critical
}

public enum FactorType
{
    MarketRegime,
    SignalQuality,
    Execution,
    RiskManagement,
    External
}

public enum RecommendationConfidence
{
    Low,
    Medium,
    High,
    VeryHigh
}
