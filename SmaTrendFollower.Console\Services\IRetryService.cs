using System.Net;

namespace SmaTrendFollower.Services;

/// <summary>
/// Unified interface for retry logic with rate limiting and exponential back-off
/// Consolidates retry patterns from existing rate limiting helpers
/// </summary>
public interface IRetryService
{
    /// <summary>
    /// Executes an operation with retry logic and rate limiting
    /// </summary>
    /// <typeparam name="T">Return type of the operation</typeparam>
    /// <param name="operation">The operation to execute</param>
    /// <param name="operationName">Name for logging and monitoring</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Result of the operation</returns>
    Task<T> ExecuteAsync<T>(Func<Task<T>> operation, string operationName = "Unknown", CancellationToken cancellationToken = default);

    /// <summary>
    /// Executes an operation without return value with retry logic and rate limiting
    /// </summary>
    /// <param name="operation">The operation to execute</param>
    /// <param name="operationName">Name for logging and monitoring</param>
    /// <param name="cancellationToken">Cancellation token</param>
    Task ExecuteAsync(Func<Task> operation, string operationName = "Unknown", CancellationToken cancellationToken = default);

    /// <summary>
    /// Executes an HTTP operation with retry logic and rate limiting
    /// </summary>
    /// <param name="httpOperation">The HTTP operation to execute</param>
    /// <param name="operationName">Name for logging and monitoring</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>HTTP response message</returns>
    Task<HttpResponseMessage> ExecuteHttpAsync(Func<Task<HttpResponseMessage>> httpOperation, string operationName = "Unknown", CancellationToken cancellationToken = default);

    /// <summary>
    /// Gets current retry configuration
    /// </summary>
    RetryConfiguration GetConfiguration();

    /// <summary>
    /// Updates retry configuration
    /// </summary>
    /// <param name="configuration">New retry configuration</param>
    void UpdateConfiguration(RetryConfiguration configuration);

    /// <summary>
    /// Gets current rate limiting statistics
    /// </summary>
    RateLimitStatistics GetStatistics();

    /// <summary>
    /// Resets rate limiting counters and statistics
    /// </summary>
    void ResetStatistics();
}

/// <summary>
/// Configuration for retry behavior
/// </summary>
public readonly record struct RetryConfiguration(
    int MaxRetries = 5,
    TimeSpan BaseDelay = default,
    TimeSpan MaxDelay = default,
    double BackoffMultiplier = 2.0,
    TimeSpan JitterRange = default,
    int RateLimit = 0,
    TimeSpan RateLimitWindow = default,
    HttpStatusCode[] RetriableHttpStatusCodes = null!,
    string[] RetriableExceptionMessages = null!
)
{
    public static RetryConfiguration Default => new(
        MaxRetries: 5,
        BaseDelay: TimeSpan.FromSeconds(1),
        MaxDelay: TimeSpan.FromMinutes(5),
        BackoffMultiplier: 2.0,
        JitterRange: TimeSpan.FromMilliseconds(1000),
        RateLimit: 0,
        RateLimitWindow: TimeSpan.FromMinutes(1),
        RetriableHttpStatusCodes: new[] { 
            HttpStatusCode.TooManyRequests, 
            HttpStatusCode.ServiceUnavailable, 
            HttpStatusCode.RequestTimeout,
            HttpStatusCode.InternalServerError 
        },
        RetriableExceptionMessages: new[] { 
            "TooManyRequests", 
            "429", 
            "rate limit" 
        }
    );

    public static RetryConfiguration ForAlpaca => Default with
    {
        RateLimit = 200,
        RateLimitWindow = TimeSpan.FromMinutes(1),
        BaseDelay = TimeSpan.FromMilliseconds(300)
    };

    public static RetryConfiguration ForPolygon => Default with
    {
        RateLimit = 5,
        RateLimitWindow = TimeSpan.FromSeconds(1),
        BaseDelay = TimeSpan.FromMilliseconds(200)
    };
}

/// <summary>
/// Statistics about rate limiting and retry behavior
/// </summary>
public readonly record struct RateLimitStatistics(
    int TotalRequests,
    int SuccessfulRequests,
    int FailedRequests,
    int RetriedRequests,
    int RateLimitedRequests,
    TimeSpan AverageResponseTime,
    DateTime WindowStart,
    int CurrentWindowRequests,
    DateTime LastRequest,
    string[] RecentErrors
);

/// <summary>
/// Specialized retry service for Alpaca API
/// </summary>
public interface IAlpacaRetryService : IRetryService
{
    /// <summary>
    /// Executes an Alpaca-specific operation with optimized retry logic
    /// </summary>
    Task<T> ExecuteAlpacaAsync<T>(Func<Task<T>> operation, string operationName = "Unknown", CancellationToken cancellationToken = default);
}

/// <summary>
/// Specialized retry service for Polygon API
/// </summary>
public interface IPolygonRetryService : IRetryService
{
    /// <summary>
    /// Executes a Polygon-specific operation with optimized retry logic
    /// </summary>
    Task<T> ExecutePolygonAsync<T>(Func<Task<T>> operation, string operationName = "Unknown", CancellationToken cancellationToken = default);
}
