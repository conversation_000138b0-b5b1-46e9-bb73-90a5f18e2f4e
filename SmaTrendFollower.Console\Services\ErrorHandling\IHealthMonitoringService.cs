namespace SmaTrendFollower.Services.ErrorHandling;

/// <summary>
/// Health monitoring service for tracking external dependencies and system health
/// </summary>
public interface IHealthMonitoringService
{
    /// <summary>
    /// Check health of a specific service
    /// </summary>
    /// <param name="serviceName">Name of the service to check</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Health check result</returns>
    Task<HealthCheckResult> CheckServiceHealthAsync(string serviceName, CancellationToken cancellationToken = default);

    /// <summary>
    /// Check health of all monitored services
    /// </summary>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Overall health status</returns>
    Task<OverallHealthStatus> CheckOverallHealthAsync(CancellationToken cancellationToken = default);

    /// <summary>
    /// Register a health check for a service
    /// </summary>
    /// <param name="serviceName">Name of the service</param>
    /// <param name="healthCheck">Health check function</param>
    /// <param name="options">Health check options</param>
    void RegisterHealthCheck(string serviceName, Func<CancellationToken, Task<HealthCheckResult>> healthCheck, HealthCheckOptions? options = null);

    /// <summary>
    /// Start continuous health monitoring
    /// </summary>
    /// <param name="cancellationToken">Cancellation token</param>
    Task StartMonitoringAsync(CancellationToken cancellationToken = default);

    /// <summary>
    /// Stop health monitoring
    /// </summary>
    Task StopMonitoringAsync();

    /// <summary>
    /// Get health history for a service
    /// </summary>
    /// <param name="serviceName">Name of the service</param>
    /// <param name="timeRange">Time range for history</param>
    /// <returns>Health history</returns>
    IEnumerable<HealthCheckResult> GetHealthHistory(string serviceName, TimeSpan timeRange);

    /// <summary>
    /// Get current health status of all services
    /// </summary>
    /// <returns>Dictionary of service names and their current health status</returns>
    Dictionary<string, HealthCheckResult> GetCurrentHealthStatus();

    /// <summary>
    /// Event fired when a service health status changes
    /// </summary>
    event EventHandler<HealthStatusChangedEventArgs>? HealthStatusChanged;
}

/// <summary>
/// Health check result
/// </summary>
public sealed class HealthCheckResult
{
    public string ServiceName { get; init; } = string.Empty;
    public HealthStatus Status { get; init; }
    public string? Description { get; init; }
    public TimeSpan ResponseTime { get; init; }
    public DateTime Timestamp { get; init; } = DateTime.UtcNow;
    public Dictionary<string, object> Data { get; init; } = new();
    public Exception? Exception { get; init; }

    public static HealthCheckResult Healthy(string serviceName, string? description = null, TimeSpan? responseTime = null)
    {
        return new HealthCheckResult
        {
            ServiceName = serviceName,
            Status = HealthStatus.Healthy,
            Description = description ?? "Service is healthy",
            ResponseTime = responseTime ?? TimeSpan.Zero
        };
    }

    public static HealthCheckResult Degraded(string serviceName, string? description = null, TimeSpan? responseTime = null)
    {
        return new HealthCheckResult
        {
            ServiceName = serviceName,
            Status = HealthStatus.Degraded,
            Description = description ?? "Service is degraded",
            ResponseTime = responseTime ?? TimeSpan.Zero
        };
    }

    public static HealthCheckResult Unhealthy(string serviceName, string? description = null, Exception? exception = null, TimeSpan? responseTime = null)
    {
        return new HealthCheckResult
        {
            ServiceName = serviceName,
            Status = HealthStatus.Unhealthy,
            Description = description ?? "Service is unhealthy",
            Exception = exception,
            ResponseTime = responseTime ?? TimeSpan.Zero
        };
    }

    public HealthCheckResult WithData(string key, object value)
    {
        Data[key] = value;
        return this;
    }
}

/// <summary>
/// Overall health status
/// </summary>
public sealed class OverallHealthStatus
{
    public HealthStatus Status { get; init; }
    public TimeSpan TotalCheckTime { get; init; }
    public DateTime Timestamp { get; init; } = DateTime.UtcNow;
    public Dictionary<string, HealthCheckResult> ServiceResults { get; init; } = new();
    public int HealthyCount => ServiceResults.Values.Count(r => r.Status == HealthStatus.Healthy);
    public int DegradedCount => ServiceResults.Values.Count(r => r.Status == HealthStatus.Degraded);
    public int UnhealthyCount => ServiceResults.Values.Count(r => r.Status == HealthStatus.Unhealthy);
    public int TotalServices => ServiceResults.Count;
}

/// <summary>
/// Health status enumeration
/// </summary>
public enum HealthStatus
{
    /// <summary>
    /// Service is healthy and functioning normally
    /// </summary>
    Healthy,

    /// <summary>
    /// Service is functioning but with degraded performance
    /// </summary>
    Degraded,

    /// <summary>
    /// Service is unhealthy and not functioning properly
    /// </summary>
    Unhealthy
}

/// <summary>
/// Health check options
/// </summary>
public sealed class HealthCheckOptions
{
    /// <summary>
    /// Interval between health checks
    /// </summary>
    public TimeSpan CheckInterval { get; set; } = TimeSpan.FromMinutes(1);

    /// <summary>
    /// Timeout for individual health checks
    /// </summary>
    public TimeSpan Timeout { get; set; } = TimeSpan.FromSeconds(30);

    /// <summary>
    /// Number of consecutive failures before marking as unhealthy
    /// </summary>
    public int FailureThreshold { get; set; } = 3;

    /// <summary>
    /// Number of consecutive successes needed to mark as healthy again
    /// </summary>
    public int SuccessThreshold { get; set; } = 2;

    /// <summary>
    /// Whether to enable alerting for this service
    /// </summary>
    public bool EnableAlerting { get; set; } = true;

    /// <summary>
    /// Tags for categorizing the health check
    /// </summary>
    public HashSet<string> Tags { get; set; } = new();

    /// <summary>
    /// Priority level for alerting
    /// </summary>
    public AlertPriority AlertPriority { get; set; } = AlertPriority.Medium;
}

/// <summary>
/// Alert priority levels
/// </summary>
public enum AlertPriority
{
    Low,
    Medium,
    High,
    Critical
}

/// <summary>
/// Health status changed event arguments
/// </summary>
public sealed class HealthStatusChangedEventArgs : EventArgs
{
    public string ServiceName { get; init; } = string.Empty;
    public HealthStatus PreviousStatus { get; init; }
    public HealthStatus NewStatus { get; init; }
    public HealthCheckResult Result { get; init; } = null!;
    public DateTime Timestamp { get; init; } = DateTime.UtcNow;
}

/// <summary>
/// Service health context for tracking state
/// </summary>
internal sealed class ServiceHealthContext
{
    public string ServiceName { get; set; } = string.Empty;
    public Func<CancellationToken, Task<HealthCheckResult>> HealthCheck { get; set; } = null!;
    public HealthCheckOptions Options { get; set; } = new();
    public HealthStatus CurrentStatus { get; set; } = HealthStatus.Healthy;
    public int ConsecutiveFailures { get; set; }
    public int ConsecutiveSuccesses { get; set; }
    public DateTime LastCheckTime { get; set; }
    public Queue<HealthCheckResult> History { get; set; } = new();
    public object Lock { get; set; } = new();
}
