using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;
using System.Collections.Concurrent;

namespace SmaTrendFollower.Services;

/// <summary>
/// Real-time market monitoring service that tracks price movements, market conditions,
/// and generates alerts for trading opportunities and risk events.
/// </summary>
public sealed class RealTimeMarketMonitor : BackgroundService, IRealTimeMarketMonitor
{
    private readonly IMarketDataService _marketDataService;
    private readonly ILiveStateStore _liveStateStore;
    private readonly ILogger<RealTimeMarketMonitor> _logger;
    private readonly MarketMonitorConfig _config;

    private readonly ConcurrentDictionary<string, MarketSnapshot> _marketSnapshots = new();
    private readonly ConcurrentQueue<MarketAlert> _alertQueue = new();
    private readonly SemaphoreSlim _monitoringLock = new(1, 1);

    // Events for real-time notifications
    public event EventHandler<MarketAlertEventArgs>? AlertGenerated;
    public event EventHandler<MarketConditionEventArgs>? MarketConditionChanged;
    public event EventHandler<PriceMovementEventArgs>? SignificantPriceMovement;

    public RealTimeMarketMonitor(
        IMarketDataService marketDataService,
        ILiveStateStore liveStateStore,
        ILogger<RealTimeMarketMonitor> logger,
        MarketMonitorConfig? config = null)
    {
        _marketDataService = marketDataService;
        _liveStateStore = liveStateStore;
        _logger = logger;
        _config = config ?? new MarketMonitorConfig();
    }

    /// <summary>
    /// Adds a symbol to real-time monitoring
    /// </summary>
    public Task AddSymbolToMonitorAsync(string symbol, MonitoringCriteria criteria)
    {
        try
        {
            var snapshot = new MarketSnapshot(
                symbol,
                0, // Will be updated on first price fetch
                0,
                DateTime.UtcNow,
                criteria,
                new List<PricePoint>(),
                MarketTrend.Unknown,
                0
            );

            _marketSnapshots[symbol] = snapshot;
            
            _logger.LogInformation("Added {Symbol} to real-time monitoring with criteria: {Criteria}",
                symbol, criteria);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to add {Symbol} to monitoring", symbol);
        }

        return Task.CompletedTask;
    }

    /// <summary>
    /// Removes a symbol from monitoring
    /// </summary>
    public Task RemoveSymbolFromMonitorAsync(string symbol)
    {
        try
        {
            _marketSnapshots.TryRemove(symbol, out _);
            _logger.LogInformation("Removed {Symbol} from real-time monitoring", symbol);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to remove {Symbol} from monitoring", symbol);
        }

        return Task.CompletedTask;
    }

    /// <summary>
    /// Gets current market snapshot for a symbol
    /// </summary>
    public MarketSnapshot? GetMarketSnapshot(string symbol)
    {
        return _marketSnapshots.TryGetValue(symbol, out var snapshot) ? snapshot : null;
    }

    /// <summary>
    /// Gets all monitored symbols and their snapshots
    /// </summary>
    public Dictionary<string, MarketSnapshot> GetAllMarketSnapshots()
    {
        return new Dictionary<string, MarketSnapshot>(_marketSnapshots);
    }

    /// <summary>
    /// Gets recent market alerts
    /// </summary>
    public List<MarketAlert> GetRecentAlerts(int count = 50)
    {
        return _alertQueue.TakeLast(count).ToList();
    }

    /// <summary>
    /// Background service execution - continuous market monitoring
    /// </summary>
    protected override async Task ExecuteAsync(CancellationToken stoppingToken)
    {
        _logger.LogInformation("RealTimeMarketMonitor started");

        // Initialize with key market indices
        await InitializeMarketIndicesAsync();

        while (!stoppingToken.IsCancellationRequested)
        {
            try
            {
                await UpdateMarketSnapshotsAsync(stoppingToken);
                await ProcessMarketAlertsAsync(stoppingToken);
                await MonitorMarketConditionsAsync(stoppingToken);
                
                await Task.Delay(_config.UpdateInterval, stoppingToken);
            }
            catch (OperationCanceledException)
            {
                break;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error in market monitoring");
                await Task.Delay(TimeSpan.FromMinutes(1), stoppingToken);
            }
        }

        _logger.LogInformation("RealTimeMarketMonitor stopped");
    }

    /// <summary>
    /// Initializes monitoring for key market indices
    /// </summary>
    private async Task InitializeMarketIndicesAsync()
    {
        var indices = new[]
        {
            ("SPY", new MonitoringCriteria(0.02m, true, true, true)),  // S&P 500
            ("QQQ", new MonitoringCriteria(0.025m, true, true, true)), // NASDAQ
            ("IWM", new MonitoringCriteria(0.03m, true, true, true)),  // Russell 2000
            ("VIX", new MonitoringCriteria(0.05m, true, false, true))  // Volatility Index
        };

        foreach (var (symbol, criteria) in indices)
        {
            await AddSymbolToMonitorAsync(symbol, criteria);
        }

        _logger.LogInformation("Initialized monitoring for {Count} market indices", indices.Length);
    }

    /// <summary>
    /// Updates market snapshots with latest price data
    /// </summary>
    private async Task UpdateMarketSnapshotsAsync(CancellationToken cancellationToken)
    {
        if (!_marketSnapshots.Any())
            return;

        await _monitoringLock.WaitAsync(cancellationToken);
        try
        {
            var symbols = _marketSnapshots.Keys.ToList();
            var updateTasks = symbols.Select(UpdateSymbolSnapshotAsync);
            
            await Task.WhenAll(updateTasks);
        }
        finally
        {
            _monitoringLock.Release();
        }
    }

    /// <summary>
    /// Updates snapshot for a single symbol
    /// </summary>
    private async Task UpdateSymbolSnapshotAsync(string symbol)
    {
        try
        {
            if (!_marketSnapshots.TryGetValue(symbol, out var currentSnapshot))
                return;

            // Get latest price data
            var endDate = DateTime.UtcNow;
            var startDate = endDate.AddMinutes(-5); // Last 5 minutes
            
            var response = await _marketDataService.GetStockBarsAsync(symbol, startDate, endDate);
            var latestBar = response.Items.LastOrDefault();
            
            if (latestBar == null)
                return;

            var currentPrice = latestBar.Close;
            var previousPrice = currentSnapshot.CurrentPrice;
            var priceChange = previousPrice > 0 ? (currentPrice - previousPrice) / previousPrice : 0;

            // Update price history
            var updatedPriceHistory = currentSnapshot.PriceHistory.ToList();
            updatedPriceHistory.Add(new PricePoint(currentPrice, DateTime.UtcNow));
            
            // Keep only recent price points
            if (updatedPriceHistory.Count > _config.MaxPriceHistoryPoints)
            {
                updatedPriceHistory = updatedPriceHistory.TakeLast(_config.MaxPriceHistoryPoints).ToList();
            }

            // Determine trend
            var trend = DetermineTrend(updatedPriceHistory);

            // Calculate volatility
            var volatility = CalculateVolatility(updatedPriceHistory);

            // Update snapshot
            var updatedSnapshot = currentSnapshot with
            {
                CurrentPrice = currentPrice,
                PriceChange = priceChange,
                LastUpdated = DateTime.UtcNow,
                PriceHistory = updatedPriceHistory,
                Trend = trend,
                Volatility = volatility
            };

            _marketSnapshots[symbol] = updatedSnapshot;

            // Check for alerts
            await CheckForAlertsAsync(updatedSnapshot, previousPrice);
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "Failed to update snapshot for {Symbol}", symbol);
        }
    }

    /// <summary>
    /// Checks for alert conditions and generates alerts
    /// </summary>
    private async Task CheckForAlertsAsync(MarketSnapshot snapshot, decimal previousPrice)
    {
        var criteria = snapshot.MonitoringCriteria;

        // Price movement alert
        if (criteria.MonitorPriceMovements && Math.Abs(snapshot.PriceChange) >= criteria.PriceMovementThreshold)
        {
            var alert = new MarketAlert(
                snapshot.Symbol,
                MarketAlertType.PriceMovement,
                $"Price moved {snapshot.PriceChange:P2} to {snapshot.CurrentPrice:F2}",
                snapshot.CurrentPrice,
                DateTime.UtcNow,
                AlertSeverity.Medium
            );

            await EnqueueAlertAsync(alert);
            
            SignificantPriceMovement?.Invoke(this, new PriceMovementEventArgs(
                snapshot.Symbol, previousPrice, snapshot.CurrentPrice, snapshot.PriceChange));
        }

        // Volatility alert
        if (criteria.MonitorVolatility && snapshot.Volatility > _config.HighVolatilityThreshold)
        {
            var alert = new MarketAlert(
                snapshot.Symbol,
                MarketAlertType.HighVolatility,
                $"High volatility detected: {snapshot.Volatility:F4}",
                snapshot.CurrentPrice,
                DateTime.UtcNow,
                AlertSeverity.High
            );

            await EnqueueAlertAsync(alert);
        }

        // Trend change alert
        if (criteria.MonitorTrendChanges && HasTrendChanged(snapshot))
        {
            var alert = new MarketAlert(
                snapshot.Symbol,
                MarketAlertType.TrendChange,
                $"Trend changed to {snapshot.Trend}",
                snapshot.CurrentPrice,
                DateTime.UtcNow,
                AlertSeverity.Medium
            );

            await EnqueueAlertAsync(alert);
        }
    }

    /// <summary>
    /// Determines market trend from price history
    /// </summary>
    private MarketTrend DetermineTrend(List<PricePoint> priceHistory)
    {
        if (priceHistory.Count < 3)
            return MarketTrend.Unknown;

        var recentPrices = priceHistory.TakeLast(5).Select(p => p.Price).ToList();
        
        var upMoves = 0;
        var downMoves = 0;

        for (int i = 1; i < recentPrices.Count; i++)
        {
            if (recentPrices[i] > recentPrices[i - 1])
                upMoves++;
            else if (recentPrices[i] < recentPrices[i - 1])
                downMoves++;
        }

        if (upMoves > downMoves * 1.5)
            return MarketTrend.Bullish;
        else if (downMoves > upMoves * 1.5)
            return MarketTrend.Bearish;
        else
            return MarketTrend.Sideways;
    }

    /// <summary>
    /// Calculates price volatility from recent price history
    /// </summary>
    private decimal CalculateVolatility(List<PricePoint> priceHistory)
    {
        if (priceHistory.Count < 2)
            return 0;

        var returns = new List<decimal>();
        for (int i = 1; i < priceHistory.Count; i++)
        {
            var prevPrice = priceHistory[i - 1].Price;
            var currentPrice = priceHistory[i].Price;
            
            if (prevPrice > 0)
            {
                returns.Add((currentPrice - prevPrice) / prevPrice);
            }
        }

        if (!returns.Any())
            return 0;

        var avgReturn = returns.Average();
        var variance = returns.Sum(r => (r - avgReturn) * (r - avgReturn)) / returns.Count;
        
        return (decimal)Math.Sqrt((double)variance);
    }

    /// <summary>
    /// Checks if trend has changed significantly
    /// </summary>
    private bool HasTrendChanged(MarketSnapshot snapshot)
    {
        // Simple implementation - could be enhanced with more sophisticated logic
        return snapshot.PriceHistory.Count >= 2 && 
               Math.Abs(snapshot.PriceChange) > _config.TrendChangeThreshold;
    }

    /// <summary>
    /// Processes market alert queue
    /// </summary>
    private async Task ProcessMarketAlertsAsync(CancellationToken cancellationToken)
    {
        var processedCount = 0;
        while (_alertQueue.TryDequeue(out var alert) && processedCount < _config.MaxAlertsPerCycle)
        {
            try
            {
                // Store alert for persistence using existing API
                await _liveStateStore.SetMarketStateAsync($"market_alert:{alert.Symbol}:{alert.Timestamp:yyyyMMddHHmmss}",
                    alert, TimeSpan.FromDays(7));

                // Trigger event
                AlertGenerated?.Invoke(this, new MarketAlertEventArgs(alert));

                _logger.LogInformation("Market alert: {Type} for {Symbol} - {Description}",
                    alert.AlertType, alert.Symbol, alert.Description);

                processedCount++;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error processing market alert for {Symbol}", alert.Symbol);
            }
        }
    }

    /// <summary>
    /// Monitors overall market conditions
    /// </summary>
    private async Task MonitorMarketConditionsAsync(CancellationToken cancellationToken)
    {
        try
        {
            // Check VIX levels for market stress
            if (_marketSnapshots.TryGetValue("VIX", out var vixSnapshot))
            {
                var vixLevel = vixSnapshot.CurrentPrice;
                var marketCondition = DetermineMarketCondition(vixLevel);
                
                // Store current market condition using existing API
                await _liveStateStore.SetMarketStateAsync("market_condition", marketCondition, TimeSpan.FromHours(1));

                MarketConditionChanged?.Invoke(this, new MarketConditionEventArgs(
                    marketCondition, vixLevel, DateTime.UtcNow));
            }
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "Error monitoring market conditions");
        }
    }

    /// <summary>
    /// Determines market condition based on VIX level
    /// </summary>
    private MarketCondition DetermineMarketCondition(decimal vixLevel)
    {
        return vixLevel switch
        {
            < 15 => MarketCondition.Calm,
            < 20 => MarketCondition.Normal,
            < 25 => MarketCondition.Elevated,
            < 35 => MarketCondition.Stressed,
            _ => MarketCondition.Crisis
        };
    }

    /// <summary>
    /// Enqueues an alert for processing
    /// </summary>
    private Task EnqueueAlertAsync(MarketAlert alert)
    {
        _alertQueue.Enqueue(alert);
        
        // Limit queue size
        while (_alertQueue.Count > _config.MaxAlertQueueSize)
        {
            _alertQueue.TryDequeue(out _);
        }

        return Task.CompletedTask;
    }

    public override void Dispose()
    {
        _monitoringLock?.Dispose();
        base.Dispose();
    }
}

/// <summary>
/// Interface for real-time market monitoring
/// </summary>
public interface IRealTimeMarketMonitor
{
    event EventHandler<MarketAlertEventArgs>? AlertGenerated;
    event EventHandler<MarketConditionEventArgs>? MarketConditionChanged;
    event EventHandler<PriceMovementEventArgs>? SignificantPriceMovement;

    Task AddSymbolToMonitorAsync(string symbol, MonitoringCriteria criteria);
    Task RemoveSymbolFromMonitorAsync(string symbol);
    MarketSnapshot? GetMarketSnapshot(string symbol);
    Dictionary<string, MarketSnapshot> GetAllMarketSnapshots();
    List<MarketAlert> GetRecentAlerts(int count = 50);
}

/// <summary>
/// Configuration for market monitoring
/// </summary>
public record MarketMonitorConfig(
    TimeSpan UpdateInterval = default,
    int MaxPriceHistoryPoints = 100,
    int MaxAlertsPerCycle = 10,
    int MaxAlertQueueSize = 1000,
    decimal HighVolatilityThreshold = 0.05m,
    decimal TrendChangeThreshold = 0.02m
)
{
    public MarketMonitorConfig() : this(TimeSpan.FromSeconds(30), 100, 10, 1000, 0.05m, 0.02m) { }
}

/// <summary>
/// Monitoring criteria for a symbol
/// </summary>
public record MonitoringCriteria(
    decimal PriceMovementThreshold,
    bool MonitorPriceMovements,
    bool MonitorVolatility,
    bool MonitorTrendChanges
);

/// <summary>
/// Market snapshot for a symbol
/// </summary>
public record MarketSnapshot(
    string Symbol,
    decimal CurrentPrice,
    decimal PriceChange,
    DateTime LastUpdated,
    MonitoringCriteria MonitoringCriteria,
    List<PricePoint> PriceHistory,
    MarketTrend Trend,
    decimal Volatility
);

/// <summary>
/// Price point with timestamp
/// </summary>
public record PricePoint(decimal Price, DateTime Timestamp);

/// <summary>
/// Market alert information
/// </summary>
public record MarketAlert(
    string Symbol,
    MarketAlertType AlertType,
    string Description,
    decimal Price,
    DateTime Timestamp,
    AlertSeverity Severity
);

/// <summary>
/// Market trend classification
/// </summary>
public enum MarketTrend
{
    Unknown,
    Bullish,
    Bearish,
    Sideways
}

/// <summary>
/// Market alert types
/// </summary>
public enum MarketAlertType
{
    PriceMovement,
    HighVolatility,
    TrendChange,
    VolumeSpike,
    TechnicalBreakout
}

/// <summary>
/// Alert severity levels
/// </summary>
public enum AlertSeverity
{
    Low,
    Medium,
    High,
    Critical
}

/// <summary>
/// Market condition classification
/// </summary>
public enum MarketCondition
{
    Calm,
    Normal,
    Elevated,
    Stressed,
    Crisis
}

/// <summary>
/// Event arguments for market alerts
/// </summary>
public record MarketAlertEventArgs(MarketAlert Alert);

/// <summary>
/// Event arguments for market condition changes
/// </summary>
public record MarketConditionEventArgs(
    MarketCondition Condition,
    decimal VixLevel,
    DateTime Timestamp
);

/// <summary>
/// Event arguments for significant price movements
/// </summary>
public record PriceMovementEventArgs(
    string Symbol,
    decimal PreviousPrice,
    decimal CurrentPrice,
    decimal PercentChange
);
