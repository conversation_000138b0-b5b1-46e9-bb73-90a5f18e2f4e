# SmaTrendFollower Backup Scheduling Script
# Automates backup scheduling and management

param(
    [ValidateSet("Install", "Uninstall", "Status", "RunNow", "Test")]
    [string]$Action = "Install",
    [ValidateSet("Hourly", "Daily", "Weekly")]
    [string]$Frequency = "Daily",
    [string]$BackupLocation = ".\backups",
    [int]$RetentionDays = 30,
    [switch]$IncludeLogs,
    [switch]$CompressBackups,
    [switch]$Verbose
)

# Color output functions
function Write-Info { param($Message) Write-Host $Message -ForegroundColor Cyan }
function Write-Success { param($Message) Write-Host $Message -ForegroundColor Green }
function Write-Warning { param($Message) Write-Host $Message -ForegroundColor Yellow }
function Write-Error { param($Message) Write-Host $Message -ForegroundColor Red }

$taskName = "SmaTrendFollower-AutoBackup"
$scriptPath = (Get-Location).Path
$backupScriptPath = "$scriptPath\backup-system.ps1"

function Install-BackupSchedule {
    Write-Info "📅 Installing backup schedule..."
    
    try {
        # Check if backup script exists
        if (-not (Test-Path $backupScriptPath)) {
            throw "Backup script not found: $backupScriptPath"
        }
        
        # Create backup directory if it doesn't exist
        if (-not (Test-Path $BackupLocation)) {
            New-Item -ItemType Directory -Path $BackupLocation -Force | Out-Null
            Write-Success "✅ Created backup directory: $BackupLocation"
        }
        
        # Determine schedule based on frequency
        $trigger = switch ($Frequency) {
            "Hourly" { 
                New-ScheduledTaskTrigger -Once -At (Get-Date).AddMinutes(5) -RepetitionInterval (New-TimeSpan -Hours 1)
            }
            "Daily" { 
                New-ScheduledTaskTrigger -Daily -At "02:00AM"
            }
            "Weekly" { 
                New-ScheduledTaskTrigger -Weekly -DaysOfWeek Sunday -At "01:00AM"
            }
        }
        
        # Build backup command arguments
        $backupArgs = @(
            "-File", "`"$backupScriptPath`"",
            "-BackupPath", "`"$BackupLocation\scheduled_$(Get-Date -Format 'yyyyMMdd_HHmmss')`""
        )
        
        if ($IncludeLogs) { $backupArgs += "-IncludeLogs" }
        if ($CompressBackups) { $backupArgs += "-CompressBackup" }
        if ($Verbose) { $backupArgs += "-Verbose" }
        
        # Create scheduled task action
        $action = New-ScheduledTaskAction -Execute "PowerShell.exe" -Argument ($backupArgs -join " ") -WorkingDirectory $scriptPath
        
        # Create task settings
        $settings = New-ScheduledTaskSettingsSet -AllowStartIfOnBatteries -DontStopIfGoingOnBatteries -StartWhenAvailable
        
        # Create principal (run as current user)
        $principal = New-ScheduledTaskPrincipal -UserId $env:USERNAME -LogonType Interactive
        
        # Register the scheduled task
        Register-ScheduledTask -TaskName $taskName -Trigger $trigger -Action $action -Settings $settings -Principal $principal -Description "Automated backup for SmaTrendFollower trading system" -Force
        
        Write-Success "✅ Backup schedule installed successfully"
        Write-Info "  Task Name: $taskName"
        Write-Info "  Frequency: $Frequency"
        Write-Info "  Backup Location: $BackupLocation"
        Write-Info "  Include Logs: $IncludeLogs"
        Write-Info "  Compress Backups: $CompressBackups"
        
        # Install cleanup task
        Install-CleanupSchedule
        
    } catch {
        Write-Error "❌ Failed to install backup schedule: $($_.Exception.Message)"
        throw
    }
}

function Install-CleanupSchedule {
    Write-Info "🧹 Installing backup cleanup schedule..."
    
    try {
        $cleanupTaskName = "SmaTrendFollower-BackupCleanup"
        
        # Create cleanup script content
        $cleanupScript = @"
# SmaTrendFollower Backup Cleanup Script
`$backupLocation = "$BackupLocation"
`$retentionDays = $RetentionDays

Write-Host "🧹 Starting backup cleanup..." -ForegroundColor Cyan
Write-Host "Backup Location: `$backupLocation" -ForegroundColor White
Write-Host "Retention Days: `$retentionDays" -ForegroundColor White

if (Test-Path `$backupLocation) {
    `$cutoffDate = (Get-Date).AddDays(-`$retentionDays)
    `$oldBackups = Get-ChildItem `$backupLocation | Where-Object { 
        `$_.LastWriteTime -lt `$cutoffDate -and 
        (`$_.Name -match '^\d{8}_\d{6}' -or `$_.Name -match 'scheduled_\d{8}_\d{6}')
    }
    
    `$deletedCount = 0
    foreach (`$backup in `$oldBackups) {
        try {
            Remove-Item `$backup.FullName -Recurse -Force
            Write-Host "🗑️ Deleted old backup: `$(`$backup.Name)" -ForegroundColor Yellow
            `$deletedCount++
        } catch {
            Write-Host "❌ Failed to delete `$(`$backup.Name): `$(`$_.Exception.Message)" -ForegroundColor Red
        }
    }
    
    Write-Host "✅ Cleanup complete. Deleted `$deletedCount old backups." -ForegroundColor Green
} else {
    Write-Host "⚠️ Backup location not found: `$backupLocation" -ForegroundColor Yellow
}
"@
        
        $cleanupScriptPath = "$scriptPath\cleanup-backups.ps1"
        $cleanupScript | Out-File $cleanupScriptPath -Encoding UTF8
        
        # Create weekly cleanup trigger
        $cleanupTrigger = New-ScheduledTaskTrigger -Weekly -DaysOfWeek Monday -At "03:00AM"
        
        # Create cleanup action
        $cleanupAction = New-ScheduledTaskAction -Execute "PowerShell.exe" -Argument "-File `"$cleanupScriptPath`"" -WorkingDirectory $scriptPath
        
        # Create task settings
        $cleanupSettings = New-ScheduledTaskSettingsSet -AllowStartIfOnBatteries -DontStopIfGoingOnBatteries
        
        # Create principal
        $cleanupPrincipal = New-ScheduledTaskPrincipal -UserId $env:USERNAME -LogonType Interactive
        
        # Register cleanup task
        Register-ScheduledTask -TaskName $cleanupTaskName -Trigger $cleanupTrigger -Action $cleanupAction -Settings $cleanupSettings -Principal $cleanupPrincipal -Description "Automated cleanup of old SmaTrendFollower backups" -Force
        
        Write-Success "✅ Backup cleanup schedule installed"
        Write-Info "  Cleanup Task: $cleanupTaskName"
        Write-Info "  Retention: $RetentionDays days"
        Write-Info "  Schedule: Weekly on Monday at 3:00 AM"
        
    } catch {
        Write-Warning "⚠️ Failed to install cleanup schedule: $($_.Exception.Message)"
    }
}

function Uninstall-BackupSchedule {
    Write-Info "🗑️ Uninstalling backup schedule..."
    
    try {
        # Remove main backup task
        $task = Get-ScheduledTask -TaskName $taskName -ErrorAction SilentlyContinue
        if ($task) {
            Unregister-ScheduledTask -TaskName $taskName -Confirm:$false
            Write-Success "✅ Removed backup task: $taskName"
        } else {
            Write-Warning "⚠️ Backup task not found: $taskName"
        }
        
        # Remove cleanup task
        $cleanupTaskName = "SmaTrendFollower-BackupCleanup"
        $cleanupTask = Get-ScheduledTask -TaskName $cleanupTaskName -ErrorAction SilentlyContinue
        if ($cleanupTask) {
            Unregister-ScheduledTask -TaskName $cleanupTaskName -Confirm:$false
            Write-Success "✅ Removed cleanup task: $cleanupTaskName"
        }
        
        # Remove cleanup script
        $cleanupScriptPath = "$scriptPath\cleanup-backups.ps1"
        if (Test-Path $cleanupScriptPath) {
            Remove-Item $cleanupScriptPath -Force
            Write-Success "✅ Removed cleanup script"
        }
        
        Write-Success "✅ Backup schedule uninstalled successfully"
        
    } catch {
        Write-Error "❌ Failed to uninstall backup schedule: $($_.Exception.Message)"
        throw
    }
}

function Show-BackupStatus {
    Write-Info "📊 Backup Schedule Status"
    Write-Info "=" * 50
    
    try {
        # Check main backup task
        $task = Get-ScheduledTask -TaskName $taskName -ErrorAction SilentlyContinue
        if ($task) {
            Write-Success "✅ Backup Task: $taskName"
            Write-Info "  State: $($task.State)"
            Write-Info "  Last Run: $($task.LastRunTime)"
            Write-Info "  Next Run: $($task.NextRunTime)"
            Write-Info "  Last Result: $($task.LastTaskResult)"
            
            # Get trigger details
            $triggers = $task.Triggers
            foreach ($trigger in $triggers) {
                Write-Info "  Trigger: $($trigger.CimClass.CimClassName)"
                if ($trigger.StartBoundary) {
                    Write-Info "  Start Time: $($trigger.StartBoundary)"
                }
                if ($trigger.Repetition -and $trigger.Repetition.Interval) {
                    Write-Info "  Repeat Interval: $($trigger.Repetition.Interval)"
                }
            }
        } else {
            Write-Warning "⚠️ Backup task not found: $taskName"
        }
        
        # Check cleanup task
        $cleanupTaskName = "SmaTrendFollower-BackupCleanup"
        $cleanupTask = Get-ScheduledTask -TaskName $cleanupTaskName -ErrorAction SilentlyContinue
        if ($cleanupTask) {
            Write-Success "`n✅ Cleanup Task: $cleanupTaskName"
            Write-Info "  State: $($cleanupTask.State)"
            Write-Info "  Last Run: $($cleanupTask.LastRunTime)"
            Write-Info "  Next Run: $($cleanupTask.NextRunTime)"
        } else {
            Write-Warning "`n⚠️ Cleanup task not found: $cleanupTaskName"
        }
        
        # Check backup location
        Write-Info "`n📁 Backup Location: $BackupLocation"
        if (Test-Path $BackupLocation) {
            $backups = Get-ChildItem $BackupLocation | Where-Object { 
                $_.Name -match '^\d{8}_\d{6}' -or $_.Name -match 'scheduled_\d{8}_\d{6}'
            }
            Write-Info "  Total Backups: $($backups.Count)"
            
            if ($backups.Count -gt 0) {
                $totalSize = ($backups | Measure-Object -Property Length -Sum).Sum
                Write-Info "  Total Size: $([math]::Round($totalSize / 1MB, 2)) MB"
                
                $newest = $backups | Sort-Object LastWriteTime -Descending | Select-Object -First 1
                $oldest = $backups | Sort-Object LastWriteTime | Select-Object -First 1
                
                Write-Info "  Newest: $($newest.Name) ($($newest.LastWriteTime))"
                Write-Info "  Oldest: $($oldest.Name) ($($oldest.LastWriteTime))"
            }
        } else {
            Write-Warning "  Backup location does not exist"
        }
        
    } catch {
        Write-Error "❌ Failed to get backup status: $($_.Exception.Message)"
    }
}

function Run-BackupNow {
    Write-Info "🚀 Running backup now..."
    
    try {
        $backupPath = "$BackupLocation\manual_$(Get-Date -Format 'yyyyMMdd_HHmmss')"
        
        $backupArgs = @(
            "-BackupPath", $backupPath
        )
        
        if ($IncludeLogs) { $backupArgs += "-IncludeLogs" }
        if ($CompressBackups) { $backupArgs += "-CompressBackup" }
        if ($Verbose) { $backupArgs += "-Verbose" }
        
        & $backupScriptPath @backupArgs
        
        if ($LASTEXITCODE -eq 0) {
            Write-Success "✅ Manual backup completed successfully"
            Write-Info "  Backup Location: $backupPath"
        } else {
            throw "Backup script returned error code: $LASTEXITCODE"
        }
        
    } catch {
        Write-Error "❌ Manual backup failed: $($_.Exception.Message)"
        throw
    }
}

function Test-BackupSchedule {
    Write-Info "🧪 Testing backup schedule..."
    
    try {
        # Test backup script exists and is executable
        if (-not (Test-Path $backupScriptPath)) {
            throw "Backup script not found: $backupScriptPath"
        }
        Write-Success "✅ Backup script found"
        
        # Test backup location is accessible
        if (-not (Test-Path $BackupLocation)) {
            New-Item -ItemType Directory -Path $BackupLocation -Force | Out-Null
        }
        Write-Success "✅ Backup location accessible"
        
        # Test scheduled task exists
        $task = Get-ScheduledTask -TaskName $taskName -ErrorAction SilentlyContinue
        if ($task) {
            Write-Success "✅ Scheduled task exists"
            
            # Test task can be started
            try {
                Start-ScheduledTask -TaskName $taskName
                Start-Sleep -Seconds 5
                $taskInfo = Get-ScheduledTask -TaskName $taskName
                Write-Success "✅ Scheduled task can be started"
                Write-Info "  Current State: $($taskInfo.State)"
            } catch {
                Write-Warning "⚠️ Could not start scheduled task: $($_.Exception.Message)"
            }
        } else {
            Write-Warning "⚠️ Scheduled task not found"
        }
        
        Write-Success "✅ Backup schedule test completed"
        
    } catch {
        Write-Error "❌ Backup schedule test failed: $($_.Exception.Message)"
        throw
    }
}

# Main execution
Write-Success "📅 SmaTrendFollower Backup Scheduler"
Write-Info "Action: $Action"

try {
    switch ($Action) {
        "Install" { 
            Install-BackupSchedule 
            Write-Info "`n💡 Use 'schedule-backups.ps1 -Action Status' to check status"
        }
        "Uninstall" { 
            Uninstall-BackupSchedule 
        }
        "Status" { 
            Show-BackupStatus 
        }
        "RunNow" { 
            Run-BackupNow 
        }
        "Test" { 
            Test-BackupSchedule 
        }
    }
    
    Write-Success "`n🎉 Backup scheduler operation completed successfully!"
    
} catch {
    Write-Error "❌ BACKUP SCHEDULER FAILED: $($_.Exception.Message)"
    exit 1
}
