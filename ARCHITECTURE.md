# SmaTrendFollower Architecture Documentation

## Overview

SmaTrendFollower is a sophisticated .NET 8 algorithmic trading system implementing an SMA-following momentum strategy with comprehensive risk management. The architecture follows clean architecture principles with dependency injection, extensive testing, and modular design patterns. The system supports both simple core trading operations and advanced features like real-time streaming, market regime detection, and dynamic universe selection.

## Architecture Principles

- **Single-Shot Execution**: Manual one-shot execution flow (no scheduled services)
- **Dependency Injection**: Full DI container with proper service scoping
- **Clean Architecture**: Separation of concerns with distinct layers
- **Testability**: Comprehensive unit and integration test coverage
- **Extensibility**: Modular design supporting optional enhanced features
- **Performance**: Optimized caching and data processing strategies

## System Architecture

```
┌─────────────────────────────────────────────────────────────────┐
│                        SmaTrendFollower                        │
├─────────────────────────────────────────────────────────────────┤
│                     Presentation Layer                         │
│  ┌─────────────────┐  ┌─────────────────┐  ┌─────────────────┐ │
│  │   Console App   │  │  Discord Bot    │  │   Web API       │ │
│  │   (Program.cs)  │  │  Notifications  │  │  (Future)       │ │
│  └─────────────────┘  └─────────────────┘  └─────────────────┘ │
├─────────────────────────────────────────────────────────────────┤
│                     Application Layer                          │
│  ┌─────────────────┐  ┌─────────────────┐  ┌─────────────────┐ │
│  │ TradingService  │  │ SignalGenerator │  │  RiskManager    │ │
│  │ (Orchestrator)  │  │ (Strategy)      │  │  (Position)     │ │
│  └─────────────────┘  └─────────────────┘  └─────────────────┘ │
│  ┌─────────────────┐  ┌─────────────────┐  ┌─────────────────┐ │
│  │ TradeExecutor   │  │ PortfolioGate   │  │  StopManager    │ │
│  │ (Execution)     │  │ (Market Gate)   │  │  (Risk Control) │ │
│  └─────────────────┘  └─────────────────┘  └─────────────────┘ │
├─────────────────────────────────────────────────────────────────┤
│                     Infrastructure Layer                       │
│  ┌─────────────────┐  ┌─────────────────┐  ┌─────────────────┐ │
│  │ MarketDataSvc   │  │ StreamingData   │  │ CacheServices   │ │
│  │ (Alpaca+Polygon)│  │ (Real-time)     │  │ (Redis+SQLite)  │ │
│  └─────────────────┘  └─────────────────┘  └─────────────────┘ │
│  ┌─────────────────┐  ┌─────────────────┐  ┌─────────────────┐ │
│  │ AlpacaFactory   │  │ PolygonFactory  │  │ DatabaseCtx     │ │
│  │ (Trading API)   │  │ (Market Data)   │  │ (Persistence)   │ │
│  └─────────────────┘  └─────────────────┘  └─────────────────┘ │
├─────────────────────────────────────────────────────────────────┤
│                        Domain Layer                            │
│  ┌─────────────────┐  ┌─────────────────┐  ┌─────────────────┐ │
│  │ TradingSignal   │  │ MarketRegime    │  │ TradingPrimitives│ │
│  │ (Core Models)   │  │ (Market State)  │  │ (Value Objects) │ │
│  └─────────────────┘  └─────────────────┘  └─────────────────┘ │
└─────────────────────────────────────────────────────────────────┘
```

## Core Components

### 1. Trading Services (Application Layer)

#### TradingService
- **Purpose**: Main orchestrator for trading cycles
- **Responsibilities**: 
  - Coordinates all trading operations
  - Manages execution flow
  - Handles safety checks and market regime validation
- **Dependencies**: All core trading services

#### SignalGenerator
- **Purpose**: Generates trading signals using SMA momentum strategy
- **Strategy**: 
  - Universe screening (SPY + top-500 tickers)
  - Technical filters: Close > SMA50 && Close > SMA200 && ATR/Close < 3%
  - Ranking by 6-month return
  - Returns top N symbols
- **Dependencies**: MarketDataService, UniverseProvider

#### RiskManager
- **Purpose**: Position sizing and risk control
- **Algorithm**: 
  - Risk capital = min(account equity × 1%, $1000)
  - Position size = riskDollars / (ATR14 × price)
  - Maximum position constraint
- **Dependencies**: MarketDataService

#### TradeExecutor
- **Purpose**: Order execution and management
- **Pattern**: 
  - Limit-on-Open entry at lastClose × 1.002
  - GTC stop-loss at entry - 2×ATR
  - Cancel existing orders before new trades
- **Dependencies**: AlpacaClientFactory

#### PortfolioGate
- **Purpose**: Market condition gating
- **Logic**: Only trade when SPY close > SPY SMA200
- **Dependencies**: MarketDataService

### 2. Data Services (Infrastructure Layer)

#### MarketDataService
- **Purpose**: Unified market data interface
- **Data Sources**: 
  - Alpaca Markets: Account data, positions, stock/ETF bars
  - Polygon.io: Index data (SPX, VIX), options data, fallback bars
- **Features**: 
  - Automatic fallback mechanisms
  - Rate limiting and retry logic
  - Timestamp normalization (UTC)
  - Caching integration

#### StreamingDataService
- **Purpose**: Real-time market data streaming
- **Capabilities**: 
  - Live quotes and bars (Alpaca WebSocket)
  - Trade execution updates
  - Index/volatility triggers (Polygon WebSocket)
- **Features**: 
  - Automatic reconnection
  - Event-driven architecture
  - Connection health monitoring

#### Cache Services
- **StockBarCacheService**: SQLite-based historical bar caching
- **IndexCacheService**: Index data caching with compression
- **RedisWarmingService**: Pre-market cache warming for fast execution
- **CacheMetricsService**: Performance monitoring and optimization

### 3. Enhanced Services (Optional Features)

#### MarketRegimeService
- **Purpose**: Market condition analysis and regime detection
- **Analysis**: SPY 100-day analysis using SMA slope, ATR volatility, return-to-drawdown ratio
- **Regimes**: TrendingUp, TrendingDown, Sideways, Volatile
- **Integration**: Skip trades during unfavorable regimes
- **Caching**: Redis cache with 24h TTL for performance

#### DynamicUniverseProvider
- **Purpose**: Dynamic symbol universe generation and screening
- **Features**:
  - Redis caching with universe:today key
  - Filtering by price >$10, volume >1M shares, volatility >2% daily stddev
  - Integration with SignalGenerator for dynamic symbol selection
  - Replaces static symbol lists with real-time screening
- **Performance**: Cached results to minimize API calls

#### VolatilityManager & OptionsStrategyManager
- **Purpose**: Advanced volatility analysis and options overlay strategies
- **Features**:
  - VIX regime detection and volatility spike monitoring
  - Options Greeks calculation and analysis
  - Implied volatility surface analysis
  - Options overlay strategies for hedging
- **Integration**: Enhanced risk management and portfolio protection

#### Advanced Intelligence Services
- **RealTimeTrailingStopManager**: Live price monitoring with automatic stop adjustments
- **LiveSignalIntelligence**: Real-time signal analysis and market monitoring
- **TradingMetricsService**: Comprehensive performance tracking and analytics
- **SystemHealthService**: System monitoring and health checks

## Data Flow

### Trading Cycle Flow
```
1. MarketSessionGuard → Validate trading allowed (weekdays)
2. TradingSafetyGuard → Safety validation checks
3. StopManager → Update trailing stops
4. PortfolioGate → Check SPY SMA200 condition
5. SignalGenerator → Generate trading signals
6. RiskManager → Calculate position sizes
7. TradeExecutor → Execute trades with stop-losses
```

### Data Integration Flow
```
1. MarketDataService → Fetch bars from Alpaca/Polygon
2. CacheService → Check SQLite cache for existing data
3. API Calls → Fetch missing data with rate limiting
4. Timestamp Normalization → Convert to UTC
5. Cache Storage → Store new data for future use
6. Return Unified Data → Consistent interface to consumers
```

## Technology Stack

### Core Framework
- **.NET 8**: Modern C# with nullable reference types
- **Microsoft.Extensions.Hosting**: Dependency injection and configuration
- **Microsoft.Extensions.Http**: HTTP client factory with Polly retry policies

### Market Data APIs
- **Alpaca.Markets SDK 7.2.0**: Trading and market data
- **Polygon.io REST API**: Index data and options
- **WebSocket Clients**: Real-time streaming data

### Data Storage
- **SQLite + Entity Framework Core**: Local data caching
- **Redis**: High-performance caching and session state
- **System.Text.Json**: Serialization and data exchange

### Technical Analysis
- **Skender.Stock.Indicators 2.6.1**: SMA, ATR, and other indicators
- **Custom Extensions**: Specialized calculations and filters

### Testing Framework
- **xUnit 2.6.1**: Unit and integration testing
- **FluentAssertions 6.12.0**: Expressive test assertions
- **Moq 4.20.69**: Mocking framework for dependencies

### Logging and Monitoring
- **Serilog**: Structured logging with console and file sinks
- **Custom Metrics**: Performance monitoring and cache statistics

## Service Registration Architecture

### Core Service Registration (Simple Mode)
```csharp
// Core trading stack (simple SMA trend following)
services.AddScoped<ISignalGenerator, SignalGenerator>();
services.AddScoped<IRiskManager, RiskManager>();
services.AddScoped<IPortfolioGate, PortfolioGate>();
services.AddScoped<IStopManager, StopManager>();
services.AddScoped<ITradeExecutor, TradeExecutor>();
services.AddScoped<ITradingService, TradingService>();

// Data and infrastructure services
services.AddSingleton<IMarketDataService, MarketDataService>();
services.AddSingleton<IAlpacaClientFactory, AlpacaClientFactory>();
services.AddSingleton<IPolygonClientFactory, PolygonClientFactory>();
```

### Enhanced Service Registration (Advanced Mode)
```csharp
// Enhanced trading services with advanced features
services.AddScoped<ITradingService, EnhancedTradingService>();
services.AddScoped<ISignalGenerator, EnhancedSignalGenerator>();

// Real-time intelligence services
services.AddScoped<IRealTimeMarketMonitor, RealTimeMarketMonitor>();
services.AddScoped<ILiveSignalIntelligence, LiveSignalIntelligence>();
services.AddScoped<ITrailingStopManager, RealTimeTrailingStopManager>();

// Performance and metrics services
services.AddSingleton<ITradingMetricsService, TradingMetricsService>();
services.AddSingleton<ISystemHealthService, SystemHealthService>();
```

### Service Scoping Strategy
- **Singleton**: Client factories, market data services, time providers, metrics services
- **Scoped**: Trading services, signal generators, risk managers (per trading cycle)
- **Transient**: Temporary calculation services and utilities

## Configuration Management

### Environment Variables (.env)
```
# Alpaca Trading API (Required)
APCA_API_KEY_ID=your_alpaca_key
APCA_API_SECRET_KEY=your_alpaca_secret
APCA_API_ENV=paper  # or live

# Polygon Market Data (Optional, for index data)
POLY_API_KEY=your_polygon_key

# Redis Cache (Optional, for performance optimization)
REDIS_URL=localhost:6379
REDIS_DATABASE=0
REDIS_PASSWORD=your_password

# Discord Notifications (Optional)
DISCORD_BOT_TOKEN=your_bot_token
DISCORD_CHANNEL_ID=your_channel_id

# Trading Configuration
TRADING_UNIVERSE_SIZE=10  # Number of symbols to trade
RISK_PER_TRADE_BPS=10     # Risk per trade in basis points
MAX_RISK_DOLLARS=1000     # Maximum risk per trade
```

### Configuration Validation
- **Required Settings**: Alpaca API credentials validation
- **Optional Settings**: Graceful degradation when optional services unavailable
- **Environment Detection**: Automatic paper/live mode detection
- **Startup Validation**: Configuration validation during application startup

## Security Considerations

### API Key Management
- Environment variable storage
- No hardcoded credentials
- Separate paper/live configurations

### Rate Limiting
- Alpaca: 200 requests/minute with exponential backoff
- Polygon: 5 requests/second with retry policies
- Circuit breaker patterns for API failures

### Data Validation
- Input sanitization for all external data
- Type safety with nullable reference types
- Comprehensive error handling and logging

## Performance Optimizations

### Caching Strategy
- **SQLite Cache**: 1-year historical bar retention
- **Redis Cache**: Live trading state with 24h TTL
- **Compression**: Data compression for storage efficiency
- **Bulk Operations**: Batch processing for database operations

### Connection Management
- **HTTP Client Factory**: Reusable HTTP connections
- **WebSocket Pooling**: Efficient real-time connections
- **Connection Health**: Automatic reconnection and monitoring

### Memory Management
- **Streaming Processing**: Large datasets processed in chunks
- **Disposal Patterns**: Proper resource cleanup
- **Garbage Collection**: Optimized object lifecycle management

## Monitoring and Observability

### Logging Strategy
- **Structured Logging**: JSON-formatted logs with Serilog
- **Log Levels**: Debug, Information, Warning, Error, Critical
- **Rolling Files**: Daily log files with 30-day retention
- **Performance Metrics**: Execution times and API call statistics

### Health Monitoring
- **API Health Checks**: Alpaca and Polygon connectivity
- **Cache Performance**: Hit rates and response times
- **Trading Metrics**: Signal generation and execution statistics
- **Error Tracking**: Exception logging and alerting

## Complete Service Catalog

### Core Trading Services (Always Active)
| Service | Interface | Implementation | Purpose |
|---------|-----------|----------------|---------|
| TradingService | `ITradingService` | `TradingService` | Main trading cycle orchestrator |
| SignalGenerator | `ISignalGenerator` | `SignalGenerator` | SMA momentum signal generation |
| RiskManager | `IRiskManager` | `RiskManager` | Position sizing and risk control |
| TradeExecutor | `ITradeExecutor` | `TradeExecutor` | Order execution and management |
| PortfolioGate | `IPortfolioGate` | `PortfolioGate` | SPY SMA200 market condition gate |
| StopManager | `IStopManager` | `StopManager` | Trailing stop-loss management |

### Data Infrastructure Services (Always Active)
| Service | Interface | Implementation | Purpose |
|---------|-----------|----------------|---------|
| MarketDataService | `IMarketDataService` | `MarketDataService` | Unified market data interface |
| AlpacaClientFactory | `IAlpacaClientFactory` | `AlpacaClientFactory` | Alpaca API client management |
| PolygonClientFactory | `IPolygonClientFactory` | `PolygonClientFactory` | Polygon API client management |
| StockBarCacheService | `IStockBarCacheService` | `StockBarCacheService` | SQLite historical data caching |
| MarketSessionGuard | `IMarketSessionGuard` | `MarketSessionGuard` | Trading session validation |

### Enhanced Services (Optional)
| Service | Interface | Implementation | Purpose |
|---------|-----------|----------------|---------|
| MarketRegimeService | `IMarketRegimeService` | `MarketRegimeService` | Market condition analysis |
| DynamicUniverseProvider | `IDynamicUniverseProvider` | `DynamicUniverseProvider` | Dynamic symbol screening |
| VolatilityManager | `IVolatilityManager` | `VolatilityManager` | VIX and volatility analysis |
| OptionsStrategyManager | `IOptionsStrategyManager` | `OptionsStrategyManager` | Options overlay strategies |
| DiscordNotificationService | `IDiscordNotificationService` | `DiscordNotificationService` | Trade notifications |

### Advanced Intelligence Services (Complex)
| Service | Interface | Implementation | Purpose |
|---------|-----------|----------------|---------|
| RealTimeTrailingStopManager | `ITrailingStopManager` | `RealTimeTrailingStopManager` | Live stop-loss adjustments |
| LiveSignalIntelligence | `ILiveSignalIntelligence` | `LiveSignalIntelligence` | Real-time signal analysis |
| TradingMetricsService | `ITradingMetricsService` | `TradingMetricsService` | Performance tracking |
| SystemHealthService | `ISystemHealthService` | `SystemHealthService` | System monitoring |
| StreamingDataService | `IStreamingDataService` | `StreamingDataService` | Real-time data streams |

### Cache and Performance Services
| Service | Interface | Implementation | Purpose |
|---------|-----------|----------------|---------|
| RedisWarmingService | `IRedisWarmingService` | `RedisWarmingService` | Pre-market cache warming |
| CacheManagementService | `ICacheManagementService` | `CacheManagementService` | Cache optimization |
| IndexCacheService | `IIndexCacheService` | `IndexCacheService` | Index data caching |
| LiveStateStore | `ILiveStateStore` | `LiveStateStore` | Redis live state management |

## Implementation Status

### ✅ Fully Implemented and Tested
- All core trading services with comprehensive unit tests
- Market data integration with Alpaca and Polygon
- SQLite caching with Entity Framework Core
- Risk management and position sizing
- Basic Discord notifications

### ⚠️ Advanced Features (Implemented but Complex)
- Real-time streaming data services
- Market regime detection and analysis
- Dynamic universe selection with Redis caching
- Advanced trailing stop management
- Performance metrics and system health monitoring

### 🔄 Continuous Enhancement Areas
- Options strategy implementation
- Advanced volatility analysis
- Machine learning signal enhancement
- Multi-timeframe analysis
- Portfolio optimization algorithms

This architecture provides a robust, scalable, and maintainable foundation for algorithmic trading with comprehensive risk management, monitoring capabilities, and extensibility for advanced features.
