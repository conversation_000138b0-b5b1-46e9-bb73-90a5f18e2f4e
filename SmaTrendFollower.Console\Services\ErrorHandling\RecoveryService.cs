using Microsoft.Extensions.Logging;
using System.Collections.Concurrent;
using System.Diagnostics;

namespace SmaTrendFollower.Services.ErrorHandling;

/// <summary>
/// Recovery service implementation with graceful degradation and automatic recovery
/// </summary>
public sealed class RecoveryService : IRecoveryService, IDisposable
{
    private readonly ILogger<RecoveryService> _logger;
    private readonly IErrorHandler _errorHandler;
    private readonly ConcurrentDictionary<string, object> _fallbackStrategies = new();
    private readonly ConcurrentDictionary<string, ServiceDegradationContext> _degradationContexts = new();
    private readonly Timer _recoveryTimer;
    private readonly SemaphoreSlim _recoveryLock = new(1, 1);
    private volatile bool _disposed;

    public event EventHandler<DegradationEventArgs>? DegradationStatusChanged;

    public RecoveryService(ILogger<RecoveryService> logger, IErrorHandler errorHandler)
    {
        _logger = logger;
        _errorHandler = errorHandler;
        
        // Start recovery timer (runs every 2 minutes)
        _recoveryTimer = new Timer(PerformAutomaticRecovery, null, 
            TimeSpan.FromMinutes(2), TimeSpan.FromMinutes(2));
    }

    public async Task<T> ExecuteWithRecoveryAsync<T>(
        Func<Task<T>> operation,
        RecoveryOptions recoveryOptions,
        CancellationToken cancellationToken = default)
    {
        var context = new ErrorContext
        {
            OperationName = recoveryOptions.OperationType,
            ServiceName = recoveryOptions.ServiceName ?? "Unknown"
        };

        var attemptCount = 0;
        Exception? lastException = null;

        while (attemptCount <= recoveryOptions.MaxRecoveryAttempts)
        {
            try
            {
                // Check if service is degraded and should be skipped
                if (!string.IsNullOrEmpty(recoveryOptions.ServiceName) && 
                    IsServiceSeverelyDegraded(recoveryOptions.ServiceName))
                {
                    _logger.LogWarning("Service {ServiceName} is severely degraded, attempting fallback", 
                        recoveryOptions.ServiceName);
                    return await ExecuteFallbackAsync<T>(recoveryOptions, lastException ?? new InvalidOperationException("Service degraded"));
                }

                // Execute the operation with timeout
                using var timeoutCts = CancellationTokenSource.CreateLinkedTokenSource(cancellationToken);
                timeoutCts.CancelAfter(recoveryOptions.RecoveryTimeout);

                var result = await operation();
                
                // If successful and service was degraded, attempt recovery
                if (!string.IsNullOrEmpty(recoveryOptions.ServiceName) && 
                    IsServiceDegraded(recoveryOptions.ServiceName))
                {
                    _ = Task.Run(() => AttemptRecoveryAsync(recoveryOptions.ServiceName, CancellationToken.None));
                }

                return result;
            }
            catch (Exception ex) when (attemptCount < recoveryOptions.MaxRecoveryAttempts)
            {
                lastException = ex;
                attemptCount++;

                // Check if we should attempt recovery
                if (recoveryOptions.ShouldAttemptRecovery?.Invoke(ex) == false)
                {
                    break;
                }

                _logger.LogWarning(ex, "Operation {OperationType} failed (attempt {AttemptCount}/{MaxAttempts}), attempting recovery",
                    recoveryOptions.OperationType, attemptCount, recoveryOptions.MaxRecoveryAttempts);

                // Execute custom recovery action if provided
                if (recoveryOptions.CustomRecoveryAction != null)
                {
                    try
                    {
                        await recoveryOptions.CustomRecoveryAction(ex);
                    }
                    catch (Exception recoveryEx)
                    {
                        _logger.LogWarning(recoveryEx, "Custom recovery action failed for {OperationType}", 
                            recoveryOptions.OperationType);
                    }
                }

                // Trigger degradation if enabled
                if (recoveryOptions.EnableDegradation && !string.IsNullOrEmpty(recoveryOptions.ServiceName))
                {
                    await TriggerDegradationAsync(recoveryOptions.ServiceName, ex.Message, cancellationToken);
                }

                // Wait before retry
                if (recoveryOptions.RecoveryDelay > TimeSpan.Zero)
                {
                    await Task.Delay(recoveryOptions.RecoveryDelay, cancellationToken);
                }
            }
            catch (Exception ex)
            {
                lastException = ex;
                break;
            }
        }

        // All recovery attempts failed, try fallback
        if (recoveryOptions.EnableFallback)
        {
            try
            {
                return await ExecuteFallbackAsync<T>(recoveryOptions, lastException!);
            }
            catch (Exception fallbackEx)
            {
                _logger.LogError(fallbackEx, "Fallback strategy failed for {OperationType}", recoveryOptions.OperationType);
            }
        }

        // Final degradation if all else fails
        if (recoveryOptions.EnableDegradation && !string.IsNullOrEmpty(recoveryOptions.ServiceName))
        {
            await TriggerDegradationAsync(recoveryOptions.ServiceName, "All recovery attempts failed", cancellationToken);
        }

        throw lastException ?? new InvalidOperationException("Operation failed without exception details");
    }

    public void RegisterFallback<T>(string operationType, Func<Exception, Task<T>> fallbackStrategy)
    {
        _fallbackStrategies[operationType] = fallbackStrategy;
        _logger.LogInformation("Registered fallback strategy for operation type {OperationType}", operationType);
    }

    public void RegisterDegradationStrategy(string serviceName, Func<Exception, Task> degradationStrategy)
    {
        var context = GetOrCreateDegradationContext(serviceName);
        lock (context.Lock)
        {
            context.DegradationStrategy = degradationStrategy;
        }
        _logger.LogInformation("Registered degradation strategy for service {ServiceName}", serviceName);
    }

    public async Task TriggerDegradationAsync(string serviceName, string reason, CancellationToken cancellationToken = default)
    {
        var context = GetOrCreateDegradationContext(serviceName);
        bool statusChanged = false;
        DegradationLevel newLevel;

        lock (context.Lock)
        {
            if (!context.IsDegraded)
            {
                context.IsDegraded = true;
                context.DegradedSince = DateTime.UtcNow;
                context.Reason = reason;
                context.Level = DegradationLevel.Minor;
                statusChanged = true;
                newLevel = context.Level;
            }
            else
            {
                // Escalate degradation level
                var previousLevel = context.Level;
                context.Level = EscalateDegradationLevel(context.Level);
                context.Reason = reason;
                
                if (context.Level != previousLevel)
                {
                    statusChanged = true;
                    newLevel = context.Level;
                }
                else
                {
                    newLevel = context.Level;
                }
            }
        }

        if (statusChanged)
        {
            _logger.LogWarning("Service {ServiceName} degraded to level {Level}: {Reason}", 
                serviceName, newLevel, reason);

            // Execute degradation strategy if available
            if (context.DegradationStrategy != null)
            {
                try
                {
                    await context.DegradationStrategy(new InvalidOperationException(reason));
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "Degradation strategy failed for service {ServiceName}", serviceName);
                }
            }

            OnDegradationStatusChanged(new DegradationEventArgs
            {
                ServiceName = serviceName,
                IsDegraded = true,
                Level = newLevel,
                Reason = reason
            });
        }
    }

    public async Task<bool> AttemptRecoveryAsync(string serviceName, CancellationToken cancellationToken = default)
    {
        if (!_degradationContexts.TryGetValue(serviceName, out var context))
        {
            return true; // Service not degraded
        }

        await _recoveryLock.WaitAsync(cancellationToken);
        try
        {
            lock (context.Lock)
            {
                if (!context.IsDegraded)
                {
                    return true; // Already recovered
                }

                context.RecoveryAttempts++;
                context.LastRecoveryAttempt = DateTime.UtcNow;
            }

            _logger.LogInformation("Attempting recovery for service {ServiceName} (attempt {AttemptCount})", 
                serviceName, context.RecoveryAttempts);

            var stopwatch = Stopwatch.StartNew();
            try
            {
                // Perform recovery check - this would be service-specific
                // For now, we'll use a simple approach
                var recoveryResult = await PerformRecoveryCheckAsync(serviceName, cancellationToken);
                stopwatch.Stop();

                if (recoveryResult.IsSuccessful)
                {
                    lock (context.Lock)
                    {
                        context.IsDegraded = false;
                        context.Level = DegradationLevel.Normal;
                        context.Reason = null;
                        context.LastError = null;
                    }

                    _logger.LogInformation("Service {ServiceName} recovered successfully after {Duration}ms", 
                        serviceName, stopwatch.ElapsedMilliseconds);

                    OnDegradationStatusChanged(new DegradationEventArgs
                    {
                        ServiceName = serviceName,
                        IsDegraded = false,
                        Level = DegradationLevel.Normal,
                        Reason = "Recovery successful"
                    });

                    return true;
                }
                else
                {
                    _logger.LogWarning("Recovery attempt failed for service {ServiceName}: {Message}", 
                        serviceName, recoveryResult.Message);
                    return false;
                }
            }
            catch (Exception ex)
            {
                stopwatch.Stop();
                lock (context.Lock)
                {
                    context.LastError = ex;
                }

                _logger.LogError(ex, "Recovery attempt failed for service {ServiceName}", serviceName);
                return false;
            }
        }
        finally
        {
            _recoveryLock.Release();
        }
    }

    public Dictionary<string, DegradationStatus> GetDegradationStatus()
    {
        var status = new Dictionary<string, DegradationStatus>();

        foreach (var kvp in _degradationContexts)
        {
            var context = kvp.Value;
            lock (context.Lock)
            {
                status[kvp.Key] = new DegradationStatus
                {
                    ServiceName = context.ServiceName,
                    IsDegraded = context.IsDegraded,
                    Reason = context.Reason,
                    DegradedSince = context.DegradedSince,
                    RecoveryAttempts = context.RecoveryAttempts,
                    LastRecoveryAttempt = context.LastRecoveryAttempt,
                    LastError = context.LastError,
                    Level = context.Level
                };
            }
        }

        return status;
    }

    private async Task<T> ExecuteFallbackAsync<T>(RecoveryOptions options, Exception originalException)
    {
        if (_fallbackStrategies.TryGetValue(options.OperationType, out var strategy))
        {
            var fallbackFunc = (Func<Exception, Task<T>>)strategy;
            _logger.LogInformation("Executing fallback strategy for {OperationType}", options.OperationType);
            return await fallbackFunc(originalException);
        }

        throw new InvalidOperationException($"No fallback strategy registered for operation type {options.OperationType}", originalException);
    }

    private ServiceDegradationContext GetOrCreateDegradationContext(string serviceName)
    {
        return _degradationContexts.GetOrAdd(serviceName, _ => new ServiceDegradationContext
        {
            ServiceName = serviceName
        });
    }

    private bool IsServiceDegraded(string serviceName)
    {
        if (_degradationContexts.TryGetValue(serviceName, out var context))
        {
            lock (context.Lock)
            {
                return context.IsDegraded;
            }
        }
        return false;
    }

    private bool IsServiceSeverelyDegraded(string serviceName)
    {
        if (_degradationContexts.TryGetValue(serviceName, out var context))
        {
            lock (context.Lock)
            {
                return context.IsDegraded && context.Level >= DegradationLevel.Severe;
            }
        }
        return false;
    }

    private static DegradationLevel EscalateDegradationLevel(DegradationLevel currentLevel)
    {
        return currentLevel switch
        {
            DegradationLevel.Normal => DegradationLevel.Minor,
            DegradationLevel.Minor => DegradationLevel.Major,
            DegradationLevel.Major => DegradationLevel.Severe,
            DegradationLevel.Severe => DegradationLevel.Severe,
            _ => DegradationLevel.Minor
        };
    }

    private async Task<RecoveryResult> PerformRecoveryCheckAsync(string serviceName, CancellationToken cancellationToken)
    {
        // This is a placeholder - in a real implementation, this would perform
        // service-specific recovery checks (e.g., ping API, check database connection, etc.)
        await Task.Delay(100, cancellationToken);
        
        // For now, assume recovery is successful after some time
        var context = _degradationContexts[serviceName];
        lock (context.Lock)
        {
            var degradedDuration = DateTime.UtcNow - context.DegradedSince;
            if (degradedDuration > TimeSpan.FromMinutes(5)) // Auto-recover after 5 minutes
            {
                return RecoveryResult.Success("Auto-recovery timeout reached");
            }
        }

        return RecoveryResult.Failure("Service still degraded");
    }

    private async void PerformAutomaticRecovery(object? state)
    {
        if (_disposed)
            return;

        try
        {
            var degradedServices = _degradationContexts.Values
                .Where(context =>
                {
                    lock (context.Lock)
                    {
                        return context.IsDegraded && 
                               (context.LastRecoveryAttempt == null || 
                                DateTime.UtcNow - context.LastRecoveryAttempt.Value > TimeSpan.FromMinutes(5));
                    }
                })
                .ToList();

            foreach (var context in degradedServices)
            {
                try
                {
                    await AttemptRecoveryAsync(context.ServiceName, CancellationToken.None);
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "Automatic recovery failed for service {ServiceName}", context.ServiceName);
                }
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error during automatic recovery process");
        }
    }

    private void OnDegradationStatusChanged(DegradationEventArgs args)
    {
        try
        {
            DegradationStatusChanged?.Invoke(this, args);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error firing degradation status changed event for {ServiceName}", args.ServiceName);
        }
    }

    public void Dispose()
    {
        if (_disposed)
            return;

        _disposed = true;
        _recoveryTimer?.Dispose();
        _recoveryLock?.Dispose();
    }
}
