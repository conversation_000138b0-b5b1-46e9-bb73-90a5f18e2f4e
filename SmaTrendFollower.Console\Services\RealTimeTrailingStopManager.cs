using Alpaca.Markets;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;
using System.Collections.Concurrent;

namespace SmaTrendFollower.Services;

/// <summary>
/// Real-time trailing stop management with live price monitoring.
/// Automatically adjusts stop-loss orders as prices move favorably.
/// </summary>
public sealed class RealTimeTrailingStopManager : BackgroundService, ITrailingStopManager
{
    private readonly IMarketDataService _marketDataService;
    private readonly ILiveStateStore _liveStateStore;
    private readonly ILogger<RealTimeTrailingStopManager> _logger;
    private readonly TrailingStopConfig _config;
    private readonly ConcurrentDictionary<string, TrailingStopState> _activeStops = new();
    private readonly SemaphoreSlim _updateSemaphore = new(1, 1);

    public RealTimeTrailingStopManager(
        IMarketDataService marketDataService,
        ILiveStateStore liveStateStore,
        ILogger<RealTimeTrailingStopManager> logger,
        TrailingStopConfig? config = null)
    {
        _marketDataService = marketDataService;
        _liveStateStore = liveStateStore;
        _logger = logger;
        _config = config ?? new TrailingStopConfig();
    }

    /// <summary>
    /// Adds a new trailing stop for monitoring
    /// </summary>
    public async Task AddTrailingStopAsync(string symbol, decimal entryPrice, decimal initialStopPrice, 
        decimal atrValue, int quantity)
    {
        try
        {
            var stopState = new TrailingStopState(
                symbol,
                entryPrice,
                initialStopPrice,
                initialStopPrice, // Current stop starts at initial
                entryPrice,       // Highest price starts at entry
                atrValue,
                quantity,
                DateTime.UtcNow,
                DateTime.UtcNow
            );

            _activeStops[symbol] = stopState;
            
            // Persist to Redis
            await _liveStateStore.SetTrailingStopAsync(symbol, initialStopPrice);
            await _liveStateStore.SetPositionStateAsync(symbol, new PositionState(
                symbol, entryPrice, entryPrice, initialStopPrice, quantity, DateTime.UtcNow, DateTime.UtcNow));

            _logger.LogInformation("Added trailing stop for {Symbol}: Entry={Entry:F2}, Stop={Stop:F2}, ATR={ATR:F2}",
                symbol, entryPrice, initialStopPrice, atrValue);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to add trailing stop for {Symbol}", symbol);
            throw;
        }
    }

    /// <summary>
    /// Updates trailing stop based on current price
    /// </summary>
    public async Task<TrailingStopUpdate> UpdateTrailingStopAsync(string symbol, decimal currentPrice)
    {
        if (!_activeStops.TryGetValue(symbol, out var stopState))
        {
            return new TrailingStopUpdate(symbol, false, 0, 0, "No active stop found");
        }

        await _updateSemaphore.WaitAsync();
        try
        {
            var update = CalculateStopUpdate(stopState, currentPrice);
            
            if (update.ShouldUpdate)
            {
                // Update the stop state
                var newStopState = stopState with
                {
                    CurrentStopPrice = update.NewStopPrice,
                    HighestPrice = Math.Max(stopState.HighestPrice, currentPrice),
                    LastUpdated = DateTime.UtcNow
                };

                _activeStops[symbol] = newStopState;

                // Persist to Redis
                await _liveStateStore.SetTrailingStopAsync(symbol, update.NewStopPrice);
                await _liveStateStore.SetPositionStateAsync(symbol, new PositionState(
                    symbol, stopState.EntryPrice, currentPrice, update.NewStopPrice, 
                    stopState.Quantity, stopState.CreatedAt, DateTime.UtcNow));

                _logger.LogInformation("Updated trailing stop for {Symbol}: Price={Price:F2}, Stop={OldStop:F2} -> {NewStop:F2}",
                    symbol, currentPrice, stopState.CurrentStopPrice, update.NewStopPrice);
            }

            return update;
        }
        finally
        {
            _updateSemaphore.Release();
        }
    }

    /// <summary>
    /// Removes trailing stop (position closed)
    /// </summary>
    public async Task RemoveTrailingStopAsync(string symbol)
    {
        try
        {
            _activeStops.TryRemove(symbol, out _);
            await _liveStateStore.RemoveTrailingStopAsync(symbol);
            await _liveStateStore.RemovePositionStateAsync(symbol);

            _logger.LogInformation("Removed trailing stop for {Symbol}", symbol);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to remove trailing stop for {Symbol}", symbol);
        }
    }

    /// <summary>
    /// Gets current trailing stop information
    /// </summary>
    public async Task<TrailingStopInfo?> GetTrailingStopAsync(string symbol)
    {
        if (_activeStops.TryGetValue(symbol, out var stopState))
        {
            var unrealizedPnL = (stopState.HighestPrice - stopState.EntryPrice) * stopState.Quantity;
            var riskAmount = (stopState.EntryPrice - stopState.CurrentStopPrice) * stopState.Quantity;
            
            return new TrailingStopInfo(
                stopState.Symbol,
                stopState.EntryPrice,
                stopState.CurrentStopPrice,
                stopState.HighestPrice,
                stopState.Quantity,
                unrealizedPnL,
                riskAmount,
                stopState.CreatedAt,
                stopState.LastUpdated
            );
        }

        // Try to restore from Redis
        var redisStop = await _liveStateStore.GetTrailingStopAsync(symbol);
        var redisPosition = await _liveStateStore.GetPositionStateAsync(symbol);
        
        if (redisStop.HasValue && redisPosition != null)
        {
            // Restore to active stops
            var restoredState = new TrailingStopState(
                symbol,
                redisPosition.EntryPrice,
                redisStop.Value,
                redisStop.Value,
                redisPosition.CurrentPrice,
                0, // ATR not stored in Redis
                redisPosition.Quantity,
                redisPosition.EntryTime,
                redisPosition.LastUpdated
            );

            _activeStops[symbol] = restoredState;

            var unrealizedPnL = (restoredState.HighestPrice - restoredState.EntryPrice) * restoredState.Quantity;
            var riskAmount = (restoredState.EntryPrice - restoredState.CurrentStopPrice) * restoredState.Quantity;

            return new TrailingStopInfo(
                symbol,
                restoredState.EntryPrice,
                restoredState.CurrentStopPrice,
                restoredState.HighestPrice,
                restoredState.Quantity,
                unrealizedPnL,
                riskAmount,
                restoredState.CreatedAt,
                restoredState.LastUpdated
            );
        }

        return null;
    }

    /// <summary>
    /// Gets all active trailing stops
    /// </summary>
    public async Task<List<TrailingStopInfo>> GetAllTrailingStopsAsync()
    {
        var results = new List<TrailingStopInfo>();

        foreach (var symbol in _activeStops.Keys)
        {
            var info = await GetTrailingStopAsync(symbol);
            if (info != null)
            {
                results.Add(info);
            }
        }

        return results;
    }

    /// <summary>
    /// Background service execution - monitors and updates stops
    /// </summary>
    protected override async Task ExecuteAsync(CancellationToken stoppingToken)
    {
        _logger.LogInformation("RealTimeTrailingStopManager started");

        while (!stoppingToken.IsCancellationRequested)
        {
            try
            {
                await MonitorAndUpdateStopsAsync(stoppingToken);
                await Task.Delay(_config.UpdateInterval, stoppingToken);
            }
            catch (OperationCanceledException)
            {
                break;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error in trailing stop monitoring");
                await Task.Delay(TimeSpan.FromMinutes(1), stoppingToken);
            }
        }

        _logger.LogInformation("RealTimeTrailingStopManager stopped");
    }

    /// <summary>
    /// Monitors all active stops and updates them based on current prices
    /// </summary>
    private async Task MonitorAndUpdateStopsAsync(CancellationToken cancellationToken)
    {
        if (!_activeStops.Any())
            return;

        var symbols = _activeStops.Keys.ToList();
        _logger.LogDebug("Monitoring {Count} trailing stops", symbols.Count);

        // Get current prices for all symbols (could be optimized with streaming data)
        var updateTasks = symbols.Select(async symbol =>
        {
            try
            {
                // In a real implementation, this would come from streaming data
                // For now, we'll simulate with the last known price
                if (_activeStops.TryGetValue(symbol, out var stopState))
                {
                    // Use the highest price as current price for simulation
                    // In reality, this would be the live streaming price
                    await UpdateTrailingStopAsync(symbol, stopState.HighestPrice);
                }
            }
            catch (Exception ex)
            {
                _logger.LogWarning(ex, "Failed to update trailing stop for {Symbol}", symbol);
            }
        });

        await Task.WhenAll(updateTasks);
    }

    /// <summary>
    /// Calculates if and how to update the trailing stop
    /// </summary>
    private TrailingStopUpdate CalculateStopUpdate(TrailingStopState stopState, decimal currentPrice)
    {
        // Check if price moved higher
        if (currentPrice <= stopState.HighestPrice)
        {
            return new TrailingStopUpdate(stopState.Symbol, false, stopState.CurrentStopPrice, 
                currentPrice, "Price not higher than previous high");
        }

        // Calculate new stop price based on trailing method
        var newStopPrice = CalculateNewStopPrice(stopState, currentPrice);

        // Only update if new stop is higher than current stop
        if (newStopPrice <= stopState.CurrentStopPrice)
        {
            return new TrailingStopUpdate(stopState.Symbol, false, stopState.CurrentStopPrice, 
                currentPrice, "New stop not higher than current stop");
        }

        // Ensure minimum movement threshold
        var stopMovement = newStopPrice - stopState.CurrentStopPrice;
        var minMovement = currentPrice * _config.MinimumStopMovementPercent;
        
        if (stopMovement < minMovement)
        {
            return new TrailingStopUpdate(stopState.Symbol, false, stopState.CurrentStopPrice, 
                currentPrice, $"Stop movement {stopMovement:F2} below minimum {minMovement:F2}");
        }

        return new TrailingStopUpdate(stopState.Symbol, true, newStopPrice, currentPrice, "Stop updated");
    }

    /// <summary>
    /// Calculates new stop price based on configured method
    /// </summary>
    private decimal CalculateNewStopPrice(TrailingStopState stopState, decimal currentPrice)
    {
        return _config.TrailingMethod switch
        {
            TrailingMethod.FixedPercent => currentPrice * (1 - _config.TrailingPercent),
            TrailingMethod.AtrBased => currentPrice - (stopState.AtrValue * _config.AtrMultiplier),
            TrailingMethod.Hybrid => Math.Max(
                currentPrice * (1 - _config.TrailingPercent),
                currentPrice - (stopState.AtrValue * _config.AtrMultiplier)
            ),
            _ => currentPrice * (1 - _config.TrailingPercent)
        };
    }

    public override void Dispose()
    {
        _updateSemaphore?.Dispose();
        base.Dispose();
    }
}

/// <summary>
/// Interface for trailing stop management
/// </summary>
public interface ITrailingStopManager
{
    Task AddTrailingStopAsync(string symbol, decimal entryPrice, decimal initialStopPrice, decimal atrValue, int quantity);
    Task<TrailingStopUpdate> UpdateTrailingStopAsync(string symbol, decimal currentPrice);
    Task RemoveTrailingStopAsync(string symbol);
    Task<TrailingStopInfo?> GetTrailingStopAsync(string symbol);
    Task<List<TrailingStopInfo>> GetAllTrailingStopsAsync();
}

/// <summary>
/// Configuration for trailing stop behavior
/// </summary>
public record TrailingStopConfig(
    TrailingMethod TrailingMethod = TrailingMethod.Hybrid,
    decimal TrailingPercent = 0.05m,           // 5% trailing percentage
    decimal AtrMultiplier = 1.5m,              // 1.5x ATR for ATR-based trailing
    decimal MinimumStopMovementPercent = 0.005m, // 0.5% minimum movement to update
    TimeSpan UpdateInterval = default          // How often to check for updates
)
{
    public TrailingStopConfig() : this(TrailingMethod.Hybrid, 0.05m, 1.5m, 0.005m, TimeSpan.FromSeconds(30)) { }
}

/// <summary>
/// Trailing stop methods
/// </summary>
public enum TrailingMethod
{
    FixedPercent,  // Fixed percentage below current price
    AtrBased,      // ATR-based distance
    Hybrid         // Maximum of both methods
}

/// <summary>
/// Internal state of a trailing stop
/// </summary>
internal record TrailingStopState(
    string Symbol,
    decimal EntryPrice,
    decimal InitialStopPrice,
    decimal CurrentStopPrice,
    decimal HighestPrice,
    decimal AtrValue,
    int Quantity,
    DateTime CreatedAt,
    DateTime LastUpdated
);

/// <summary>
/// Result of a trailing stop update operation
/// </summary>
public record TrailingStopUpdate(
    string Symbol,
    bool ShouldUpdate,
    decimal NewStopPrice,
    decimal CurrentPrice,
    string Reason
);

/// <summary>
/// Information about an active trailing stop
/// </summary>
public record TrailingStopInfo(
    string Symbol,
    decimal EntryPrice,
    decimal CurrentStopPrice,
    decimal HighestPrice,
    int Quantity,
    decimal UnrealizedPnL,
    decimal RiskAmount,
    DateTime CreatedAt,
    DateTime LastUpdated
);
