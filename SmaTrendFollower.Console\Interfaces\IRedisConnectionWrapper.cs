using StackExchange.Redis;

namespace SmaTrendFollower.Interfaces;

/// <summary>
/// Wrapper interface for Redis ConnectionMultiplexer to enable mocking in tests
/// </summary>
public interface IRedisConnectionWrapper : IDisposable
{
    /// <summary>
    /// Gets a database instance
    /// </summary>
    IDatabase GetDatabase(int database = -1, object? asyncState = null);
    
    /// <summary>
    /// Gets whether the connection is connected
    /// </summary>
    bool IsConnected { get; }
    
    /// <summary>
    /// Gets the configuration used for this connection
    /// </summary>
    string Configuration { get; }
}

/// <summary>
/// Default implementation that wraps the actual ConnectionMultiplexer
/// </summary>
public class RedisConnectionWrapper : IRedisConnectionWrapper
{
    private readonly ConnectionMultiplexer _connectionMultiplexer;

    public RedisConnectionWrapper(ConnectionMultiplexer connectionMultiplexer)
    {
        _connectionMultiplexer = connectionMultiplexer ?? throw new ArgumentNullException(nameof(connectionMultiplexer));
    }

    public IDatabase GetDatabase(int database = -1, object? asyncState = null)
    {
        return _connectionMultiplexer.GetDatabase(database, asyncState);
    }

    public bool IsConnected => _connectionMultiplexer.IsConnected;

    public string Configuration => _connectionMultiplexer.Configuration;

    public void Dispose()
    {
        _connectionMultiplexer?.Dispose();
    }
}
