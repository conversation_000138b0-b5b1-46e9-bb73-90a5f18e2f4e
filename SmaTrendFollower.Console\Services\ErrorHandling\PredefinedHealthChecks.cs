using Microsoft.Extensions.Logging;
using System.Diagnostics;
using System.Net.NetworkInformation;
using StackExchange.Redis;
using Alpaca.Markets;

namespace SmaTrendFollower.Services.ErrorHandling;

/// <summary>
/// Predefined health checks for common services
/// </summary>
public static class PredefinedHealthChecks
{
    /// <summary>
    /// Create health check for Alpaca API
    /// </summary>
    public static Func<CancellationToken, Task<HealthCheckResult>> CreateAlpacaHealthCheck(
        IAlpacaClientFactory clientFactory,
        ILogger logger)
    {
        return async (cancellationToken) =>
        {
            var stopwatch = Stopwatch.StartNew();
            try
            {
                using var tradingClient = clientFactory.CreateTradingClient();
                var account = await tradingClient.GetAccountAsync();
                
                stopwatch.Stop();
                
                if (account?.AccountId == null)
                {
                    return HealthCheckResult.Unhealthy("Alpaca", "Account data is invalid", responseTime: stopwatch.Elapsed);
                }

                var result = HealthCheckResult.Healthy("Alpaca", "Account accessible", stopwatch.Elapsed)
                    .WithData("AccountId", account.AccountId.ToString())
                    .WithData("Status", account.Status.ToString())
                    .WithData("Equity", account.Equity?.ToString("C") ?? "N/A");

                // Check for account issues based on status
                if (account.Status != AccountStatus.Active)
                {
                    return HealthCheckResult.Degraded("Alpaca", $"Account status is {account.Status}", stopwatch.Elapsed)
                        .WithData("Status", account.Status.ToString())
                        .WithData("AccountId", account.AccountId.ToString());
                }

                return result;
            }
            catch (Exception ex)
            {
                stopwatch.Stop();
                logger.LogWarning(ex, "Alpaca health check failed");
                return HealthCheckResult.Unhealthy("Alpaca", "API connection failed", ex, stopwatch.Elapsed);
            }
        };
    }

    /// <summary>
    /// Create health check for Polygon API
    /// </summary>
    public static Func<CancellationToken, Task<HealthCheckResult>> CreatePolygonHealthCheck(
        IPolygonClientFactory clientFactory,
        ILogger logger)
    {
        return async (cancellationToken) =>
        {
            var stopwatch = Stopwatch.StartNew();
            try
            {
                using var httpClient = clientFactory.CreateClient();
                
                // Simple API status check
                var response = await httpClient.GetAsync("v1/meta/symbols/SPY/company", cancellationToken);
                stopwatch.Stop();

                if (response.IsSuccessStatusCode)
                {
                    return HealthCheckResult.Healthy("Polygon", "API accessible", stopwatch.Elapsed)
                        .WithData("StatusCode", (int)response.StatusCode)
                        .WithData("ResponseTime", stopwatch.Elapsed.TotalMilliseconds);
                }
                else
                {
                    return HealthCheckResult.Degraded("Polygon", $"API returned {response.StatusCode}", stopwatch.Elapsed)
                        .WithData("StatusCode", (int)response.StatusCode);
                }
            }
            catch (Exception ex)
            {
                stopwatch.Stop();
                logger.LogWarning(ex, "Polygon health check failed");
                return HealthCheckResult.Unhealthy("Polygon", "API connection failed", ex, stopwatch.Elapsed);
            }
        };
    }

    /// <summary>
    /// Create health check for Redis
    /// </summary>
    public static Func<CancellationToken, Task<HealthCheckResult>> CreateRedisHealthCheck(
        IConnectionMultiplexer redis,
        ILogger logger)
    {
        return async (cancellationToken) =>
        {
            var stopwatch = Stopwatch.StartNew();
            try
            {
                var database = redis.GetDatabase();
                
                // Perform a simple ping
                var pingTime = await database.PingAsync();
                stopwatch.Stop();

                if (pingTime.TotalMilliseconds > 1000) // Slow response
                {
                    return HealthCheckResult.Degraded("Redis", $"Slow response: {pingTime.TotalMilliseconds:F0}ms", stopwatch.Elapsed)
                        .WithData("PingTime", pingTime.TotalMilliseconds)
                        .WithData("IsConnected", redis.IsConnected);
                }

                return HealthCheckResult.Healthy("Redis", "Connection healthy", stopwatch.Elapsed)
                    .WithData("PingTime", pingTime.TotalMilliseconds)
                    .WithData("IsConnected", redis.IsConnected)
                    .WithData("ConnectedEndpoints", redis.GetEndPoints().Length);
            }
            catch (Exception ex)
            {
                stopwatch.Stop();
                logger.LogWarning(ex, "Redis health check failed");
                return HealthCheckResult.Unhealthy("Redis", "Connection failed", ex, stopwatch.Elapsed);
            }
        };
    }

    /// <summary>
    /// Create health check for Discord notifications
    /// </summary>
    public static Func<CancellationToken, Task<HealthCheckResult>> CreateDiscordHealthCheck(
        HttpClient httpClient,
        string botToken,
        ILogger logger)
    {
        return async (cancellationToken) =>
        {
            var stopwatch = Stopwatch.StartNew();
            try
            {
                // Check Discord API status
                httpClient.DefaultRequestHeaders.Clear();
                httpClient.DefaultRequestHeaders.Add("Authorization", $"Bot {botToken}");
                
                var response = await httpClient.GetAsync("https://discord.com/api/v10/users/@me", cancellationToken);
                stopwatch.Stop();

                if (response.IsSuccessStatusCode)
                {
                    return HealthCheckResult.Healthy("Discord", "Bot token valid", stopwatch.Elapsed)
                        .WithData("StatusCode", (int)response.StatusCode);
                }
                else if (response.StatusCode == System.Net.HttpStatusCode.Unauthorized)
                {
                    return HealthCheckResult.Unhealthy("Discord", "Invalid bot token", responseTime: stopwatch.Elapsed)
                        .WithData("StatusCode", (int)response.StatusCode);
                }
                else
                {
                    return HealthCheckResult.Degraded("Discord", $"API returned {response.StatusCode}", stopwatch.Elapsed)
                        .WithData("StatusCode", (int)response.StatusCode);
                }
            }
            catch (Exception ex)
            {
                stopwatch.Stop();
                logger.LogWarning(ex, "Discord health check failed");
                return HealthCheckResult.Unhealthy("Discord", "API connection failed", ex, stopwatch.Elapsed);
            }
        };
    }

    /// <summary>
    /// Create health check for database connectivity
    /// </summary>
    public static Func<CancellationToken, Task<HealthCheckResult>> CreateDatabaseHealthCheck<TContext>(
        Func<TContext> contextFactory,
        ILogger logger) where TContext : class, IDisposable
    {
        return async (cancellationToken) =>
        {
            var stopwatch = Stopwatch.StartNew();
            try
            {
                using var context = contextFactory();
                
                // Perform a simple database operation
                // This would need to be customized based on the actual DbContext
                // For now, just test that we can create the context
                stopwatch.Stop();

                return HealthCheckResult.Healthy("Database", "Connection successful", stopwatch.Elapsed);
            }
            catch (Exception ex)
            {
                stopwatch.Stop();
                logger.LogWarning(ex, "Database health check failed");
                return HealthCheckResult.Unhealthy("Database", "Connection failed", ex, stopwatch.Elapsed);
            }
        };
    }

    /// <summary>
    /// Create health check for network connectivity
    /// </summary>
    public static Func<CancellationToken, Task<HealthCheckResult>> CreateNetworkHealthCheck(
        string hostName,
        ILogger logger)
    {
        return async (cancellationToken) =>
        {
            var stopwatch = Stopwatch.StartNew();
            try
            {
                using var ping = new Ping();
                var reply = await ping.SendPingAsync(hostName, 5000);
                stopwatch.Stop();

                if (reply.Status == IPStatus.Success)
                {
                    var result = HealthCheckResult.Healthy("Network", $"Ping to {hostName} successful", stopwatch.Elapsed)
                        .WithData("RoundTripTime", reply.RoundtripTime)
                        .WithData("Host", hostName);

                    // Check for slow network
                    if (reply.RoundtripTime > 500)
                    {
                        return HealthCheckResult.Degraded("Network", $"Slow network: {reply.RoundtripTime}ms", stopwatch.Elapsed)
                            .WithData("RoundTripTime", reply.RoundtripTime)
                            .WithData("Host", hostName);
                    }

                    return result;
                }
                else
                {
                    return HealthCheckResult.Unhealthy("Network", $"Ping to {hostName} failed: {reply.Status}", responseTime: stopwatch.Elapsed)
                        .WithData("Status", reply.Status.ToString())
                        .WithData("Host", hostName);
                }
            }
            catch (Exception ex)
            {
                stopwatch.Stop();
                logger.LogWarning(ex, "Network health check failed for {HostName}", hostName);
                return HealthCheckResult.Unhealthy("Network", $"Network check failed for {hostName}", ex, stopwatch.Elapsed);
            }
        };
    }

    /// <summary>
    /// Create health check for system resources
    /// </summary>
    public static Func<CancellationToken, Task<HealthCheckResult>> CreateSystemResourcesHealthCheck(ILogger logger)
    {
        return async (cancellationToken) =>
        {
            var stopwatch = Stopwatch.StartNew();
            try
            {
                var process = Process.GetCurrentProcess();
                var workingSet = process.WorkingSet64;
                var privateMemory = process.PrivateMemorySize64;
                
                // Get CPU usage (simplified)
                var startTime = DateTime.UtcNow;
                var startCpuUsage = process.TotalProcessorTime;
                await Task.Delay(100, cancellationToken);
                var endTime = DateTime.UtcNow;
                var endCpuUsage = process.TotalProcessorTime;
                
                var cpuUsedMs = (endCpuUsage - startCpuUsage).TotalMilliseconds;
                var totalMsPassed = (endTime - startTime).TotalMilliseconds;
                var cpuUsageTotal = cpuUsedMs / (Environment.ProcessorCount * totalMsPassed);
                
                stopwatch.Stop();

                var result = HealthCheckResult.Healthy("SystemResources", "System resources normal", stopwatch.Elapsed)
                    .WithData("WorkingSetMB", workingSet / 1024 / 1024)
                    .WithData("PrivateMemoryMB", privateMemory / 1024 / 1024)
                    .WithData("CpuUsagePercent", cpuUsageTotal * 100)
                    .WithData("ProcessorCount", Environment.ProcessorCount);

                // Check for high resource usage
                if (workingSet > 1024 * 1024 * 1024 || cpuUsageTotal > 0.8) // 1GB RAM or 80% CPU
                {
                    return HealthCheckResult.Degraded("SystemResources", "High resource usage detected", stopwatch.Elapsed)
                        .WithData("WorkingSetMB", workingSet / 1024 / 1024)
                        .WithData("CpuUsagePercent", cpuUsageTotal * 100);
                }

                return result;
            }
            catch (Exception ex)
            {
                stopwatch.Stop();
                logger.LogWarning(ex, "System resources health check failed");
                return HealthCheckResult.Unhealthy("SystemResources", "Failed to check system resources", ex, stopwatch.Elapsed);
            }
        };
    }
}
