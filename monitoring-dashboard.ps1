# SmaTrendFollower Live Trading Monitoring Dashboard
# Real-time monitoring script for live trading operations

param(
    [int]$RefreshInterval = 30,  # Refresh every 30 seconds
    [switch]$Continuous = $false,
    [switch]$AlertsOnly = $false
)

# Color definitions
$Colors = @{
    Success = "Green"
    Warning = "Yellow" 
    Error = "Red"
    Info = "Cyan"
    Header = "Magenta"
}

function Write-ColorOutput {
    param([string]$Message, [string]$Color = "White")
    Write-Host $Message -ForegroundColor $Colors[$Color]
}

function Get-SystemHealth {
    try {
        $healthOutput = & dotnet run --project SmaTrendFollower.Console -- health 2>&1
        return $healthOutput
    }
    catch {
        return "ERROR: Unable to get system health - $($_.Exception.Message)"
    }
}

function Get-AccountStatus {
    try {
        $accountOutput = & dotnet run --project SmaTrendFollower.Console -- account-status 2>&1
        return $accountOutput
    }
    catch {
        return "ERROR: Unable to get account status - $($_.Exception.Message)"
    }
}

function Get-TradingMetrics {
    try {
        $metricsOutput = & dotnet run --project SmaTrendFollower.Console -- metrics 2>&1
        return $metricsOutput
    }
    catch {
        return "ERROR: Unable to get trading metrics - $($_.Exception.Message)"
    }
}

function Get-LiveMarketData {
    try {
        $liveOutput = & dotnet run --project SmaTrendFollower.Console -- live 2>&1
        return $liveOutput
    }
    catch {
        return "ERROR: Unable to get live market data - $($_.Exception.Message)"
    }
}

function Show-Dashboard {
    Clear-Host
    
    $timestamp = Get-Date -Format "yyyy-MM-dd HH:mm:ss"
    
    Write-ColorOutput "╔══════════════════════════════════════════════════════════════════════════════════════╗" "Header"
    Write-ColorOutput "║                           SmaTrendFollower Live Trading Dashboard                    ║" "Header"
    Write-ColorOutput "║                                   $timestamp                                   ║" "Header"
    Write-ColorOutput "╚══════════════════════════════════════════════════════════════════════════════════════╝" "Header"
    Write-Host ""
    
    # System Health Section
    Write-ColorOutput "🏥 SYSTEM HEALTH" "Header"
    Write-ColorOutput "═══════════════════════════════════════════════════════════════════════════════════════" "Header"
    $health = Get-SystemHealth
    if ($health -match "ERROR") {
        Write-ColorOutput $health "Error"
    } else {
        Write-ColorOutput $health "Success"
    }
    Write-Host ""
    
    # Account Status Section
    Write-ColorOutput "💰 ACCOUNT STATUS" "Header"
    Write-ColorOutput "═══════════════════════════════════════════════════════════════════════════════════════" "Header"
    $account = Get-AccountStatus
    if ($account -match "ERROR") {
        Write-ColorOutput $account "Error"
    } else {
        Write-ColorOutput $account "Info"
    }
    Write-Host ""
    
    # Trading Metrics Section
    Write-ColorOutput "📊 TRADING METRICS" "Header"
    Write-ColorOutput "═══════════════════════════════════════════════════════════════════════════════════════" "Header"
    $metrics = Get-TradingMetrics
    if ($metrics -match "ERROR") {
        Write-ColorOutput $metrics "Error"
    } else {
        # Parse metrics for alerts
        if ($metrics -match "Total P&L: \$(-\d+\.\d+)") {
            $pnl = [decimal]$matches[1]
            if ($pnl -lt -100) {
                Write-ColorOutput "🚨 ALERT: Significant loss detected: $pnl" "Error"
            }
        }
        Write-ColorOutput $metrics "Info"
    }
    Write-Host ""
    
    # Live Market Data Section
    Write-ColorOutput "📡 LIVE MARKET DATA" "Header"
    Write-ColorOutput "═══════════════════════════════════════════════════════════════════════════════════════" "Header"
    $liveData = Get-LiveMarketData
    if ($liveData -match "ERROR") {
        Write-ColorOutput $liveData "Error"
    } else {
        Write-ColorOutput $liveData "Info"
    }
    Write-Host ""
    
    # Safety Status Check
    Write-ColorOutput "🛡️ SAFETY STATUS" "Header"
    Write-ColorOutput "═══════════════════════════════════════════════════════════════════════════════════════" "Header"
    try {
        $safetyCheck = & dotnet run --project SmaTrendFollower.Console -- validate 2>&1
        if ($safetyCheck -match "Trading cycle blocked") {
            Write-ColorOutput "🚨 TRADING BLOCKED: Safety systems active" "Warning"
        } elseif ($safetyCheck -match "Trading cycle completed") {
            Write-ColorOutput "✅ TRADING ACTIVE: All safety checks passed" "Success"
        } else {
            Write-ColorOutput "⚠️ UNKNOWN STATUS: $safetyCheck" "Warning"
        }
    }
    catch {
        Write-ColorOutput "❌ SAFETY CHECK FAILED: $($_.Exception.Message)" "Error"
    }
    Write-Host ""
    
    # Control Instructions
    Write-ColorOutput "🎮 CONTROLS" "Header"
    Write-ColorOutput "═══════════════════════════════════════════════════════════════════════════════════════" "Header"
    Write-ColorOutput "Press 'Q' to quit, 'R' to refresh now, 'E' for emergency stop" "Info"
    Write-Host ""
    
    if ($Continuous) {
        Write-ColorOutput "Next refresh in $RefreshInterval seconds..." "Info"
    }
}

function Emergency-Stop {
    Write-ColorOutput "🚨 EMERGENCY STOP INITIATED" "Error"
    Write-ColorOutput "═══════════════════════════════════════════════════════════════════════════════════════" "Error"
    
    try {
        # Enable dry run mode immediately
        Write-ColorOutput "Enabling dry run mode..." "Warning"
        & dotnet run --project SmaTrendFollower.Console -- --dry-run validate
        
        # Cancel all orders (if command exists)
        Write-ColorOutput "Attempting to cancel all orders..." "Warning"
        & dotnet run --project SmaTrendFollower.Console -- cancel-all-orders 2>$null
        
        Write-ColorOutput "✅ Emergency stop completed" "Success"
        Write-ColorOutput "System is now in dry run mode - no live trades will execute" "Success"
    }
    catch {
        Write-ColorOutput "❌ Emergency stop failed: $($_.Exception.Message)" "Error"
    }
    
    Read-Host "Press Enter to continue..."
}

# Main execution
Write-ColorOutput "Starting SmaTrendFollower Monitoring Dashboard..." "Info"
Write-ColorOutput "Refresh Interval: $RefreshInterval seconds" "Info"
Write-ColorOutput "Continuous Mode: $Continuous" "Info"
Write-Host ""

do {
    Show-Dashboard
    
    if ($Continuous) {
        # Non-blocking input check
        $timeout = $RefreshInterval
        $startTime = Get-Date
        
        do {
            if ([Console]::KeyAvailable) {
                $key = [Console]::ReadKey($true)
                switch ($key.KeyChar.ToString().ToUpper()) {
                    'Q' { 
                        Write-ColorOutput "Exiting dashboard..." "Info"
                        exit 
                    }
                    'R' { 
                        Write-ColorOutput "Refreshing now..." "Info"
                        break 
                    }
                    'E' { 
                        Emergency-Stop
                        break 
                    }
                }
            }
            Start-Sleep -Milliseconds 100
            $elapsed = ((Get-Date) - $startTime).TotalSeconds
        } while ($elapsed -lt $timeout)
    } else {
        $input = Read-Host "Enter command (Q=quit, R=refresh, E=emergency stop)"
        switch ($input.ToUpper()) {
            'Q' { 
                Write-ColorOutput "Exiting dashboard..." "Info"
                exit 
            }
            'E' { 
                Emergency-Stop 
            }
        }
    }
} while ($true)
