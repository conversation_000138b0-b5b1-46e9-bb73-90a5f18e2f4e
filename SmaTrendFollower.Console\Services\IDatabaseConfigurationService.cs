using Microsoft.EntityFrameworkCore;

namespace SmaTrendFollower.Services;

/// <summary>
/// Service for configuring database connections with optimized settings for performance
/// </summary>
public interface IDatabaseConfigurationService
{
    /// <summary>
    /// Configures SQLite DbContext options with optimized settings
    /// </summary>
    /// <param name="options">DbContext options builder</param>
    /// <param name="connectionString">SQLite connection string</param>
    void ConfigureSqlite(DbContextOptionsBuilder options, string connectionString);

    /// <summary>
    /// Gets optimized SQLite connection string with performance settings
    /// </summary>
    /// <param name="databasePath">Path to SQLite database file</param>
    /// <returns>Optimized connection string</returns>
    string GetOptimizedConnectionString(string databasePath);

    /// <summary>
    /// Performs database optimization operations
    /// </summary>
    /// <param name="connectionString">Database connection string</param>
    Task OptimizeDatabaseAsync(string connectionString);
}
