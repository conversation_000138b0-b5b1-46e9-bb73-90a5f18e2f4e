using Microsoft.Extensions.Logging;
using System.Text.Json;
using static System.Console;

namespace SmaTrendFollower.Services;

/// <summary>
/// Console commands for cache management and monitoring
/// </summary>
public sealed class CacheConsoleCommands
{
    private readonly ICacheManagementService _managementService;
    private readonly ICacheMetricsService _metricsService;
    private readonly ICacheWarmingService _warmingService;
    private readonly ILogger<CacheConsoleCommands> _logger;

    public CacheConsoleCommands(
        ICacheManagementService managementService,
        ICacheMetricsService metricsService,
        ICacheWarmingService warmingService,
        ILogger<CacheConsoleCommands> logger)
    {
        _managementService = managementService;
        _metricsService = metricsService;
        _warmingService = warmingService;
        _logger = logger;
    }

    /// <summary>
    /// Displays cache dashboard information
    /// </summary>
    public async Task ShowDashboardAsync()
    {
        try
        {
            WriteLine("=== SmaTrendFollower Cache Dashboard ===");
            WriteLine();

            var dashboard = await _managementService.GetDashboardDataAsync();

            // Overview
            WriteLine("📊 CACHE OVERVIEW");
            WriteLine($"   Total Symbols: {dashboard.Overview.TotalSymbols:N0}");
            WriteLine($"   Total Bars: {dashboard.Overview.TotalBars:N0}");
            WriteLine($"   Total Size: {FormatBytes(dashboard.Overview.TotalSizeBytes)}");
            WriteLine($"   Compressed Size: {FormatBytes(dashboard.Overview.CompressedSizeBytes)}");
            WriteLine($"   Compression Ratio: {dashboard.Overview.CompressionRatio:P2}");
            WriteLine($"   Data Span: {dashboard.Overview.DataSpan.Days} days");
            WriteLine();

            // Performance
            WriteLine("⚡ PERFORMANCE METRICS");
            WriteLine($"   Cache Hit Ratio: {dashboard.Performance.CacheHitRatio:P2}");
            WriteLine($"   Total Requests: {dashboard.Performance.TotalRequests:N0}");
            WriteLine($"   Cache Hits: {dashboard.Performance.CacheHits:N0}");
            WriteLine($"   Cache Misses: {dashboard.Performance.CacheMisses:N0}");
            WriteLine($"   Avg Cache Response: {dashboard.Performance.AverageCacheResponseTimeMs:F2}ms");
            WriteLine($"   Avg API Response: {dashboard.Performance.AverageApiResponseTimeMs:F2}ms");
            WriteLine($"   API Throttle Events: {dashboard.Performance.ApiThrottleEvents:N0}");
            WriteLine();

            // Health
            WriteLine("🏥 CACHE HEALTH");
            WriteLine($"   Overall Health: {dashboard.Health.OverallHealth}");
            WriteLine($"   Health Score: {dashboard.Health.HealthScore:P1}");
            foreach (var metric in dashboard.Health.Metrics.Take(3))
            {
                var status = metric.Status switch
                {
                    "Good" => "✅",
                    "Warning" => "⚠️",
                    "Critical" => "❌",
                    _ => "❓"
                };
                WriteLine($"   {status} {metric.Name}: {metric.Value:F2} (threshold: {metric.Threshold:F2})");
            }
            WriteLine();

            // Top Symbols
            WriteLine("🔥 TOP SYMBOLS (by requests)");
            foreach (var symbol in dashboard.TopSymbols.Take(10))
            {
                WriteLine($"   {symbol.Key}: {symbol.Value.TotalBars:N0} bars, {symbol.Value.CacheHitRatio:P1} hit ratio");
            }
            WriteLine();

            WriteLine($"Last Updated: {dashboard.LastUpdated:yyyy-MM-dd HH:mm:ss} UTC");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error displaying cache dashboard");
            WriteLine($"❌ Error: {ex.Message}");
        }
    }

    /// <summary>
    /// Performs cache maintenance
    /// </summary>
    public async Task RunMaintenanceAsync(string mode = "standard")
    {
        try
        {
            WriteLine($"🔧 Starting cache maintenance ({mode})...");

            var options = mode.ToLower() switch
            {
                "quick" => CacheMaintenanceDefaults.Quick,
                "full" => CacheMaintenanceDefaults.Full,
                _ => CacheMaintenanceDefaults.Standard
            };

            var result = await _managementService.PerformMaintenanceAsync(options);

            WriteLine($"✅ Maintenance completed in {result.Duration.TotalSeconds:F1}s");
            WriteLine($"   Success: {result.Success}");
            WriteLine($"   Data Cleaned: {FormatBytes(result.DataCleaned)}");
            WriteLine($"   Bars Compressed: {result.BarsCompressed:N0}");
            WriteLine($"   Space Saved: {FormatBytes(result.SpaceSaved)}");
            WriteLine($"   Operations: {result.Operations.Length}");

            if (result.Errors.Any())
            {
                WriteLine("⚠️ Errors:");
                foreach (var error in result.Errors)
                {
                    WriteLine($"   - {error}");
                }
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error during cache maintenance");
            WriteLine($"❌ Error: {ex.Message}");
        }
    }

    /// <summary>
    /// Validates cache integrity
    /// </summary>
    public async Task ValidateCacheAsync()
    {
        try
        {
            WriteLine("🔍 Validating cache integrity...");

            var result = await _managementService.ValidateCacheIntegrityAsync();

            WriteLine($"✅ Validation completed");
            WriteLine($"   Valid: {result.IsValid}");
            WriteLine($"   Total Checks: {result.TotalChecks}");
            WriteLine($"   Issues Found: {result.IssuesFound}");

            if (result.Issues.Any())
            {
                WriteLine("\n📋 Issues:");
                foreach (var issue in result.Issues.Take(10))
                {
                    var icon = issue.Severity switch
                    {
                        "Error" => "❌",
                        "Warning" => "⚠️",
                        _ => "ℹ️"
                    };
                    WriteLine($"   {icon} [{issue.Category}] {issue.Description}");
                    if (!string.IsNullOrEmpty(issue.Symbol))
                    {
                        WriteLine($"      Symbol: {issue.Symbol} {issue.TimeFrame}");
                    }
                }

                if (result.Issues.Length > 10)
                {
                    WriteLine($"   ... and {result.Issues.Length - 10} more issues");
                }
            }

            if (result.Recommendations.Any())
            {
                WriteLine("\n💡 Recommendations:");
                foreach (var recommendation in result.Recommendations)
                {
                    WriteLine($"   - {recommendation}");
                }
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error during cache validation");
            WriteLine($"❌ Error: {ex.Message}");
        }
    }

    /// <summary>
    /// Refreshes cache for specific symbols
    /// </summary>
    public async Task RefreshSymbolsAsync(string symbolsInput, int days = 30)
    {
        try
        {
            var symbols = symbolsInput.Split(',', StringSplitOptions.RemoveEmptyEntries)
                                    .Select(s => s.Trim().ToUpper())
                                    .ToArray();

            WriteLine($"🔄 Refreshing cache for {symbols.Length} symbols ({days} days)...");

            var result = await _managementService.RefreshSymbolsAsync(symbols, null, days);

            WriteLine($"✅ Refresh completed in {result.Duration.TotalSeconds:F1}s");
            WriteLine($"   Processed: {result.SymbolsProcessed}");
            WriteLine($"   Successful: {result.SymbolsSuccessful}");
            WriteLine($"   Failed: {result.SymbolsFailed}");
            WriteLine($"   New Bars: {result.NewBarsAdded:N0}");
            WriteLine($"   Updated Bars: {result.BarsUpdated:N0}");

            if (result.FailedSymbols.Any())
            {
                WriteLine("\n❌ Failed Symbols:");
                foreach (var symbol in result.FailedSymbols)
                {
                    WriteLine($"   - {symbol}");
                }
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error during symbol refresh");
            WriteLine($"❌ Error: {ex.Message}");
        }
    }

    /// <summary>
    /// Runs cache warming
    /// </summary>
    public async Task WarmCacheAsync(string type = "essential")
    {
        try
        {
            WriteLine($"🔥 Starting cache warming ({type})...");

            var (symbolsWarmed, barsWarmed) = type.ToLower() switch
            {
                "essential" => await _warmingService.WarmEssentialSymbolsAsync(),
                _ => await _warmingService.WarmEssentialSymbolsAsync()
            };

            WriteLine($"✅ Cache warming completed");
            WriteLine($"   Symbols Warmed: {symbolsWarmed}");
            WriteLine($"   Bars Cached: {barsWarmed:N0}");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error during cache warming");
            WriteLine($"❌ Error: {ex.Message}");
        }
    }

    /// <summary>
    /// Shows cache metrics in JSON format
    /// </summary>
    public void ShowMetricsJson()
    {
        try
        {
            var json = _metricsService.ExportMetricsAsJson();
            WriteLine("📊 Cache Metrics (JSON):");
            WriteLine(json);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error exporting metrics");
            WriteLine($"❌ Error: {ex.Message}");
        }
    }

    /// <summary>
    /// Resets cache metrics
    /// </summary>
    public void ResetMetrics()
    {
        try
        {
            _metricsService.ResetMetrics();
            WriteLine("✅ Cache metrics reset");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error resetting metrics");
            WriteLine($"❌ Error: {ex.Message}");
        }
    }

    /// <summary>
    /// Shows available cache commands
    /// </summary>
    public void ShowHelp()
    {
        WriteLine("=== SmaTrendFollower Cache Commands ===");
        WriteLine();
        WriteLine("📊 dashboard              - Show cache dashboard");
        WriteLine("🔧 maintenance [mode]     - Run maintenance (quick/standard/full)");
        WriteLine("🔍 validate               - Validate cache integrity");
        WriteLine("🔄 refresh <symbols>      - Refresh symbols (comma-separated)");
        WriteLine("🔥 warm [type]            - Warm cache (essential)");
        WriteLine("📈 metrics                - Show metrics in JSON");
        WriteLine("🔄 reset-metrics          - Reset performance metrics");
        WriteLine("❓ help                   - Show this help");
        WriteLine();
        WriteLine("Examples:");
        WriteLine("  cache dashboard");
        WriteLine("  cache maintenance full");
        WriteLine("  cache refresh SPY,QQQ,AAPL");
        WriteLine("  cache warm essential");
    }

    private static string FormatBytes(long bytes)
    {
        if (bytes == 0) return "0 B";
        
        string[] sizes = { "B", "KB", "MB", "GB", "TB" };
        int order = 0;
        double size = bytes;
        
        while (size >= 1024 && order < sizes.Length - 1)
        {
            order++;
            size /= 1024;
        }
        
        return $"{size:F2} {sizes[order]}";
    }
}
