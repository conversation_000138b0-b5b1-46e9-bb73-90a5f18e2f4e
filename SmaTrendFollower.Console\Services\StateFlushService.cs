using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;
using System.Text.Json;

namespace SmaTrendFollower.Services;

/// <summary>
/// Background service that periodically flushes Redis state to SQLite for persistence.
/// Ensures important trading state is preserved across restarts.
/// </summary>
public sealed class StateFlushService : BackgroundService
{
    private readonly ILiveStateStore _liveStateStore;
    private readonly IBarStore _historicalStore;
    private readonly ILogger<StateFlushService> _logger;
    private readonly TimeSpan _flushInterval;
    private readonly string _stateBackupPath;

    public StateFlushService(
        ILiveStateStore liveStateStore,
        IBarStore historicalStore,
        IConfiguration configuration,
        ILogger<StateFlushService> logger)
    {
        _liveStateStore = liveStateStore;
        _historicalStore = historicalStore;
        _logger = logger;
        
        _flushInterval = TimeSpan.FromMinutes(int.Parse(configuration["STATE_FLUSH_INTERVAL_MINUTES"] ?? "15"));
        _stateBackupPath = configuration["STATE_BACKUP_PATH"] ?? "Data/state_backup.json";
        
        // Ensure backup directory exists
        var backupDir = Path.GetDirectoryName(_stateBackupPath);
        if (!string.IsNullOrEmpty(backupDir))
        {
            Directory.CreateDirectory(backupDir);
        }
    }

    protected override async Task ExecuteAsync(CancellationToken stoppingToken)
    {
        _logger.LogInformation("StateFlushService started with {FlushInterval} interval", _flushInterval);

        while (!stoppingToken.IsCancellationRequested)
        {
            try
            {
                await FlushStateAsync(stoppingToken);
                await Task.Delay(_flushInterval, stoppingToken);
            }
            catch (OperationCanceledException)
            {
                // Expected when cancellation is requested
                break;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error during state flush operation");
                
                // Wait a shorter interval before retrying on error
                try
                {
                    await Task.Delay(TimeSpan.FromMinutes(1), stoppingToken);
                }
                catch (OperationCanceledException)
                {
                    break;
                }
            }
        }

        // Perform final flush on shutdown
        try
        {
            _logger.LogInformation("Performing final state flush on shutdown");
            await FlushStateAsync(CancellationToken.None);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error during final state flush");
        }
    }

    /// <summary>
    /// Flushes current Redis state to persistent storage
    /// </summary>
    private async Task FlushStateAsync(CancellationToken cancellationToken)
    {
        try
        {
            var stats = await _liveStateStore.GetStatsAsync(cancellationToken);
            
            if (stats.TrailingStopCount == 0 && stats.PositionStateCount == 0 && stats.RetryQueueLength == 0)
            {
                _logger.LogDebug("No state to flush");
                return;
            }

            _logger.LogDebug("Flushing state: {TrailingStops} stops, {Positions} positions, {RetryItems} retry items",
                stats.TrailingStopCount, stats.PositionStateCount, stats.RetryQueueLength);

            var stateSnapshot = await CreateStateSnapshotAsync(cancellationToken);
            await SaveStateSnapshotAsync(stateSnapshot, cancellationToken);

            _logger.LogInformation("State flushed successfully at {Time:yyyy-MM-dd HH:mm:ss} UTC", DateTime.UtcNow);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to flush state");
            throw;
        }
    }

    /// <summary>
    /// Creates a snapshot of current Redis state
    /// </summary>
    private async Task<StateSnapshot> CreateStateSnapshotAsync(CancellationToken cancellationToken)
    {
        var trailingStops = await _liveStateStore.GetAllTrailingStopsAsync(cancellationToken);
        
        var positionStates = new Dictionary<string, PositionState>();
        foreach (var symbol in trailingStops.Keys)
        {
            var positionState = await _liveStateStore.GetPositionStateAsync(symbol, cancellationToken);
            if (positionState != null)
            {
                positionStates[symbol] = positionState;
            }
        }

        var retryItems = new List<RetryItem>();
        var queueLength = await _liveStateStore.GetRetryQueueLengthAsync(cancellationToken);
        for (int i = 0; i < Math.Min(queueLength, 100); i++) // Limit to prevent memory issues
        {
            var item = await _liveStateStore.DequeueRetryAsync(cancellationToken);
            if (item != null)
            {
                retryItems.Add(item);
                // Re-enqueue the item since we're just peeking
                await _liveStateStore.EnqueueRetryAsync(item, cancellationToken);
            }
        }

        return new StateSnapshot(
            DateTime.UtcNow,
            trailingStops,
            positionStates,
            retryItems
        );
    }

    /// <summary>
    /// Saves state snapshot to persistent storage
    /// </summary>
    private async Task SaveStateSnapshotAsync(StateSnapshot snapshot, CancellationToken cancellationToken)
    {
        var json = JsonSerializer.Serialize(snapshot, new JsonSerializerOptions
        {
            WriteIndented = true,
            PropertyNamingPolicy = JsonNamingPolicy.CamelCase
        });

        // Create backup of existing file
        if (File.Exists(_stateBackupPath))
        {
            var backupPath = _stateBackupPath + ".bak";
            File.Copy(_stateBackupPath, backupPath, overwrite: true);
        }

        await File.WriteAllTextAsync(_stateBackupPath, json, cancellationToken);
        
        _logger.LogDebug("State snapshot saved to {Path}", _stateBackupPath);
    }

    /// <summary>
    /// Restores state from persistent storage on startup
    /// </summary>
    public async Task RestoreStateAsync(CancellationToken cancellationToken = default)
    {
        try
        {
            if (!File.Exists(_stateBackupPath))
            {
                _logger.LogInformation("No state backup file found, starting with clean state");
                return;
            }

            var json = await File.ReadAllTextAsync(_stateBackupPath, cancellationToken);
            var snapshot = JsonSerializer.Deserialize<StateSnapshot>(json, new JsonSerializerOptions
            {
                PropertyNamingPolicy = JsonNamingPolicy.CamelCase
            });

            if (snapshot == null)
            {
                _logger.LogWarning("Failed to deserialize state snapshot");
                return;
            }

            // Check if snapshot is too old (older than 24 hours)
            if (DateTime.UtcNow - snapshot.Timestamp > TimeSpan.FromHours(24))
            {
                _logger.LogWarning("State snapshot is too old ({Age:F1} hours), skipping restore",
                    (DateTime.UtcNow - snapshot.Timestamp).TotalHours);
                return;
            }

            // Restore trailing stops
            foreach (var kvp in snapshot.TrailingStops)
            {
                await _liveStateStore.SetTrailingStopAsync(kvp.Key, kvp.Value, cancellationToken);
            }

            // Restore position states
            foreach (var kvp in snapshot.PositionStates)
            {
                await _liveStateStore.SetPositionStateAsync(kvp.Key, kvp.Value, cancellationToken);
            }

            // Restore retry items
            foreach (var item in snapshot.RetryItems)
            {
                await _liveStateStore.EnqueueRetryAsync(item, cancellationToken);
            }

            _logger.LogInformation("Restored state: {TrailingStops} stops, {Positions} positions, {RetryItems} retry items",
                snapshot.TrailingStops.Count, snapshot.PositionStates.Count, snapshot.RetryItems.Count);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to restore state from backup");
            // Don't throw - continue with clean state
        }
    }
}

/// <summary>
/// Represents a snapshot of trading state for persistence
/// </summary>
public record StateSnapshot(
    DateTime Timestamp,
    Dictionary<string, decimal> TrailingStops,
    Dictionary<string, PositionState> PositionStates,
    List<RetryItem> RetryItems
);
