namespace SmaTrendFollower.Services.ErrorHandling;

/// <summary>
/// Categories of errors that can occur in the trading system
/// </summary>
public enum ErrorCategory
{
    /// <summary>
    /// Market data retrieval and processing errors
    /// </summary>
    MarketData,

    /// <summary>
    /// Trade execution and order management errors
    /// </summary>
    TradeExecution,

    /// <summary>
    /// Risk management and position sizing errors
    /// </summary>
    RiskManagement,

    /// <summary>
    /// External API communication errors (Alpaca, Polygon, Discord, etc.)
    /// </summary>
    ExternalApi,

    /// <summary>
    /// Configuration and setup errors
    /// </summary>
    Configuration,

    /// <summary>
    /// Signal generation and analysis errors
    /// </summary>
    SignalGeneration,

    /// <summary>
    /// Cache and data storage errors
    /// </summary>
    DataStorage,

    /// <summary>
    /// Network connectivity and communication errors
    /// </summary>
    Network,

    /// <summary>
    /// Authentication and authorization errors
    /// </summary>
    Authentication,

    /// <summary>
    /// Rate limiting and throttling errors
    /// </summary>
    RateLimit,

    /// <summary>
    /// System resource and performance errors
    /// </summary>
    System,

    /// <summary>
    /// Business logic and validation errors
    /// </summary>
    BusinessLogic,

    /// <summary>
    /// Unknown or unclassified errors
    /// </summary>
    Unknown
}

/// <summary>
/// Severity levels for errors
/// </summary>
public enum ErrorSeverity
{
    /// <summary>
    /// Low severity - informational, system can continue normally
    /// </summary>
    Low,

    /// <summary>
    /// Medium severity - warning, system can continue with degraded functionality
    /// </summary>
    Medium,

    /// <summary>
    /// High severity - error, immediate attention required but system can continue
    /// </summary>
    High,

    /// <summary>
    /// Critical severity - system failure, immediate intervention required
    /// </summary>
    Critical
}

/// <summary>
/// Recovery strategies for different types of errors
/// </summary>
public enum RecoveryStrategy
{
    /// <summary>
    /// No recovery action needed
    /// </summary>
    None,

    /// <summary>
    /// Retry the operation with exponential backoff
    /// </summary>
    Retry,

    /// <summary>
    /// Use fallback data source or method
    /// </summary>
    Fallback,

    /// <summary>
    /// Skip the current operation and continue
    /// </summary>
    Skip,

    /// <summary>
    /// Gracefully degrade functionality
    /// </summary>
    Degrade,

    /// <summary>
    /// Stop current operation and alert
    /// </summary>
    Stop,

    /// <summary>
    /// Circuit breaker - temporarily disable service
    /// </summary>
    CircuitBreaker
}

/// <summary>
/// Error context information for enhanced debugging and monitoring
/// </summary>
public sealed class ErrorContext
{
    public string OperationName { get; init; } = string.Empty;
    public string ServiceName { get; init; } = string.Empty;
    public DateTime Timestamp { get; init; } = DateTime.UtcNow;
    public string? UserId { get; init; }
    public string? SessionId { get; init; }
    public string? CorrelationId { get; init; }
    public Dictionary<string, object> Properties { get; init; } = new();
    public string? StackTrace { get; init; }
    public string? MachineName { get; init; } = Environment.MachineName;
    public string? ProcessId { get; init; } = Environment.ProcessId.ToString();

    public ErrorContext WithProperty(string key, object value)
    {
        Properties[key] = value;
        return this;
    }

    public T? GetProperty<T>(string key) where T : class
    {
        return Properties.TryGetValue(key, out var value) ? value as T : null;
    }

    public T GetProperty<T>(string key, T defaultValue) where T : struct
    {
        if (Properties.TryGetValue(key, out var value) && value is T typedValue)
        {
            return typedValue;
        }
        return defaultValue;
    }
}

/// <summary>
/// Error handling result with recovery information
/// </summary>
public sealed class ErrorHandlingResult
{
    public bool IsHandled { get; init; }
    public bool ShouldRetry { get; init; }
    public TimeSpan? RetryDelay { get; init; }
    public RecoveryStrategy Strategy { get; init; }
    public string? RecoveryMessage { get; init; }
    public Dictionary<string, object> RecoveryData { get; init; } = new();

    public static ErrorHandlingResult Handled(RecoveryStrategy strategy, string? message = null)
    {
        return new ErrorHandlingResult
        {
            IsHandled = true,
            Strategy = strategy,
            RecoveryMessage = message
        };
    }

    public static ErrorHandlingResult Retry(TimeSpan delay, string? message = null)
    {
        return new ErrorHandlingResult
        {
            IsHandled = true,
            ShouldRetry = true,
            RetryDelay = delay,
            Strategy = RecoveryStrategy.Retry,
            RecoveryMessage = message
        };
    }

    public static ErrorHandlingResult Fallback(string? message = null)
    {
        return new ErrorHandlingResult
        {
            IsHandled = true,
            Strategy = RecoveryStrategy.Fallback,
            RecoveryMessage = message
        };
    }

    public static ErrorHandlingResult Skip(string? message = null)
    {
        return new ErrorHandlingResult
        {
            IsHandled = true,
            Strategy = RecoveryStrategy.Skip,
            RecoveryMessage = message
        };
    }

    public static ErrorHandlingResult Stop(string? message = null)
    {
        return new ErrorHandlingResult
        {
            IsHandled = true,
            Strategy = RecoveryStrategy.Stop,
            RecoveryMessage = message
        };
    }

    public static ErrorHandlingResult NotHandled()
    {
        return new ErrorHandlingResult
        {
            IsHandled = false,
            Strategy = RecoveryStrategy.None
        };
    }

    public ErrorHandlingResult WithData(string key, object value)
    {
        RecoveryData[key] = value;
        return this;
    }
}
