namespace SmaTrendFollower.Services;

/// <summary>
/// Interface for dynamic safety configuration that adjusts based on account size
/// </summary>
public interface IDynamicSafetyConfigurationService
{
    /// <summary>
    /// Gets dynamic safety configuration based on current account equity
    /// </summary>
    Task<SafetyConfiguration> GetDynamicConfigurationAsync();

    /// <summary>
    /// Gets account size tier for logging and monitoring
    /// </summary>
    Task<AccountSizeTier> GetAccountSizeTierAsync();

    /// <summary>
    /// Gets comprehensive risk summary for the current account
    /// </summary>
    Task<AccountRiskSummary> GetAccountRiskSummaryAsync();
}
