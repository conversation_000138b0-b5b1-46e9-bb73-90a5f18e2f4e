using System.ComponentModel;

namespace SmaTrendFollower.Models;

/// <summary>
/// SEC filing types for event filtering and market data analysis.
/// Prefixed with '<PERSON><PERSON>' to avoid conflicts with external SDK enums.
/// </summary>
public enum SmaSecFilingType
{
    [Description("10-K Annual Report")]
    Form10K,

    [Description("10-Q Quarterly Report")]
    Form10Q,

    [Description("8-K Current Report")]
    Form8K,

    [Description("Proxy Statement")]
    ProxyStatement,

    [Description("13F Holdings Report")]
    Form13F,

    [Description("4 Insider Trading")]
    Form4,

    [Description("3 Initial Statement")]
    Form3,

    [Description("5 Annual Statement")]
    Form5,

    [Description("S-1 Registration")]
    FormS1,

    [Description("424B Prospectus")]
    Form424B,

    [Description("Unknown Filing Type")]
    Unknown
}

/// <summary>
/// Market event types for filtering and analysis.
/// Prefixed with 'Sma' to avoid conflicts with external SDK enums.
/// </summary>
public enum SmaMarketEventType
{
    [Description("Earnings Announcement")]
    EarningsAnnouncement,

    [Description("Dividend Declaration")]
    DividendDeclaration,

    [Description("Stock Split")]
    StockSplit,

    [Description("Merger & Acquisition")]
    MergerAcquisition,

    [Description("Spin-off")]
    Spinoff,

    [Description("IPO Launch")]
    IpoLaunch,

    [Description("Delisting Notice")]
    DelistingNotice,

    [Description("Bankruptcy Filing")]
    BankruptcyFiling,

    [Description("FDA Approval")]
    FdaApproval,

    [Description("Product Launch")]
    ProductLaunch,

    [Description("Conference Call")]
    ConferenceCall,

    [Description("Analyst Rating Change")]
    AnalystRatingChange,

    [Description("Insider Trading")]
    InsiderTrading,

    [Description("Share Buyback")]
    ShareBuyback,

    [Description("Guidance Update")]
    GuidanceUpdate,

    [Description("Unknown Event")]
    Unknown
}

/// <summary>
/// Event filter criteria for market data and trading decisions.
/// Prefixed with 'Sma' to avoid conflicts with external SDK enums.
/// </summary>
public enum SmaEventFilterType
{
    [Description("Include All Events")]
    IncludeAll,

    [Description("Exclude Earnings Events")]
    ExcludeEarnings,

    [Description("Exclude FDA Events")]
    ExcludeFda,

    [Description("Exclude M&A Events")]
    ExcludeMergers,

    [Description("Exclude High Impact Events")]
    ExcludeHighImpact,

    [Description("Include Only Low Impact")]
    IncludeLowImpactOnly,

    [Description("Include Only Earnings")]
    IncludeEarningsOnly,

    [Description("Custom Filter")]
    Custom
}

/// <summary>
/// Event impact levels for risk management and position sizing.
/// </summary>
public enum SmaEventImpactLevel
{
    [Description("Low Impact")]
    Low = 1,

    [Description("Medium Impact")]
    Medium = 2,

    [Description("High Impact")]
    High = 3,

    [Description("Critical Impact")]
    Critical = 4,

    [Description("Unknown Impact")]
    Unknown = 0
}

/// <summary>
/// Trading action recommendations based on events.
/// </summary>
public enum SmaEventTradingAction
{
    [Description("Continue Normal Trading")]
    ContinueNormal,

    [Description("Reduce Position Size")]
    ReducePositionSize,

    [Description("Avoid New Positions")]
    AvoidNewPositions,

    [Description("Close Existing Positions")]
    CloseExistingPositions,

    [Description("Increase Monitoring")]
    IncreaseMonitoring,

    [Description("Apply Tighter Stops")]
    ApplyTighterStops,

    [Description("No Action Required")]
    NoAction
}

/// <summary>
/// Time periods for event filtering and analysis.
/// </summary>
public enum SmaEventTimePeriod
{
    [Description("Next 24 Hours")]
    Next24Hours,

    [Description("Next 3 Days")]
    Next3Days,

    [Description("Next Week")]
    NextWeek,

    [Description("Next Month")]
    NextMonth,

    [Description("Past 24 Hours")]
    Past24Hours,

    [Description("Past 3 Days")]
    Past3Days,

    [Description("Past Week")]
    PastWeek,

    [Description("Past Month")]
    PastMonth,

    [Description("Custom Range")]
    CustomRange
}

/// <summary>
/// Internal order side enum to avoid conflicts with external SDKs.
/// </summary>
public enum SmaOrderSide
{
    [Description("Buy Order")]
    Buy,

    [Description("Sell Order")]
    Sell
}

/// <summary>
/// Internal order type enum to avoid conflicts with external SDKs.
/// </summary>
public enum SmaOrderType
{
    [Description("Market Order")]
    Market,

    [Description("Limit Order")]
    Limit,

    [Description("Stop Order")]
    Stop,

    [Description("Stop Limit Order")]
    StopLimit,

    [Description("Trailing Stop Order")]
    TrailingStop
}

/// <summary>
/// Data model for market events with proper enum usage.
/// </summary>
public class MarketEvent
{
    public string Symbol { get; set; } = string.Empty;
    public SmaMarketEventType EventType { get; set; }
    public DateTime EventDate { get; set; }
    public SmaEventImpactLevel ImpactLevel { get; set; }
    public string Description { get; set; } = string.Empty;
    public string Source { get; set; } = string.Empty;
    public DateTime CacheTimestamp { get; set; } = DateTime.UtcNow;
    public Dictionary<string, object> Metadata { get; set; } = new();
}

/// <summary>
/// Data model for SEC filings with proper enum usage.
/// </summary>
public class SecFiling
{
    public string Symbol { get; set; } = string.Empty;
    public SmaSecFilingType FilingType { get; set; }
    public DateTime FilingDate { get; set; }
    public string Description { get; set; } = string.Empty;
    public string Url { get; set; } = string.Empty;
    public DateTime CacheTimestamp { get; set; } = DateTime.UtcNow;
    public Dictionary<string, object> Metadata { get; set; } = new();
}

/// <summary>
/// Trading recommendation based on event analysis.
/// </summary>
public class EventTradingRecommendation
{
    public string Symbol { get; set; } = string.Empty;
    public SmaEventTradingAction Action { get; set; }
    public string Reason { get; set; } = string.Empty;
    public decimal Confidence { get; set; } // 0.0 to 1.0
    public decimal RecommendedPositionSizeMultiplier { get; set; } = 1.0m;
    public SmaEventFilterType FilterType { get; set; }
    public int AnalyzedEvents { get; set; }
    public int AnalyzedFilings { get; set; }
    public DateTime GeneratedAt { get; set; } = DateTime.UtcNow;
}

/// <summary>
/// Extension methods for enum operations and conversions.
/// </summary>
public static class EnumExtensions
{
    /// <summary>
    /// Gets the description attribute value for an enum value.
    /// </summary>
    public static string GetDescription(this Enum value)
    {
        var field = value.GetType().GetField(value.ToString());
        var attribute = field?.GetCustomAttributes(typeof(DescriptionAttribute), false)
                              .FirstOrDefault() as DescriptionAttribute;
        return attribute?.Description ?? value.ToString();
    }

    /// <summary>
    /// Parses an enum from its description attribute.
    /// </summary>
    public static T ParseFromDescription<T>(string description) where T : Enum
    {
        var type = typeof(T);
        foreach (var field in type.GetFields())
        {
            var attribute = field.GetCustomAttributes(typeof(DescriptionAttribute), false)
                                 .FirstOrDefault() as DescriptionAttribute;
            if (attribute?.Description == description)
            {
                return (T)field.GetValue(null)!;
            }
        }

        // Fallback to standard enum parsing
        return (T)Enum.Parse(type, description, true);
    }

    /// <summary>
    /// Gets all enum values with their descriptions.
    /// </summary>
    public static Dictionary<T, string> GetEnumDescriptions<T>() where T : struct, Enum
    {
        return Enum.GetValues<T>()
                   .ToDictionary(value => value, value => value.GetDescription());
    }

    /// <summary>
    /// Validates if an enum value is defined.
    /// </summary>
    public static bool IsValidEnumValue<T>(this T enumValue) where T : Enum
    {
        return Enum.IsDefined(typeof(T), enumValue);
    }

    /// <summary>
    /// Gets a safe enum value, returning a default if the value is not defined.
    /// </summary>
    public static T GetSafeEnumValue<T>(this T enumValue, T defaultValue) where T : Enum
    {
        return enumValue.IsValidEnumValue() ? enumValue : defaultValue;
    }
}