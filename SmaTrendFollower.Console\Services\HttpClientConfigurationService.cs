using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using Polly;
using Polly.Extensions.Http;
using System.Net;

namespace SmaTrendFollower.Services;

/// <summary>
/// Service for configuring HTTP clients with enhanced connectivity and resilience features
/// </summary>
public static class HttpClientConfigurationService
{
    /// <summary>
    /// Configures HTTP clients for Alpaca and Polygon APIs with enhanced connectivity features
    /// </summary>
    public static void ConfigureHttpClients(IServiceCollection services, ILogger logger)
    {
        // Configure Alpaca HTTP client
        services.AddHttpClient("alpaca", client =>
        {
            client.BaseAddress = new Uri("https://paper-api.alpaca.markets/");
            client.Timeout = TimeSpan.FromSeconds(30);
            client.DefaultRequestHeaders.Add("User-Agent", "SmaTrendFollower/1.0");
            client.DefaultRequestHeaders.Add("Accept", "application/json");
        })
        .ConfigurePrimaryHttpMessageHandler(() => new HttpClientHandler
        {
            // Enhanced connection pooling settings for high-frequency trading
            MaxConnectionsPerServer = 20, // Increased for better throughput

            // Connection settings for better reliability and performance
            UseCookies = false,
            UseProxy = false,
            PreAuthenticate = true, // Pre-authenticate for faster subsequent requests

            // Connection lifecycle management
            MaxRequestContentBufferSize = 1024 * 1024, // 1MB buffer
            MaxResponseHeadersLength = 64 * 1024, // 64KB headers

            // SSL/TLS settings
            ServerCertificateCustomValidationCallback = HttpClientHandler.DangerousAcceptAnyServerCertificateValidator
        })
        .AddPolicyHandler(GetRetryPolicy("Alpaca", logger))
        .AddPolicyHandler(GetCircuitBreakerPolicy("Alpaca", logger))
        .AddPolicyHandler(GetTimeoutPolicy());

        // Configure Polygon HTTP client
        services.AddHttpClient("polygon", client =>
        {
            client.BaseAddress = new Uri("https://api.polygon.io/");
            client.Timeout = TimeSpan.FromSeconds(30);
            client.DefaultRequestHeaders.Add("User-Agent", "SmaTrendFollower/1.0");
            client.DefaultRequestHeaders.Add("Accept", "application/json");
        })
        .ConfigurePrimaryHttpMessageHandler(() => new HttpClientHandler
        {
            // Enhanced connection pooling settings for high-frequency market data
            MaxConnectionsPerServer = 25, // Higher for Polygon due to more frequent calls

            // Connection settings for better reliability and performance
            UseCookies = false,
            UseProxy = false,
            PreAuthenticate = true, // Pre-authenticate for faster subsequent requests

            // Connection lifecycle management
            MaxRequestContentBufferSize = 2 * 1024 * 1024, // 2MB buffer for larger market data responses
            MaxResponseHeadersLength = 64 * 1024, // 64KB headers

            // SSL/TLS settings
            ServerCertificateCustomValidationCallback = HttpClientHandler.DangerousAcceptAnyServerCertificateValidator
        })
        .AddPolicyHandler(GetRetryPolicy("Polygon", logger))
        .AddPolicyHandler(GetCircuitBreakerPolicy("Polygon", logger))
        .AddPolicyHandler(GetTimeoutPolicy());

        logger.LogInformation("HTTP clients configured with enhanced connectivity and resilience features");
    }

    /// <summary>
    /// Creates a retry policy with exponential backoff
    /// </summary>
    private static IAsyncPolicy<HttpResponseMessage> GetRetryPolicy(string apiName, ILogger logger)
    {
        return HttpPolicyExtensions
            .HandleTransientHttpError() // Handles HttpRequestException and 5XX, 408 status codes
            .OrResult(msg => msg.StatusCode == HttpStatusCode.TooManyRequests)
            .WaitAndRetryAsync(
                retryCount: 5,
                sleepDurationProvider: retryAttempt => 
                    TimeSpan.FromSeconds(Math.Pow(2, retryAttempt)) + 
                    TimeSpan.FromMilliseconds(Random.Shared.Next(0, 1000)), // Jitter
                onRetry: (outcome, timespan, retryCount, context) =>
                {
                    var statusCode = outcome.Result?.StatusCode.ToString() ?? "Exception";
                    logger.LogWarning("{ApiName} HTTP retry {RetryCount}/5 after {Delay}ms. Status: {Status}",
                        apiName, retryCount, timespan.TotalMilliseconds, statusCode);
                });
    }

    /// <summary>
    /// Creates a circuit breaker policy
    /// </summary>
    private static IAsyncPolicy<HttpResponseMessage> GetCircuitBreakerPolicy(string apiName, ILogger logger)
    {
        return HttpPolicyExtensions
            .HandleTransientHttpError()
            .CircuitBreakerAsync(
                handledEventsAllowedBeforeBreaking: 3,
                durationOfBreak: TimeSpan.FromMinutes(1),
                onBreak: (result, duration) =>
                {
                    logger.LogError("{ApiName} circuit breaker opened for {Duration}ms. Last error: {Error}",
                        apiName, duration.TotalMilliseconds, 
                        result.Exception?.Message ?? result.Result?.StatusCode.ToString() ?? "Unknown");
                },
                onReset: () =>
                {
                    logger.LogInformation("{ApiName} circuit breaker reset - service recovered", apiName);
                },
                onHalfOpen: () =>
                {
                    logger.LogInformation("{ApiName} circuit breaker half-open - testing service", apiName);
                });
    }

    /// <summary>
    /// Creates a timeout policy
    /// </summary>
    private static IAsyncPolicy<HttpResponseMessage> GetTimeoutPolicy()
    {
        return Policy.TimeoutAsync<HttpResponseMessage>(TimeSpan.FromSeconds(25)); // Slightly less than HttpClient timeout
    }

    /// <summary>
    /// Configures connection pooling settings for optimal performance in high-frequency trading
    /// </summary>
    public static void ConfigureConnectionPooling()
    {
        // Enhanced global connection pool settings for trading applications
        ServicePointManager.DefaultConnectionLimit = 200; // Increased for high-throughput trading
        ServicePointManager.MaxServicePointIdleTime = (int)TimeSpan.FromMinutes(10).TotalMilliseconds; // Longer idle time
        ServicePointManager.UseNagleAlgorithm = false; // Disable for low-latency
        ServicePointManager.Expect100Continue = false; // Disable for faster requests

        // DNS settings for better connectivity and caching
        ServicePointManager.DnsRefreshTimeout = (int)TimeSpan.FromMinutes(5).TotalMilliseconds; // Longer DNS cache
        ServicePointManager.EnableDnsRoundRobin = true; // Enable DNS round-robin for load balancing

        // Security protocol settings
        ServicePointManager.SecurityProtocol = SecurityProtocolType.Tls12 | SecurityProtocolType.Tls13;

        // Additional performance settings
        ServicePointManager.CheckCertificateRevocationList = false; // Disable for performance
        ServicePointManager.MaxServicePoints = 50; // Limit service points for memory efficiency
    }

    /// <summary>
    /// Gets recommended timeout values for different operation types
    /// </summary>
    public static class TimeoutSettings
    {
        public static readonly TimeSpan QuickOperation = TimeSpan.FromSeconds(10);   // Market status, account info
        public static readonly TimeSpan StandardOperation = TimeSpan.FromSeconds(30); // Stock bars, basic queries
        public static readonly TimeSpan LongOperation = TimeSpan.FromSeconds(60);     // Large data downloads
        public static readonly TimeSpan StreamingOperation = TimeSpan.FromMinutes(5); // WebSocket operations
    }

    /// <summary>
    /// Connection health metrics for monitoring
    /// </summary>
    public static class ConnectionMetrics
    {
        public static int GetActiveConnections(string serviceName)
        {
            // This would integrate with actual connection monitoring
            // For now, return a placeholder as ServicePointManager is obsolete
            return 0; // Placeholder - would use HttpClient metrics in production
        }

        public static TimeSpan GetAverageResponseTime(string serviceName)
        {
            // This would integrate with actual metrics collection
            // For now, return a placeholder
            return TimeSpan.FromMilliseconds(100);
        }
    }
}
