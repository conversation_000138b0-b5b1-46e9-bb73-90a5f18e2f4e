# SmaTrendFollower Live Trading Initialization Script
# Safely starts live trading with minimal position sizes and comprehensive monitoring

param(
    [switch]$DryRun = $false,
    [switch]$MinimalPositions = $true,
    [switch]$Force = $false,
    [int]$MonitoringInterval = 30
)

# Color definitions for output
$Colors = @{
    Success = "Green"
    Warning = "Yellow" 
    Error = "Red"
    Info = "Cyan"
    Header = "Magenta"
}

function Write-ColorOutput {
    param([string]$Message, [string]$Color = "White")
    Write-Host $Message -ForegroundColor $Colors[$Color]
}

function Test-Prerequisites {
    Write-ColorOutput "🔍 Checking Prerequisites..." "Header"
    
    $issues = @()
    
    # Check if .NET is available
    try {
        $dotnetVersion = dotnet --version
        Write-ColorOutput "✅ .NET Version: $dotnetVersion" "Success"
    }
    catch {
        $issues += "❌ .NET not found or not accessible"
    }
    
    # Check if project builds
    try {
        Write-ColorOutput "Building project..." "Info"
        $buildResult = dotnet build SmaTrendFollower.Console --verbosity quiet 2>&1
        if ($LASTEXITCODE -eq 0) {
            Write-ColorOutput "✅ Project builds successfully" "Success"
        } else {
            $issues += "❌ Project build failed: $buildResult"
        }
    }
    catch {
        $issues += "❌ Build check failed: $($_.Exception.Message)"
    }
    
    # Check environment configuration
    if (Test-Path ".env") {
        $envContent = Get-Content ".env"
        
        # Check for live environment
        if ($envContent -match "APCA_API_ENV=live") {
            Write-ColorOutput "✅ Live environment configured" "Success"
        } else {
            $issues += "❌ Environment not set to live (APCA_API_ENV=live)"
        }
        
        # Check for required API keys
        if ($envContent -match "APCA_API_KEY_ID=.+") {
            Write-ColorOutput "✅ Alpaca API Key configured" "Success"
        } else {
            $issues += "❌ Alpaca API Key not configured"
        }
        
        # Check safety configuration
        if ($envContent -match "SAFETY_ALLOWED_ENVIRONMENT=Live") {
            Write-ColorOutput "✅ Safety configuration allows live trading" "Success"
        } else {
            $issues += "❌ Safety configuration does not allow live trading"
        }
    } else {
        $issues += "❌ .env file not found"
    }
    
    # Check Redis connectivity
    try {
        $redisTest = redis-cli ping 2>$null
        if ($redisTest -eq "PONG") {
            Write-ColorOutput "✅ Redis server accessible" "Success"
        } else {
            Write-ColorOutput "⚠️ Redis server not responding (optional)" "Warning"
        }
    }
    catch {
        Write-ColorOutput "⚠️ Redis check failed (optional)" "Warning"
    }
    
    return $issues
}

function Test-SystemValidation {
    Write-ColorOutput "🧪 Running System Validation..." "Header"
    
    try {
        if ($DryRun) {
            $validation = & dotnet run --project SmaTrendFollower.Console -- --dry-run validate 2>&1
        } else {
            $validation = & dotnet run --project SmaTrendFollower.Console -- validate 2>&1
        }
        
        if ($validation -match "Trading cycle completed" -or $validation -match "validation passed") {
            Write-ColorOutput "✅ System validation passed" "Success"
            return $true
        } else {
            Write-ColorOutput "❌ System validation failed:" "Error"
            Write-ColorOutput $validation "Error"
            return $false
        }
    }
    catch {
        Write-ColorOutput "❌ System validation error: $($_.Exception.Message)" "Error"
        return $false
    }
}

function Test-AccountStatus {
    Write-ColorOutput "💰 Checking Account Status..." "Header"
    
    try {
        $accountStatus = & dotnet run --project SmaTrendFollower.Console -- account-status 2>&1
        
        if ($accountStatus -match "Equity.*\$(\d+(?:,\d{3})*(?:\.\d{2})?)") {
            $equity = $matches[1] -replace ",", ""
            $equityValue = [decimal]$equity
            
            Write-ColorOutput "Account Equity: $$$equity" "Info"
            
            if ($equityValue -ge 5000) {
                Write-ColorOutput "✅ Account has sufficient equity for live trading" "Success"
                return $true
            } else {
                Write-ColorOutput "❌ Account equity below minimum $5,000 requirement" "Error"
                return $false
            }
        } else {
            Write-ColorOutput "⚠️ Could not parse account equity from response" "Warning"
            Write-ColorOutput $accountStatus "Info"
            return $false
        }
    }
    catch {
        Write-ColorOutput "❌ Account status check failed: $($_.Exception.Message)" "Error"
        return $false
    }
}

function Start-LiveTrading {
    Write-ColorOutput "🚀 Starting Live Trading..." "Header"
    
    if ($MinimalPositions) {
        Write-ColorOutput "Using minimal position sizing for initial validation" "Info"
        
        # Temporarily reduce position limits for initial testing
        $envContent = Get-Content ".env"
        $envContent = $envContent -replace "SAFETY_MAX_POSITIONS=\d+", "SAFETY_MAX_POSITIONS=2"
        $envContent = $envContent -replace "SAFETY_MAX_SINGLE_TRADE_VALUE=\d+", "SAFETY_MAX_SINGLE_TRADE_VALUE=500"
        $envContent | Set-Content ".env.temp"
        
        Write-ColorOutput "Temporarily reduced limits: Max 2 positions, $500 max trade size" "Warning"
    }
    
    try {
        if ($DryRun) {
            Write-ColorOutput "🛡️ DRY RUN MODE - No actual trades will be executed" "Warning"
            $tradingResult = & dotnet run --project SmaTrendFollower.Console -- --dry-run run 2>&1
        } else {
            Write-ColorOutput "💰 LIVE TRADING MODE - Real money will be used" "Warning"
            if (-not $Force) {
                $confirmation = Read-Host "Are you sure you want to start live trading? (yes/no)"
                if ($confirmation -ne "yes") {
                    Write-ColorOutput "Live trading cancelled by user" "Info"
                    return $false
                }
            }
            $tradingResult = & dotnet run --project SmaTrendFollower.Console -- --confirm run 2>&1
        }
        
        Write-ColorOutput "Trading cycle output:" "Info"
        Write-ColorOutput $tradingResult "Info"
        
        if ($tradingResult -match "Trading cycle completed") {
            Write-ColorOutput "✅ Trading cycle completed successfully" "Success"
            return $true
        } else {
            Write-ColorOutput "⚠️ Trading cycle completed with warnings or was blocked" "Warning"
            return $true
        }
    }
    catch {
        Write-ColorOutput "❌ Trading execution failed: $($_.Exception.Message)" "Error"
        return $false
    }
    finally {
        # Restore original configuration if we modified it
        if ($MinimalPositions -and (Test-Path ".env.temp")) {
            Move-Item ".env.temp" ".env" -Force
            Write-ColorOutput "Restored original configuration" "Info"
        }
    }
}

function Start-Monitoring {
    Write-ColorOutput "📊 Starting Monitoring Dashboard..." "Header"
    
    if (Test-Path "monitoring-dashboard.ps1") {
        Write-ColorOutput "Launching monitoring dashboard..." "Info"
        Start-Process powershell -ArgumentList "-File monitoring-dashboard.ps1 -Continuous -RefreshInterval $MonitoringInterval"
        Write-ColorOutput "✅ Monitoring dashboard started" "Success"
    } else {
        Write-ColorOutput "⚠️ Monitoring dashboard script not found" "Warning"
        Write-ColorOutput "Manual monitoring commands:" "Info"
        Write-ColorOutput "  dotnet run -- health" "Info"
        Write-ColorOutput "  dotnet run -- metrics" "Info"
        Write-ColorOutput "  dotnet run -- account-status" "Info"
    }
}

# Main execution
Write-ColorOutput "╔══════════════════════════════════════════════════════════════════════════════════════╗" "Header"
Write-ColorOutput "║                        SmaTrendFollower Live Trading Startup                          ║" "Header"
Write-ColorOutput "║                                $(Get-Date -Format 'yyyy-MM-dd HH:mm:ss')                                ║" "Header"
Write-ColorOutput "╚══════════════════════════════════════════════════════════════════════════════════════╝" "Header"
Write-Host ""

# Display configuration
Write-ColorOutput "Configuration:" "Info"
Write-ColorOutput "  Dry Run Mode: $DryRun" "Info"
Write-ColorOutput "  Minimal Positions: $MinimalPositions" "Info"
Write-ColorOutput "  Force Mode: $Force" "Info"
Write-ColorOutput "  Monitoring Interval: $MonitoringInterval seconds" "Info"
Write-Host ""

# Step 1: Check Prerequisites
$issues = Test-Prerequisites
if ($issues.Count -gt 0) {
    Write-ColorOutput "❌ Prerequisites check failed:" "Error"
    foreach ($issue in $issues) {
        Write-ColorOutput "  $issue" "Error"
    }
    
    if (-not $Force) {
        Write-ColorOutput "Use -Force to override prerequisite checks" "Warning"
        exit 1
    } else {
        Write-ColorOutput "⚠️ Continuing despite prerequisite issues (Force mode)" "Warning"
    }
}
Write-Host ""

# Step 2: System Validation
if (-not (Test-SystemValidation)) {
    Write-ColorOutput "❌ System validation failed" "Error"
    if (-not $Force) {
        exit 1
    } else {
        Write-ColorOutput "⚠️ Continuing despite validation failure (Force mode)" "Warning"
    }
}
Write-Host ""

# Step 3: Account Status Check
if (-not $DryRun -and -not (Test-AccountStatus)) {
    Write-ColorOutput "❌ Account status check failed" "Error"
    if (-not $Force) {
        exit 1
    } else {
        Write-ColorOutput "⚠️ Continuing despite account issues (Force mode)" "Warning"
    }
}
Write-Host ""

# Step 4: Start Trading
if (Start-LiveTrading) {
    Write-ColorOutput "✅ Live trading started successfully" "Success"
    
    # Step 5: Start Monitoring
    Start-Monitoring
    
    Write-Host ""
    Write-ColorOutput "🎯 Live Trading Active" "Success"
    Write-ColorOutput "═══════════════════════════════════════════════════════════════════════════════════════" "Success"
    Write-ColorOutput "Monitor the system using:" "Info"
    Write-ColorOutput "  - Monitoring dashboard (if launched)" "Info"
    Write-ColorOutput "  - Manual commands: dotnet run -- health|metrics|account-status" "Info"
    Write-ColorOutput "  - Emergency stop: dotnet run -- emergency-stop" "Info"
    Write-Host ""
    Write-ColorOutput "Press any key to exit..." "Info"
    $null = $Host.UI.RawUI.ReadKey("NoEcho,IncludeKeyDown")
} else {
    Write-ColorOutput "❌ Failed to start live trading" "Error"
    exit 1
}
