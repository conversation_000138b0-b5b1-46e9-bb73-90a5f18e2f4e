using Microsoft.Extensions.Logging;
using Polly;
using System.Net;

namespace SmaTrendFollower.Services;

/// <summary>
/// Base implementation of IRetryService with configurable retry logic and rate limiting
/// </summary>
public class RetryService : IRetryService, IDisposable
{
    private readonly ILogger<RetryService> _logger;
    private readonly SemaphoreSlim _semaphore;
    private readonly Timer _resetTimer;
    private RetryConfiguration _configuration;
    private RateLimitStatistics _statistics;
    private int _requestCount;
    private DateTime _windowStart;
    private readonly List<string> _recentErrors;
    private readonly object _statsLock = new();

    public RetryService(ILogger<RetryService> logger, RetryConfiguration? configuration = null)
    {
        _logger = logger ?? throw new ArgumentNullException(nameof(logger));
        _configuration = configuration ?? RetryConfiguration.Default;
        _semaphore = new SemaphoreSlim(1, 1);
        _recentErrors = new List<string>();
        _windowStart = DateTime.UtcNow;

        // Set up rate limit window reset timer
        if (_configuration.RateLimit > 0)
        {
            _resetTimer = new Timer(ResetRateLimitWindow, null, _configuration.RateLimitWindow, _configuration.RateLimitWindow);
        }
        else
        {
            _resetTimer = new Timer(_ => { }, null, Timeout.Infinite, Timeout.Infinite);
        }
    }

    public async Task<T> ExecuteAsync<T>(Func<Task<T>> operation, string operationName = "Unknown", CancellationToken cancellationToken = default)
    {
        await _semaphore.WaitAsync(cancellationToken);
        try
        {
            await WaitForRateLimitIfNecessary(cancellationToken);
            IncrementRequestCount();

            _logger.LogDebug("Executing operation: {Operation}", operationName);

            var retryPolicy = CreateRetryPolicy<T>(operationName);
            var startTime = DateTime.UtcNow;

            try
            {
                var result = await retryPolicy.ExecuteAsync(async () =>
                {
                    cancellationToken.ThrowIfCancellationRequested();
                    return await operation();
                });

                RecordSuccess(startTime);
                return result;
            }
            catch (Exception ex)
            {
                RecordFailure(startTime, ex);
                throw;
            }
        }
        finally
        {
            _semaphore.Release();
        }
    }

    public async Task ExecuteAsync(Func<Task> operation, string operationName = "Unknown", CancellationToken cancellationToken = default)
    {
        await ExecuteAsync(async () =>
        {
            await operation();
            return true; // Dummy return value
        }, operationName, cancellationToken);
    }

    public async Task<HttpResponseMessage> ExecuteHttpAsync(Func<Task<HttpResponseMessage>> httpOperation, string operationName = "Unknown", CancellationToken cancellationToken = default)
    {
        await _semaphore.WaitAsync(cancellationToken);
        try
        {
            await WaitForRateLimitIfNecessary(cancellationToken);
            IncrementRequestCount();

            _logger.LogDebug("Executing HTTP operation: {Operation}", operationName);

            var retryPolicy = CreateHttpRetryPolicy(operationName);
            var startTime = DateTime.UtcNow;

            try
            {
                var result = await retryPolicy.ExecuteAsync(async () =>
                {
                    cancellationToken.ThrowIfCancellationRequested();
                    var response = await httpOperation();
                    
                    if (IsRetriableHttpStatusCode(response.StatusCode))
                    {
                        _logger.LogWarning("HTTP operation {Operation} returned retriable status: {Status}", 
                            operationName, response.StatusCode);
                    }
                    
                    return response;
                });

                RecordSuccess(startTime);
                return result;
            }
            catch (Exception ex)
            {
                RecordFailure(startTime, ex);
                throw;
            }
        }
        finally
        {
            _semaphore.Release();
        }
    }

    public RetryConfiguration GetConfiguration() => _configuration;

    public void UpdateConfiguration(RetryConfiguration configuration)
    {
        _configuration = configuration;
        _logger.LogInformation("Retry configuration updated: MaxRetries={MaxRetries}, RateLimit={RateLimit}", 
            configuration.MaxRetries, configuration.RateLimit);
    }

    public RateLimitStatistics GetStatistics()
    {
        lock (_statsLock)
        {
            return _statistics;
        }
    }

    public void ResetStatistics()
    {
        lock (_statsLock)
        {
            _statistics = new RateLimitStatistics(
                TotalRequests: 0,
                SuccessfulRequests: 0,
                FailedRequests: 0,
                RetriedRequests: 0,
                RateLimitedRequests: 0,
                AverageResponseTime: TimeSpan.Zero,
                WindowStart: DateTime.UtcNow,
                CurrentWindowRequests: 0,
                LastRequest: DateTime.MinValue,
                RecentErrors: Array.Empty<string>()
            );
            _recentErrors.Clear();
            _requestCount = 0;
            _windowStart = DateTime.UtcNow;
        }

        _logger.LogInformation("Retry statistics reset");
    }

    private IAsyncPolicy<T> CreateRetryPolicy<T>(string operationName)
    {
        return Policy
            .Handle<Exception>(ex => IsRetriableException(ex))
            .WaitAndRetryAsync(
                retryCount: _configuration.MaxRetries,
                sleepDurationProvider: CalculateDelay,
                onRetry: (exception, timespan, retryCount, context) =>
                {
                    RecordRetry();
                    _logger.LogWarning("Retry {RetryCount}/{MaxRetries} for {Operation} after {Delay}ms. Exception: {Exception}",
                        retryCount, _configuration.MaxRetries, operationName, timespan.TotalMilliseconds, exception.Message);
                })
            .AsAsyncPolicy<T>();
    }

    private IAsyncPolicy<HttpResponseMessage> CreateHttpRetryPolicy(string operationName)
    {
        return Policy
            .HandleResult<HttpResponseMessage>(response => IsRetriableHttpStatusCode(response.StatusCode))
            .Or<HttpRequestException>()
            .Or<TaskCanceledException>()
            .WaitAndRetryAsync(
                retryCount: _configuration.MaxRetries,
                sleepDurationProvider: CalculateDelay,
                onRetry: (outcome, timespan, retryCount, context) =>
                {
                    RecordRetry();
                    var statusCode = outcome.Result?.StatusCode.ToString() ?? outcome.Exception?.Message ?? "Unknown";
                    _logger.LogWarning("HTTP retry {RetryCount}/{MaxRetries} for {Operation} after {Delay}ms. Status: {Status}",
                        retryCount, _configuration.MaxRetries, operationName, timespan.TotalMilliseconds, statusCode);
                });
    }

    private TimeSpan CalculateDelay(int retryAttempt)
    {
        var baseDelay = _configuration.BaseDelay != default 
            ? _configuration.BaseDelay 
            : TimeSpan.FromSeconds(Math.Pow(_configuration.BackoffMultiplier, retryAttempt));

        var jitter = _configuration.JitterRange != default 
            ? TimeSpan.FromMilliseconds(Random.Shared.Next(0, (int)_configuration.JitterRange.TotalMilliseconds))
            : TimeSpan.Zero;

        var totalDelay = baseDelay + jitter;

        return _configuration.MaxDelay != default && totalDelay > _configuration.MaxDelay 
            ? _configuration.MaxDelay 
            : totalDelay;
    }

    private bool IsRetriableException(Exception ex)
    {
        if (_configuration.RetriableExceptionMessages?.Length > 0)
        {
            return _configuration.RetriableExceptionMessages.Any(msg => 
                ex.Message.Contains(msg, StringComparison.OrdinalIgnoreCase));
        }

        // Default retriable exceptions
        return ex is HttpRequestException || 
               ex is TaskCanceledException ||
               ex.Message.Contains("TooManyRequests", StringComparison.OrdinalIgnoreCase) ||
               ex.Message.Contains("429") ||
               ex.Message.Contains("rate limit", StringComparison.OrdinalIgnoreCase);
    }

    private bool IsRetriableHttpStatusCode(HttpStatusCode statusCode)
    {
        if (_configuration.RetriableHttpStatusCodes?.Length > 0)
        {
            return _configuration.RetriableHttpStatusCodes.Contains(statusCode);
        }

        // Default retriable status codes
        return statusCode == HttpStatusCode.TooManyRequests ||
               statusCode == HttpStatusCode.ServiceUnavailable ||
               statusCode == HttpStatusCode.RequestTimeout ||
               statusCode == HttpStatusCode.InternalServerError;
    }

    private async Task WaitForRateLimitIfNecessary(CancellationToken cancellationToken)
    {
        if (_configuration.RateLimit <= 0) return;

        // Check if we're approaching or at the rate limit
        if (_requestCount >= _configuration.RateLimit)
        {
            var delay = TimeSpan.FromMilliseconds(200 + Random.Shared.Next(0, 100));
            _logger.LogDebug("At rate limit ({Count}/{Limit}), adding {Delay}ms delay", 
                _requestCount, _configuration.RateLimit, delay.TotalMilliseconds);
            await Task.Delay(delay, cancellationToken);
        }
        else if (_requestCount >= _configuration.RateLimit * 0.9) // 90% of limit
        {
            var delay = TimeSpan.FromMilliseconds(100 + Random.Shared.Next(0, 50));
            _logger.LogDebug("Approaching rate limit ({Count}/{Limit}), adding {Delay}ms delay", 
                _requestCount, _configuration.RateLimit, delay.TotalMilliseconds);
            await Task.Delay(delay, cancellationToken);
        }
    }

    private void IncrementRequestCount()
    {
        Interlocked.Increment(ref _requestCount);
        
        lock (_statsLock)
        {
            _statistics = _statistics with 
            { 
                TotalRequests = _statistics.TotalRequests + 1,
                CurrentWindowRequests = _requestCount,
                LastRequest = DateTime.UtcNow
            };
        }
    }

    private void RecordSuccess(DateTime startTime)
    {
        var responseTime = DateTime.UtcNow - startTime;
        
        lock (_statsLock)
        {
            var totalRequests = _statistics.TotalRequests;
            var currentAverage = _statistics.AverageResponseTime;
            var newAverage = totalRequests > 1 
                ? TimeSpan.FromTicks((currentAverage.Ticks * (totalRequests - 1) + responseTime.Ticks) / totalRequests)
                : responseTime;

            _statistics = _statistics with 
            { 
                SuccessfulRequests = _statistics.SuccessfulRequests + 1,
                AverageResponseTime = newAverage
            };
        }
    }

    private void RecordFailure(DateTime startTime, Exception ex)
    {
        lock (_statsLock)
        {
            _statistics = _statistics with { FailedRequests = _statistics.FailedRequests + 1 };
            
            _recentErrors.Add($"{DateTime.UtcNow:HH:mm:ss}: {ex.Message}");
            if (_recentErrors.Count > 10) // Keep only last 10 errors
            {
                _recentErrors.RemoveAt(0);
            }
            
            _statistics = _statistics with { RecentErrors = _recentErrors.ToArray() };
        }
    }

    private void RecordRetry()
    {
        lock (_statsLock)
        {
            _statistics = _statistics with { RetriedRequests = _statistics.RetriedRequests + 1 };
        }
    }

    private void ResetRateLimitWindow(object? state)
    {
        var oldCount = Interlocked.Exchange(ref _requestCount, 0);
        _windowStart = DateTime.UtcNow;
        
        lock (_statsLock)
        {
            _statistics = _statistics with 
            { 
                WindowStart = _windowStart,
                CurrentWindowRequests = 0
            };
        }
        
        if (oldCount > 0)
        {
            _logger.LogDebug("Reset rate limit window. Previous window: {Count}/{Limit} requests", 
                oldCount, _configuration.RateLimit);
        }
    }

    public void Dispose()
    {
        _resetTimer?.Dispose();
        _semaphore?.Dispose();
    }
}
