using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Configuration;
using FluentAssertions;
using SmaTrendFollower.Services;
using SmaTrendFollower.Configuration;
using Xunit;

namespace SmaTrendFollower.Tests.Integration;

public class VixFallbackIntegrationTests : IDisposable
{
    private readonly IServiceProvider _serviceProvider;
    private readonly IMarketDataService _marketDataService;

    public VixFallbackIntegrationTests()
    {
        // Load environment variables for real API testing
        DotNetEnv.Env.Load();

        var host = Host.CreateDefaultBuilder()
            .ConfigureServices(services =>
            {
                // Use minimal services for testing
                services.AddMinimalServices();
            })
            .Build();

        _serviceProvider = host.Services;
        _marketDataService = _serviceProvider.GetRequiredService<IMarketDataService>();
    }

    [Fact]
    public async Task GetVixAnalysisAsync_ShouldReturnValidData_WithFallbackStrategy()
    {
        // Skip test if no valid credentials available
        if (!TestConfiguration.ShouldRunIntegrationTests)
        {
            return; // Skip test silently
        }

        // Act
        var vixAnalysis = await _marketDataService.GetVixAnalysisAsync();

        // Assert
        vixAnalysis.Should().NotBeNull();
        vixAnalysis.CurrentVix.Should().BeGreaterThan(0);
        vixAnalysis.CurrentVix.Should().BeLessOrEqualTo(200); // Sanity check
        vixAnalysis.Timestamp.Should().BeCloseTo(DateTime.UtcNow, TimeSpan.FromMinutes(5));

        System.Console.WriteLine($"VIX Analysis Result:");
        System.Console.WriteLine($"  Current VIX: {vixAnalysis.CurrentVix:F2}");
        System.Console.WriteLine($"  VIX SMA30: {vixAnalysis.VixSma30:F2}");
        System.Console.WriteLine($"  Is Above SMA: {vixAnalysis.IsAboveSma}");
        System.Console.WriteLine($"  Is Spike: {vixAnalysis.IsSpike}");
        System.Console.WriteLine($"  Timestamp: {vixAnalysis.Timestamp:yyyy-MM-dd HH:mm:ss} UTC");
    }

    [Fact]
    public async Task IsVixSpikeAsync_ShouldReturnValidResult_WithFallbackStrategy()
    {
        // Skip test if no valid credentials available
        if (!TestConfiguration.ShouldRunIntegrationTests)
        {
            return; // Skip test silently
        }

        // Act
        var isSpike = await _marketDataService.IsVixSpikeAsync(25.0m);

        // Assert
        // Result should be a valid boolean (no exceptions thrown)
        isSpike.Should().Be(isSpike); // Just verify it's a valid boolean

        System.Console.WriteLine($"VIX Spike Check (threshold 25.0): {isSpike}");
    }

    [Fact]
    public async Task VixFallbackService_ShouldProvideWebFallback_WhenPolygonFails()
    {
        // Arrange
        var vixFallbackService = _serviceProvider.GetRequiredService<IVixFallbackService>();

        // Act - Test web fallback directly
        var webVix = await vixFallbackService.GetVixFromWebAsync();

        // Assert
        if (webVix.HasValue)
        {
            webVix.Value.Should().BeGreaterThan(0);
            webVix.Value.Should().BeLessOrEqualTo(200);
            System.Console.WriteLine($"Web VIX fallback successful: {webVix.Value:F2}");
        }
        else
        {
            System.Console.WriteLine("Web VIX fallback returned null (expected during market hours or network issues)");
        }
    }

    [Fact]
    public async Task VixFallbackService_ShouldProvideSyntheticVix_WhenOtherSourcesFail()
    {
        // Arrange
        var vixFallbackService = _serviceProvider.GetRequiredService<IVixFallbackService>();

        // Act - Test synthetic VIX calculation
        var syntheticVix = await vixFallbackService.CalculateSyntheticVixAsync();

        // Assert
        if (syntheticVix.HasValue)
        {
            syntheticVix.Value.Should().BeGreaterThan(0);
            syntheticVix.Value.Should().BeLessOrEqualTo(80);
            System.Console.WriteLine($"Synthetic VIX calculation successful: {syntheticVix.Value:F2}");
        }
        else
        {
            System.Console.WriteLine("Synthetic VIX calculation returned null (expected when VIX ETFs unavailable)");
        }
    }

    [Fact]
    public async Task VixAnalysis_ShouldTrackDataSource_ForDebugging()
    {
        // This test verifies that we can track which data source was used
        // by examining the logs and ensuring the fallback strategy is working

        // Arrange
        var logger = _serviceProvider.GetRequiredService<ILogger<VixFallbackIntegrationTests>>();

        // Act
        logger.LogInformation("Testing VIX fallback strategy...");
        
        var vixAnalysis = await _marketDataService.GetVixAnalysisAsync();
        
        // Assert
        vixAnalysis.Should().NotBeNull();
        vixAnalysis.CurrentVix.Should().BeGreaterThan(0);

        // Log the result for manual verification
        logger.LogInformation("VIX fallback test completed successfully");
        logger.LogInformation("Current VIX: {CurrentVix:F2}", vixAnalysis.CurrentVix);
        logger.LogInformation("Data source can be determined from previous log entries");

        System.Console.WriteLine("Check the logs above to see which VIX data source was used:");
        System.Console.WriteLine("- 'VIX retrieved from Polygon' = Primary source worked");
        System.Console.WriteLine("- 'VIX retrieved from web' = Secondary fallback worked");
        System.Console.WriteLine("- 'VIX calculated synthetically' = Tertiary fallback worked");
        System.Console.WriteLine("- 'using default value' = All sources failed, using conservative default");
    }

    [Fact]
    public async Task VixSpike_ShouldBeDetected_DuringHighVolatilityPeriods()
    {
        // Act
        var vixAnalysis = await _marketDataService.GetVixAnalysisAsync();
        var isSpike25 = await _marketDataService.IsVixSpikeAsync(25.0m);
        var isSpike30 = await _marketDataService.IsVixSpikeAsync(30.0m);
        var isSpike35 = await _marketDataService.IsVixSpikeAsync(35.0m);

        // Assert
        vixAnalysis.Should().NotBeNull();

        System.Console.WriteLine($"VIX Spike Detection Results:");
        System.Console.WriteLine($"  Current VIX: {vixAnalysis.CurrentVix:F2}");
        System.Console.WriteLine($"  Spike > 25: {isSpike25}");
        System.Console.WriteLine($"  Spike > 30: {isSpike30}");
        System.Console.WriteLine($"  Spike > 35: {isSpike35}");
        System.Console.WriteLine($"  Analysis Spike Flag: {vixAnalysis.IsSpike}");

        // Logical consistency checks
        if (vixAnalysis.CurrentVix > 35)
        {
            isSpike35.Should().BeTrue("VIX > 35 should trigger spike detection");
            isSpike30.Should().BeTrue("VIX > 35 should also trigger 30 threshold");
            isSpike25.Should().BeTrue("VIX > 35 should also trigger 25 threshold");
        }
        else if (vixAnalysis.CurrentVix > 30)
        {
            isSpike30.Should().BeTrue("VIX > 30 should trigger spike detection");
            isSpike25.Should().BeTrue("VIX > 30 should also trigger 25 threshold");
        }
        else if (vixAnalysis.CurrentVix > 25)
        {
            isSpike25.Should().BeTrue("VIX > 25 should trigger spike detection");
        }
    }

    public void Dispose()
    {
        if (_serviceProvider is IDisposable disposable)
        {
            disposable.Dispose();
        }
    }
}
