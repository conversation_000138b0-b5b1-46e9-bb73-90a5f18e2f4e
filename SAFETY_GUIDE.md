# SmaTrendFollower Safety Guide

## 🛡️ Comprehensive Safety System

The SmaTrendFollower trading system includes multiple layers of safety protection to prevent unintended trades and limit risk exposure.

## Safety Features

### 1. Multi-Layer Safety Validation
- **Trading Cycle Validation**: Checks before any trading begins
- **Individual Trade Validation**: Validates each trade before execution
- **Environment Protection**: Prevents accidental live trading
- **Real-time Monitoring**: Continuous safety checks during operation

### 2. Risk Management Controls
- **Daily Loss Limits**: Maximum daily loss protection
- **Position Size Limits**: Maximum position size as % of equity
- **Trade Value Limits**: Maximum single trade value
- **Position Count Limits**: Maximum number of open positions
- **Account Equity Minimums**: Minimum account balance required

### 3. Trading Environment Protection
- **Paper Trading Only**: Restrict to paper trading environment
- **Live Trading Only**: Restrict to live trading environment  
- **Environment Auto-Detection**: Automatically detects trading environment
- **Confirmation Requirements**: Require manual confirmation for live trades

### 4. Operational Safety
- **Dry Run Mode**: Execute strategy without placing actual trades
- **Daily Trade Limits**: Maximum number of trades per day
- **Rate Limiting**: Prevents API abuse and overtrading
- **Error Handling**: Graceful handling of API failures

## Command Line Safety Controls

### Basic Safety Commands
```bash
# Dry run mode - no actual trades executed
dotnet run --dry-run

# Show current safety configuration
dotnet run --show-safety

# Paper trading only
dotnet run --paper-only

# Live trading with confirmation
dotnet run --live-only --confirm

# Check account status
dotnet run --check-account
```

### Safety Configuration Examples
```bash
# Conservative live trading
dotnet run --live-only --confirm

# Paper trading with relaxed limits
dotnet run --paper-only

# Test mode - see what would happen
dotnet run --dry-run --show-safety
```

## Environment Variables

Configure safety settings via environment variables:

```bash
# Risk limits
SAFETY_MAX_DAILY_LOSS=500
SAFETY_MAX_POSITION_SIZE_PERCENT=0.05
SAFETY_MAX_POSITIONS=10
SAFETY_MAX_DAILY_TRADES=20
SAFETY_MIN_ACCOUNT_EQUITY=1000
SAFETY_MAX_SINGLE_TRADE_VALUE=2000

# Operational controls
SAFETY_REQUIRE_CONFIRMATION=true
SAFETY_DRY_RUN_MODE=false
SAFETY_ALLOWED_ENVIRONMENT=Paper
```

## Safety Configuration Presets

### Safe Defaults (Production)
- Max Daily Loss: $500
- Max Position Size: 5% of equity
- Max Positions: 10
- Max Daily Trades: 20
- Min Account Equity: $1,000
- Max Single Trade: $2,000
- Require Confirmation: Yes
- Environment: Paper Only

### Paper Trading Configuration
- Max Daily Loss: $1,000
- Max Position Size: 10% of equity
- Max Positions: 20
- Max Daily Trades: 50
- Min Account Equity: $100
- Max Single Trade: $5,000
- Require Confirmation: No
- Environment: Paper Only

### Live Trading Configuration
- Max Daily Loss: $250 (more conservative)
- Max Position Size: 3% of equity
- Max Positions: 8
- Max Daily Trades: 15
- Min Account Equity: $5,000
- Max Single Trade: $1,500
- Require Confirmation: Yes
- Environment: Live Only

## Safety Validation Process

### Before Trading Cycle
1. ✅ Environment validation (paper vs live)
2. ✅ Account equity check
3. ✅ Daily loss limit check
4. ✅ System health verification

### Before Each Trade
1. ✅ Dry run mode check
2. ✅ Environment validation
3. ✅ Account equity verification
4. ✅ Position count limits
5. ✅ Daily trade count limits
6. ✅ Single trade value limits
7. ✅ Position size percentage limits
8. ✅ Daily loss limits
9. ✅ Live trading confirmation

### Safety Levels
- **Info**: Informational messages
- **Warning**: Potential issues, trade may be blocked
- **Error**: Serious issues, trade blocked
- **Critical**: Critical safety violations, trading stopped

## Emergency Procedures

### Immediate Stop Trading
```bash
# Kill any running process
Ctrl+C

# Check current positions
dotnet run --check-account
```

### Reset Daily Counters
Daily counters reset automatically at midnight UTC. No manual reset required.

### Override Safety (Emergency Only)
Safety overrides should only be used in emergencies and with extreme caution:

```bash
# Disable confirmation requirement (use with caution)
SAFETY_REQUIRE_CONFIRMATION=false dotnet run --live-only
```

## Monitoring and Logging

### Safety Events Logged
- All safety validation results
- Blocked trades with reasons
- Configuration changes
- Daily counter resets
- Environment detection

### Log Levels
- **Critical**: Safety violations that stop trading
- **Error**: Failed safety checks
- **Warning**: Potential safety issues
- **Info**: Normal safety operations

## Best Practices

### Development/Testing
1. Always start with `--dry-run` mode
2. Use `--paper-only` for strategy testing
3. Verify safety configuration with `--show-safety`
4. Test with small position sizes first

### Production Deployment
1. Start with paper trading to validate
2. Use conservative safety limits initially
3. Gradually increase limits as confidence grows
4. Monitor logs for safety violations
5. Regular safety configuration reviews

### Risk Management
1. Never disable all safety features
2. Set daily loss limits appropriate for account size
3. Use position size limits to prevent concentration risk
4. Monitor daily trade counts to prevent overtrading
5. Regular account equity checks

## Troubleshooting

### Common Safety Blocks
- **"Dry run mode enabled"**: Remove `--dry-run` flag
- **"Live trading not allowed"**: Use `--confirm` flag or change environment
- **"Account equity below minimum"**: Increase account balance or lower minimum
- **"Maximum positions reached"**: Close some positions or increase limit
- **"Daily trade limit reached"**: Wait until next day or increase limit

### Configuration Issues
- Check environment variables are set correctly
- Verify numeric values are positive
- Ensure percentage values are between 0 and 1
- Validate environment enum values (Paper, Live, Both)

## Support

For safety-related issues:
1. Check logs for detailed error messages
2. Verify safety configuration with `--show-safety`
3. Test with `--dry-run` mode first
4. Review this safety guide
5. Contact support with log details if needed

Remember: **Safety features are designed to protect your capital. Never disable them unless you fully understand the risks.**
