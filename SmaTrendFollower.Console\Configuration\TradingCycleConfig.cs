using Microsoft.Extensions.Configuration;

namespace SmaTrendFollower.Configuration;

/// <summary>
/// Configuration for trading cycle intervals with dynamic VIX-based adjustments
/// </summary>
public class TradingCycleConfig
{
    /// <summary>
    /// Default cycle interval in minutes (used when no VIX data available)
    /// </summary>
    public int DefaultIntervalMinutes { get; set; } = 5;

    /// <summary>
    /// Cycle interval during high volatility periods (VIX > HighVolatilityThreshold)
    /// </summary>
    public int HighVolatilityIntervalMinutes { get; set; } = 2;

    /// <summary>
    /// Cycle interval during normal volatility periods
    /// </summary>
    public int NormalVolatilityIntervalMinutes { get; set; } = 5;

    /// <summary>
    /// Cycle interval during low volatility periods (VIX < LowVolatilityThreshold)
    /// </summary>
    public int LowVolatilityIntervalMinutes { get; set; } = 10;

    /// <summary>
    /// Cycle interval during pre/post market hours
    /// </summary>
    public int ExtendedHoursIntervalMinutes { get; set; } = 15;

    /// <summary>
    /// Cycle interval during overnight hours (market closed)
    /// </summary>
    public int OvernightIntervalMinutes { get; set; } = 30;

    /// <summary>
    /// VIX threshold above which to use high volatility interval
    /// </summary>
    public decimal HighVolatilityThreshold { get; set; } = 25.0m;

    /// <summary>
    /// VIX threshold below which to use low volatility interval
    /// </summary>
    public decimal LowVolatilityThreshold { get; set; } = 15.0m;

    /// <summary>
    /// Whether to enable dynamic VIX-based interval adjustment
    /// </summary>
    public bool EnableVixBasedAdjustment { get; set; } = true;

    /// <summary>
    /// Whether to enable different intervals for extended hours
    /// </summary>
    public bool EnableExtendedHoursAdjustment { get; set; } = true;

    /// <summary>
    /// Minimum allowed cycle interval in minutes (safety limit)
    /// </summary>
    public int MinimumIntervalMinutes { get; set; } = 1;

    /// <summary>
    /// Maximum allowed cycle interval in minutes (safety limit)
    /// </summary>
    public int MaximumIntervalMinutes { get; set; } = 60;

    /// <summary>
    /// Command line override for cycle interval (takes precedence over all other settings)
    /// </summary>
    public int? CommandLineOverrideMinutes { get; set; }

    /// <summary>
    /// Creates a TradingCycleConfig from IConfiguration
    /// </summary>
    public static TradingCycleConfig FromConfiguration(IConfiguration configuration)
    {
        var config = new TradingCycleConfig();

        // Load from environment variables with fallback to defaults
        config.DefaultIntervalMinutes = configuration.GetValue("TRADING_CYCLE_INTERVAL_MINUTES", config.DefaultIntervalMinutes);
        config.HighVolatilityIntervalMinutes = configuration.GetValue("TRADING_CYCLE_HIGH_VIX_MINUTES", config.HighVolatilityIntervalMinutes);
        config.NormalVolatilityIntervalMinutes = configuration.GetValue("TRADING_CYCLE_NORMAL_VIX_MINUTES", config.NormalVolatilityIntervalMinutes);
        config.LowVolatilityIntervalMinutes = configuration.GetValue("TRADING_CYCLE_LOW_VIX_MINUTES", config.LowVolatilityIntervalMinutes);
        config.ExtendedHoursIntervalMinutes = configuration.GetValue("TRADING_CYCLE_EXTENDED_HOURS_MINUTES", config.ExtendedHoursIntervalMinutes);
        config.OvernightIntervalMinutes = configuration.GetValue("TRADING_CYCLE_OVERNIGHT_MINUTES", config.OvernightIntervalMinutes);
        
        config.HighVolatilityThreshold = configuration.GetValue("TRADING_CYCLE_HIGH_VIX_THRESHOLD", config.HighVolatilityThreshold);
        config.LowVolatilityThreshold = configuration.GetValue("TRADING_CYCLE_LOW_VIX_THRESHOLD", config.LowVolatilityThreshold);
        
        config.EnableVixBasedAdjustment = configuration.GetValue("TRADING_CYCLE_ENABLE_VIX_ADJUSTMENT", config.EnableVixBasedAdjustment);
        config.EnableExtendedHoursAdjustment = configuration.GetValue("TRADING_CYCLE_ENABLE_EXTENDED_HOURS_ADJUSTMENT", config.EnableExtendedHoursAdjustment);
        
        config.MinimumIntervalMinutes = configuration.GetValue("TRADING_CYCLE_MIN_INTERVAL_MINUTES", config.MinimumIntervalMinutes);
        config.MaximumIntervalMinutes = configuration.GetValue("TRADING_CYCLE_MAX_INTERVAL_MINUTES", config.MaximumIntervalMinutes);

        // Validate configuration
        config.ValidateAndAdjust();

        return config;
    }

    /// <summary>
    /// Validates and adjusts configuration values to ensure they are within safe bounds
    /// </summary>
    public void ValidateAndAdjust()
    {
        // Ensure minimum and maximum bounds are reasonable
        MinimumIntervalMinutes = Math.Max(1, MinimumIntervalMinutes);
        MaximumIntervalMinutes = Math.Min(120, Math.Max(MinimumIntervalMinutes + 1, MaximumIntervalMinutes));

        // Clamp all interval values to be within bounds
        DefaultIntervalMinutes = Math.Clamp(DefaultIntervalMinutes, MinimumIntervalMinutes, MaximumIntervalMinutes);
        HighVolatilityIntervalMinutes = Math.Clamp(HighVolatilityIntervalMinutes, MinimumIntervalMinutes, MaximumIntervalMinutes);
        NormalVolatilityIntervalMinutes = Math.Clamp(NormalVolatilityIntervalMinutes, MinimumIntervalMinutes, MaximumIntervalMinutes);
        LowVolatilityIntervalMinutes = Math.Clamp(LowVolatilityIntervalMinutes, MinimumIntervalMinutes, MaximumIntervalMinutes);
        ExtendedHoursIntervalMinutes = Math.Clamp(ExtendedHoursIntervalMinutes, MinimumIntervalMinutes, MaximumIntervalMinutes);
        OvernightIntervalMinutes = Math.Clamp(OvernightIntervalMinutes, MinimumIntervalMinutes, MaximumIntervalMinutes);

        // Ensure VIX thresholds are reasonable
        HighVolatilityThreshold = Math.Max(15.0m, HighVolatilityThreshold);
        LowVolatilityThreshold = Math.Min(HighVolatilityThreshold - 1.0m, Math.Max(10.0m, LowVolatilityThreshold));

        // Apply command line override if specified
        if (CommandLineOverrideMinutes.HasValue)
        {
            var overrideValue = Math.Clamp(CommandLineOverrideMinutes.Value, MinimumIntervalMinutes, MaximumIntervalMinutes);
            DefaultIntervalMinutes = overrideValue;
            HighVolatilityIntervalMinutes = overrideValue;
            NormalVolatilityIntervalMinutes = overrideValue;
            LowVolatilityIntervalMinutes = overrideValue;
            ExtendedHoursIntervalMinutes = overrideValue;
            // Don't override overnight interval to allow for longer waits when market is closed
        }
    }

    /// <summary>
    /// Gets the appropriate cycle interval based on current market conditions
    /// </summary>
    public TimeSpan GetCycleInterval(decimal? currentVix = null, bool isMarketOpen = true, bool isExtendedHours = false)
    {
        int intervalMinutes;

        // Command line override takes precedence
        if (CommandLineOverrideMinutes.HasValue)
        {
            intervalMinutes = CommandLineOverrideMinutes.Value;
        }
        // Market closed - use overnight interval
        else if (!isMarketOpen)
        {
            intervalMinutes = OvernightIntervalMinutes;
        }
        // Extended hours (pre/post market)
        else if (isExtendedHours && EnableExtendedHoursAdjustment)
        {
            intervalMinutes = ExtendedHoursIntervalMinutes;
        }
        // VIX-based adjustment during regular market hours
        else if (currentVix.HasValue && EnableVixBasedAdjustment)
        {
            intervalMinutes = currentVix.Value switch
            {
                var vix when vix >= HighVolatilityThreshold => HighVolatilityIntervalMinutes,
                var vix when vix <= LowVolatilityThreshold => LowVolatilityIntervalMinutes,
                _ => NormalVolatilityIntervalMinutes
            };
        }
        // Default interval
        else
        {
            intervalMinutes = DefaultIntervalMinutes;
        }

        // Ensure the interval is within bounds
        intervalMinutes = Math.Clamp(intervalMinutes, MinimumIntervalMinutes, MaximumIntervalMinutes);

        return TimeSpan.FromMinutes(intervalMinutes);
    }

    /// <summary>
    /// Gets a description of why a particular interval was chosen
    /// </summary>
    public string GetIntervalReason(decimal? currentVix = null, bool isMarketOpen = true, bool isExtendedHours = false)
    {
        if (CommandLineOverrideMinutes.HasValue)
        {
            return $"Command line override: {CommandLineOverrideMinutes.Value} minutes";
        }

        if (!isMarketOpen)
        {
            return $"Market closed: {OvernightIntervalMinutes} minutes";
        }

        if (isExtendedHours && EnableExtendedHoursAdjustment)
        {
            return $"Extended hours: {ExtendedHoursIntervalMinutes} minutes";
        }

        if (currentVix.HasValue && EnableVixBasedAdjustment)
        {
            return currentVix.Value switch
            {
                var vix when vix >= HighVolatilityThreshold => $"High volatility (VIX {vix:F1} >= {HighVolatilityThreshold}): {HighVolatilityIntervalMinutes} minutes",
                var vix when vix <= LowVolatilityThreshold => $"Low volatility (VIX {vix:F1} <= {LowVolatilityThreshold}): {LowVolatilityIntervalMinutes} minutes",
                var vix => $"Normal volatility (VIX {vix:F1}): {NormalVolatilityIntervalMinutes} minutes"
            };
        }

        return $"Default interval: {DefaultIntervalMinutes} minutes";
    }
}
