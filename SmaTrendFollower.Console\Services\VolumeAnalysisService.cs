using Microsoft.Extensions.Logging;
using Alpaca.Markets;

namespace SmaTrendFollower.Services;

/// <summary>
/// Volume pattern types
/// </summary>
public enum VolumePattern
{
    Normal,
    Accumulation,
    Distribution,
    Breakout,
    Exhaustion,
    Climax
}

/// <summary>
/// Interface for volume analysis and pattern recognition
/// </summary>
public interface IVolumeAnalysisService
{
    /// <summary>
    /// Analyzes volume patterns for confirmation signals
    /// </summary>
    VolumeAnalysisResult AnalyzeVolume(IEnumerable<IBar> bars);

    /// <summary>
    /// Calculates volume-based momentum indicators
    /// </summary>
    VolumeMomentumIndicators CalculateVolumeMomentum(IEnumerable<IBar> bars);

    /// <summary>
    /// Detects volume anomalies and unusual activity
    /// </summary>
    VolumeAnomalyResult DetectVolumeAnomalies(IEnumerable<IBar> bars);

    /// <summary>
    /// Analyzes volume distribution throughout trading day
    /// </summary>
    VolumeDistributionAnalysis AnalyzeVolumeDistribution(IEnumerable<IBar> bars);

    /// <summary>
    /// Calculates volume-weighted average price (VWAP) levels
    /// </summary>
    VWAPAnalysis CalculateVWAP(IEnumerable<IBar> bars);
}

/// <summary>
/// Volume analysis service for pattern recognition and confirmation
/// </summary>
public sealed class VolumeAnalysisService : IVolumeAnalysisService
{
    private readonly ILogger<VolumeAnalysisService> _logger;

    public VolumeAnalysisService(ILogger<VolumeAnalysisService> logger)
    {
        _logger = logger;
    }

    public VolumeAnalysisResult AnalyzeVolume(IEnumerable<IBar> bars)
    {
        var barsList = bars.ToList();
        _logger.LogInformation("Analyzing volume patterns for {Count} bars", barsList.Count);

        try
        {
            if (barsList.Count < 20)
            {
                _logger.LogWarning("Insufficient data for volume analysis");
                return new VolumeAnalysisResult(VolumePattern.Normal, 1.0m, false, DateTime.UtcNow);
            }

            var volumes = barsList.Select(b => (decimal)b.Volume).ToArray();
            var prices = barsList.Select(b => b.Close).ToArray();
            
            var pattern = DetectVolumePattern(volumes, prices);
            var volumeRatio = CalculateVolumeRatio(volumes);
            var isConfirming = DetermineConfirmation(pattern, volumeRatio, prices);

            var result = new VolumeAnalysisResult(pattern, volumeRatio, isConfirming, DateTime.UtcNow);

            _logger.LogInformation("Volume analysis completed. Pattern: {Pattern}, Ratio: {Ratio:F2}, Confirming: {Confirming}",
                pattern, volumeRatio, isConfirming);

            return result;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Volume analysis failed");
            throw;
        }
    }

    public VolumeMomentumIndicators CalculateVolumeMomentum(IEnumerable<IBar> bars)
    {
        var barsList = bars.ToList();
        _logger.LogInformation("Calculating volume momentum indicators for {Count} bars", barsList.Count);

        try
        {
            if (barsList.Count < 14)
            {
                return new VolumeMomentumIndicators(0, 0, 0, 0, DateTime.UtcNow);
            }

            var volumes = barsList.Select(b => (decimal)b.Volume).ToArray();
            var prices = barsList.Select(b => b.Close).ToArray();

            var obv = CalculateOnBalanceVolume(prices, volumes);
            var vpt = CalculateVolumePriceTrend(prices, volumes);
            var mfi = CalculateMoneyFlowIndex(barsList.TakeLast(14));
            var ad = CalculateAccumulationDistribution(barsList);

            var indicators = new VolumeMomentumIndicators(obv, vpt, mfi, ad, DateTime.UtcNow);

            _logger.LogInformation("Volume momentum calculated. OBV: {OBV:F0}, MFI: {MFI:F1}", obv, mfi);

            return indicators;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Volume momentum calculation failed");
            throw;
        }
    }

    public VolumeAnomalyResult DetectVolumeAnomalies(IEnumerable<IBar> bars)
    {
        var barsList = bars.ToList();
        _logger.LogInformation("Detecting volume anomalies for {Count} bars", barsList.Count);

        try
        {
            if (barsList.Count < 20)
            {
                return new VolumeAnomalyResult(false, 1.0m, "Insufficient data", DateTime.UtcNow);
            }

            var volumes = barsList.Select(b => (decimal)b.Volume).ToArray();
            var recentVolume = volumes.Last();
            var averageVolume = volumes.Take(volumes.Length - 1).Average();
            var volumeStdDev = CalculateStandardDeviation(volumes.Take(volumes.Length - 1));

            var volumeRatio = recentVolume / averageVolume;
            var zScore = (recentVolume - averageVolume) / volumeStdDev;

            var isAnomaly = Math.Abs(zScore) > 2.0m; // 2 standard deviations
            var description = GenerateAnomalyDescription(volumeRatio, zScore);

            var result = new VolumeAnomalyResult(isAnomaly, volumeRatio, description, DateTime.UtcNow);

            _logger.LogInformation("Volume anomaly detection completed. Anomaly: {Anomaly}, Ratio: {Ratio:F2}",
                isAnomaly, volumeRatio);

            return result;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Volume anomaly detection failed");
            throw;
        }
    }

    public VolumeDistributionAnalysis AnalyzeVolumeDistribution(IEnumerable<IBar> bars)
    {
        var barsList = bars.ToList();
        _logger.LogInformation("Analyzing volume distribution for {Count} bars", barsList.Count);

        try
        {
            if (barsList.Count < 10)
            {
                return new VolumeDistributionAnalysis("Insufficient data", 0, 0, 0, DateTime.UtcNow);
            }

            var volumes = barsList.Select(b => (decimal)b.Volume).ToArray();
            var totalVolume = volumes.Sum();
            
            // Analyze distribution by time periods (simplified)
            var morningVolume = volumes.Take(volumes.Length / 3).Sum();
            var middayVolume = volumes.Skip(volumes.Length / 3).Take(volumes.Length / 3).Sum();
            var afternoonVolume = volumes.Skip(2 * volumes.Length / 3).Sum();

            var morningPct = morningVolume / totalVolume * 100;
            var middayPct = middayVolume / totalVolume * 100;
            var afternoonPct = afternoonVolume / totalVolume * 100;

            var pattern = DetermineDistributionPattern(morningPct, middayPct, afternoonPct);

            var analysis = new VolumeDistributionAnalysis(
                pattern, 
                morningPct, 
                middayPct, 
                afternoonPct, 
                DateTime.UtcNow
            );

            _logger.LogInformation("Volume distribution analyzed. Pattern: {Pattern}", pattern);

            return analysis;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Volume distribution analysis failed");
            throw;
        }
    }

    public VWAPAnalysis CalculateVWAP(IEnumerable<IBar> bars)
    {
        var barsList = bars.ToList();
        _logger.LogInformation("Calculating VWAP for {Count} bars", barsList.Count);

        try
        {
            if (barsList.Count == 0)
            {
                return new VWAPAnalysis(0, 0, 0, DateTime.UtcNow);
            }

            decimal cumulativeVolumePrice = 0;
            decimal cumulativeVolume = 0;

            foreach (var bar in barsList)
            {
                var typicalPrice = (bar.High + bar.Low + bar.Close) / 3;
                cumulativeVolumePrice += typicalPrice * (decimal)bar.Volume;
                cumulativeVolume += (decimal)bar.Volume;
            }

            var vwap = cumulativeVolume > 0 ? cumulativeVolumePrice / cumulativeVolume : 0;
            var currentPrice = barsList.Last().Close;
            var deviation = (currentPrice - vwap) / vwap * 100;

            var analysis = new VWAPAnalysis(vwap, currentPrice, deviation, DateTime.UtcNow);

            _logger.LogInformation("VWAP calculated: {VWAP:F2}, Current: {Current:F2}, Deviation: {Dev:F2}%",
                vwap, currentPrice, deviation);

            return analysis;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "VWAP calculation failed");
            throw;
        }
    }

    private VolumePattern DetectVolumePattern(decimal[] volumes, decimal[] prices)
    {
        if (volumes.Length < 10) return VolumePattern.Normal;

        var recentVolumes = volumes.TakeLast(5).ToArray();
        var previousVolumes = volumes.Take(volumes.Length - 5).ToArray();
        var recentPrices = prices.TakeLast(5).ToArray();

        var avgRecentVolume = recentVolumes.Average();
        var avgPreviousVolume = previousVolumes.Average();
        var volumeIncrease = avgRecentVolume / avgPreviousVolume;

        var priceChange = (recentPrices.Last() - recentPrices.First()) / recentPrices.First();

        // Pattern detection logic
        if (volumeIncrease > 2.0m && Math.Abs(priceChange) > 0.05m)
            return VolumePattern.Breakout;
        
        if (volumeIncrease > 3.0m && Math.Abs(priceChange) < 0.02m)
            return VolumePattern.Climax;
        
        if (volumeIncrease > 1.5m && priceChange > 0.02m)
            return VolumePattern.Accumulation;
        
        if (volumeIncrease > 1.5m && priceChange < -0.02m)
            return VolumePattern.Distribution;
        
        if (volumeIncrease < 0.5m)
            return VolumePattern.Exhaustion;

        return VolumePattern.Normal;
    }

    private decimal CalculateVolumeRatio(decimal[] volumes)
    {
        if (volumes.Length < 2) return 1.0m;
        
        var recentVolume = volumes.TakeLast(5).Average();
        var historicalVolume = volumes.Take(volumes.Length - 5).Average();
        
        return historicalVolume > 0 ? recentVolume / historicalVolume : 1.0m;
    }

    private bool DetermineConfirmation(VolumePattern pattern, decimal volumeRatio, decimal[] prices)
    {
        var priceChange = prices.Length > 1 ? (prices.Last() - prices.First()) / prices.First() : 0;
        
        return pattern switch
        {
            VolumePattern.Accumulation => priceChange > 0 && volumeRatio > 1.2m,
            VolumePattern.Distribution => priceChange < 0 && volumeRatio > 1.2m,
            VolumePattern.Breakout => Math.Abs(priceChange) > 0.03m && volumeRatio > 1.5m,
            VolumePattern.Climax => volumeRatio > 2.0m,
            _ => volumeRatio > 1.0m
        };
    }

    private decimal CalculateOnBalanceVolume(decimal[] prices, decimal[] volumes)
    {
        decimal obv = 0;
        
        for (int i = 1; i < prices.Length; i++)
        {
            if (prices[i] > prices[i - 1])
                obv += volumes[i];
            else if (prices[i] < prices[i - 1])
                obv -= volumes[i];
        }
        
        return obv;
    }

    private decimal CalculateVolumePriceTrend(decimal[] prices, decimal[] volumes)
    {
        decimal vpt = 0;
        
        for (int i = 1; i < prices.Length; i++)
        {
            var priceChange = (prices[i] - prices[i - 1]) / prices[i - 1];
            vpt += priceChange * volumes[i];
        }
        
        return vpt;
    }

    private decimal CalculateMoneyFlowIndex(IEnumerable<IBar> bars)
    {
        var barsList = bars.ToList();
        if (barsList.Count < 14) return 50;

        decimal positiveFlow = 0;
        decimal negativeFlow = 0;

        for (int i = 1; i < barsList.Count; i++)
        {
            var currentTypical = (barsList[i].High + barsList[i].Low + barsList[i].Close) / 3;
            var previousTypical = (barsList[i-1].High + barsList[i-1].Low + barsList[i-1].Close) / 3;
            var rawMoneyFlow = currentTypical * (decimal)barsList[i].Volume;

            if (currentTypical > previousTypical)
                positiveFlow += rawMoneyFlow;
            else if (currentTypical < previousTypical)
                negativeFlow += rawMoneyFlow;
        }

        if (negativeFlow == 0) return 100;
        
        var moneyRatio = positiveFlow / negativeFlow;
        return 100 - (100 / (1 + moneyRatio));
    }

    private decimal CalculateAccumulationDistribution(IEnumerable<IBar> bars)
    {
        decimal ad = 0;
        
        foreach (var bar in bars)
        {
            var clv = ((bar.Close - bar.Low) - (bar.High - bar.Close)) / (bar.High - bar.Low);
            if (bar.High == bar.Low) clv = 0; // Avoid division by zero
            ad += clv * (decimal)bar.Volume;
        }
        
        return ad;
    }

    private decimal CalculateStandardDeviation(IEnumerable<decimal> values)
    {
        var valuesList = values.ToList();
        if (valuesList.Count < 2) return 0;
        
        var average = valuesList.Average();
        var sumOfSquares = valuesList.Sum(v => (v - average) * (v - average));
        
        return (decimal)Math.Sqrt((double)(sumOfSquares / (valuesList.Count - 1)));
    }

    private string GenerateAnomalyDescription(decimal volumeRatio, decimal zScore)
    {
        if (zScore > 3) return "Extremely high volume spike";
        if (zScore > 2) return "Significant volume increase";
        if (zScore < -2) return "Unusually low volume";
        if (volumeRatio > 2) return "Above average volume";
        if (volumeRatio < 0.5m) return "Below average volume";
        
        return "Normal volume activity";
    }

    private string DetermineDistributionPattern(decimal morning, decimal midday, decimal afternoon)
    {
        if (morning > 40 && afternoon > 30) return "U-Shaped";
        if (midday > 50) return "Midday Peak";
        if (morning > 50) return "Morning Heavy";
        if (afternoon > 50) return "Afternoon Heavy";
        
        return "Balanced";
    }
}

/// <summary>
/// Volume analysis result
/// </summary>
public record VolumeAnalysisResult(
    VolumePattern Pattern,
    decimal VolumeRatio,
    bool IsConfirming,
    DateTime AnalyzedAt
);

/// <summary>
/// Volume momentum indicators
/// </summary>
public record VolumeMomentumIndicators(
    decimal OnBalanceVolume,
    decimal VolumePriceTrend,
    decimal MoneyFlowIndex,
    decimal AccumulationDistribution,
    DateTime CalculatedAt
);

/// <summary>
/// Volume anomaly detection result
/// </summary>
public record VolumeAnomalyResult(
    bool IsAnomaly,
    decimal VolumeRatio,
    string Description,
    DateTime DetectedAt
);

/// <summary>
/// Volume distribution analysis
/// </summary>
public record VolumeDistributionAnalysis(
    string Pattern,
    decimal MorningPercentage,
    decimal MiddayPercentage,
    decimal AfternoonPercentage,
    DateTime AnalyzedAt
);

/// <summary>
/// VWAP analysis result
/// </summary>
public record VWAPAnalysis(
    decimal VWAP,
    decimal CurrentPrice,
    decimal DeviationPercentage,
    DateTime CalculatedAt
);
