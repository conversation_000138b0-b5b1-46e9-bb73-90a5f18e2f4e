using System.Text.Json;

namespace SmaTrendFollower.Services;

/// <summary>
/// Interface for Polygon WebSocket client for real-time streaming data
/// </summary>
public interface IPolygonWebSocketClient : IDisposable
{
    // === Events ===
    
    /// <summary>
    /// Fired when connection status changes
    /// </summary>
    event EventHandler<PolygonConnectionStatusEventArgs>? ConnectionStatusChanged;
    
    /// <summary>
    /// Fired when an index value update is received
    /// </summary>
    event EventHandler<PolygonIndexUpdateEventArgs>? IndexUpdated;
    
    /// <summary>
    /// Fired when an error occurs
    /// </summary>
    event EventHandler<PolygonErrorEventArgs>? ErrorOccurred;
    
    // === Properties ===
    
    /// <summary>
    /// Current connection status
    /// </summary>
    PolygonConnectionStatus ConnectionStatus { get; }
    
    // === Connection Management ===
    
    /// <summary>
    /// Connects to Polygon WebSocket stream
    /// </summary>
    Task ConnectAsync(CancellationToken cancellationToken = default);
    
    /// <summary>
    /// Disconnects from Polygon WebSocket stream
    /// </summary>
    Task DisconnectAsync(CancellationToken cancellationToken = default);
    
    // === Subscription Management ===
    
    /// <summary>
    /// Subscribe to index updates (e.g., I:VIX, I:SPX)
    /// </summary>
    Task SubscribeToIndexUpdatesAsync(IEnumerable<string> indexSymbols, CancellationToken cancellationToken = default);
    
    /// <summary>
    /// Unsubscribe from index updates
    /// </summary>
    Task UnsubscribeFromIndexUpdatesAsync(IEnumerable<string> indexSymbols, CancellationToken cancellationToken = default);
    
    /// <summary>
    /// Unsubscribe from all subscriptions
    /// </summary>
    Task UnsubscribeAllAsync(CancellationToken cancellationToken = default);
}

/// <summary>
/// Connection status for Polygon WebSocket
/// </summary>
public enum PolygonConnectionStatus
{
    Disconnected,
    Connecting,
    Connected,
    Authenticating,
    Authenticated,
    Reconnecting,
    Error
}

/// <summary>
/// Event args for Polygon connection status changes
/// </summary>
public class PolygonConnectionStatusEventArgs : EventArgs
{
    public PolygonConnectionStatus Status { get; init; }
    public string? Message { get; init; }
    public Exception? Exception { get; init; }
}

/// <summary>
/// Event args for Polygon index updates
/// </summary>
public class PolygonIndexUpdateEventArgs : EventArgs
{
    public string IndexSymbol { get; init; } = "";
    public decimal Value { get; init; }
    public decimal Change { get; init; }
    public decimal ChangePercent { get; init; }
    public DateTime Timestamp { get; init; }
    public long Volume { get; init; }
}

/// <summary>
/// Event args for Polygon errors
/// </summary>
public class PolygonErrorEventArgs : EventArgs
{
    public string Message { get; init; } = "";
    public int? ErrorCode { get; init; }
    public Exception? Exception { get; init; }
}

/// <summary>
/// Polygon WebSocket message types
/// </summary>
public static class PolygonMessageTypes
{
    public const string Status = "status";
    public const string IndexValue = "V"; // Index value update
    public const string Error = "error";
    public const string Success = "success";
}

/// <summary>
/// Base class for Polygon WebSocket messages
/// </summary>
public abstract class PolygonWebSocketMessage
{
    public string EventType { get; init; } = "";
}

/// <summary>
/// Polygon status message
/// </summary>
public class PolygonStatusMessage : PolygonWebSocketMessage
{
    public string Status { get; init; } = "";
    public string Message { get; init; } = "";
}

/// <summary>
/// Polygon index value message
/// </summary>
public class PolygonIndexValueMessage : PolygonWebSocketMessage
{
    public string Symbol { get; init; } = "";
    public decimal Value { get; init; }
    public long Timestamp { get; init; }
    public decimal? Change { get; init; }
    public decimal? ChangePercent { get; init; }
    public long? Volume { get; init; }
}

/// <summary>
/// Polygon error message
/// </summary>
public class PolygonErrorMessage : PolygonWebSocketMessage
{
    public string Message { get; init; } = "";
    public int? Code { get; init; }
}
