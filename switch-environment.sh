#!/bin/bash
# SmaTrendFollower Environment Switcher
# Usage: ./switch-environment.sh [live|paper]

if [ $# -eq 0 ]; then
    echo "Usage: $0 [live|paper]"
    exit 1
fi

ENVIRONMENT=$1
ENV_FILE=".env"

if [ ! -f "$ENV_FILE" ]; then
    echo "❌ Environment file .env not found!"
    exit 1
fi

if [ "$ENVIRONMENT" = "live" ]; then
    echo "🔴 Switching to LIVE TRADING environment..."
    echo "⚠️  WARNING: This will use REAL MONEY!"
    
    # Enable live credentials
    sed -i 's/^# \(APCA_API_KEY_ID=AKGBPW5HD8LVI5C6NJUJ\)/\1/' "$ENV_FILE"
    sed -i 's/^# \(APCA_API_SECRET_KEY=MdrZZtKbFtLQWNuggQMDqN12loiOheHWjSiWN2CM\)/\1/' "$ENV_FILE"
    sed -i 's/^# \(APCA_API_ENV=live\)/\1/' "$ENV_FILE"
    
    # Disable paper credentials
    sed -i 's/^\(APCA_API_KEY_ID=PK0AM3WB1CES3YBQPGR0\)/# \1/' "$ENV_FILE"
    sed -i 's/^\(APCA_API_SECRET_KEY=2O4bJsHpGjyYU6FvQ956kIJaLePdBihZveKutbtf\)/# \1/' "$ENV_FILE"
    sed -i 's/^\(APCA_API_ENV=paper\)/# \1/' "$ENV_FILE"
    
    # Set safety environment
    sed -i 's/^SAFETY_ALLOWED_ENVIRONMENT=.*/SAFETY_ALLOWED_ENVIRONMENT=Live/' "$ENV_FILE"
    sed -i 's/^SAFETY_REQUIRE_CONFIRMATION=.*/SAFETY_REQUIRE_CONFIRMATION=true/' "$ENV_FILE"
    
elif [ "$ENVIRONMENT" = "paper" ]; then
    echo "📄 Switching to PAPER TRADING environment..."
    echo "✅ Safe mode: No real money will be used"
    
    # Disable live credentials
    sed -i 's/^\(APCA_API_KEY_ID=AKGBPW5HD8LVI5C6NJUJ\)/# \1/' "$ENV_FILE"
    sed -i 's/^\(APCA_API_SECRET_KEY=MdrZZtKbFtLQWNuggQMDqN12loiOheHWjSiWN2CM\)/# \1/' "$ENV_FILE"
    sed -i 's/^\(APCA_API_ENV=live\)/# \1/' "$ENV_FILE"
    
    # Enable paper credentials
    sed -i 's/^# \(APCA_API_KEY_ID=PK0AM3WB1CES3YBQPGR0\)/\1/' "$ENV_FILE"
    sed -i 's/^# \(APCA_API_SECRET_KEY=2O4bJsHpGjyYU6FvQ956kIJaLePdBihZveKutbtf\)/\1/' "$ENV_FILE"
    sed -i 's/^# \(APCA_API_ENV=paper\)/\1/' "$ENV_FILE"
    
    # Set safety environment
    sed -i 's/^SAFETY_ALLOWED_ENVIRONMENT=.*/SAFETY_ALLOWED_ENVIRONMENT=Paper/' "$ENV_FILE"
    sed -i 's/^SAFETY_REQUIRE_CONFIRMATION=.*/SAFETY_REQUIRE_CONFIRMATION=false/' "$ENV_FILE"
    
else
    echo "❌ Invalid environment. Use 'live' or 'paper'"
    exit 1
fi

echo "✅ Environment switched to: $ENVIRONMENT"
echo ""
echo "Current configuration:"
echo "  Environment: $ENVIRONMENT"

if [ "$ENVIRONMENT" = "live" ]; then
    echo "  API Key: AKGBPW5HD8LVI5C6NJUJ"
    echo "  Confirmation Required: Yes"
    echo ""
    echo "⚠️  REMINDER: Use --confirm flag for live trades"
else
    echo "  API Key: PK0AM3WB1CES3YBQPGR0"
    echo "  Confirmation Required: No"
    echo ""
    echo "✅ Paper trading mode - safe for testing"
fi
