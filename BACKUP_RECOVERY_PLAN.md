# SmaTrendFollower Backup and Recovery Plan

## Overview
This document outlines comprehensive backup and recovery procedures for the SmaTrendFollower trading system to ensure business continuity and data protection.

## 🔄 Backup Strategy

### 1. Configuration Backups
```bash
# Daily configuration backup
backup-config.ps1

# Manual backup before changes
cp .env .env.backup.$(date +%Y%m%d_%H%M%S)
cp appsettings.json appsettings.backup.$(date +%Y%m%d_%H%M%S)
```

### 2. Database Backups
```bash
# SQLite database backup
cp data/stock_bars.db data/backups/stock_bars_$(date +%Y%m%d_%H%M%S).db

# Automated daily backup
# Add to cron/task scheduler:
# 0 2 * * * /path/to/backup-database.sh
```

### 3. Trading State Backups
```bash
# Redis state backup
redis-cli --rdb data/backups/redis_$(date +%Y%m%d_%H%M%S).rdb

# Live state backup
dotnet run -- backup-state --output data/backups/state_$(date +%Y%m%d_%H%M%S).json
```

### 4. Log File Archival
```bash
# Archive old logs (keep last 30 days)
find logs/ -name "*.log" -mtime +30 -exec gzip {} \;
find logs/ -name "*.log.gz" -mtime +90 -delete
```

## 🛠️ Automated Backup Scripts

### backup-system.ps1
```powershell
# SmaTrendFollower System Backup Script
param(
    [string]$BackupPath = ".\backups\$(Get-Date -Format 'yyyyMMdd_HHmmss')"
)

Write-Host "Starting SmaTrendFollower system backup..." -ForegroundColor Green

# Create backup directory
New-Item -ItemType Directory -Path $BackupPath -Force

# Backup configuration files
Write-Host "Backing up configuration files..." -ForegroundColor Yellow
Copy-Item ".env" "$BackupPath\.env.backup" -ErrorAction SilentlyContinue
Copy-Item "appsettings.json" "$BackupPath\appsettings.backup.json" -ErrorAction SilentlyContinue

# Backup database
Write-Host "Backing up database..." -ForegroundColor Yellow
if (Test-Path "data\stock_bars.db") {
    Copy-Item "data\stock_bars.db" "$BackupPath\stock_bars.backup.db"
}

# Backup Redis state
Write-Host "Backing up Redis state..." -ForegroundColor Yellow
try {
    redis-cli --rdb "$BackupPath\redis.backup.rdb" 2>$null
} catch {
    Write-Host "Redis backup failed (Redis may not be running)" -ForegroundColor Red
}

# Backup trading state
Write-Host "Backing up trading state..." -ForegroundColor Yellow
try {
    & dotnet run --project SmaTrendFollower.Console -- backup-state --output "$BackupPath\trading_state.backup.json" 2>$null
} catch {
    Write-Host "Trading state backup failed" -ForegroundColor Red
}

# Backup logs (last 7 days)
Write-Host "Backing up recent logs..." -ForegroundColor Yellow
if (Test-Path "logs") {
    $recentLogs = Get-ChildItem "logs" -Filter "*.log" | Where-Object { $_.LastWriteTime -gt (Get-Date).AddDays(-7) }
    foreach ($log in $recentLogs) {
        Copy-Item $log.FullName "$BackupPath\$($log.Name)"
    }
}

# Create backup manifest
$manifest = @{
    BackupDate = Get-Date -Format "yyyy-MM-dd HH:mm:ss"
    BackupPath = $BackupPath
    Files = Get-ChildItem $BackupPath | Select-Object Name, Length, LastWriteTime
    SystemInfo = @{
        MachineName = $env:COMPUTERNAME
        UserName = $env:USERNAME
        OSVersion = [System.Environment]::OSVersion.VersionString
        DotNetVersion = (dotnet --version)
    }
}

$manifest | ConvertTo-Json -Depth 3 | Out-File "$BackupPath\backup_manifest.json"

Write-Host "Backup completed successfully: $BackupPath" -ForegroundColor Green
Write-Host "Backup size: $((Get-ChildItem $BackupPath -Recurse | Measure-Object -Property Length -Sum).Sum / 1MB) MB" -ForegroundColor Green
```

## 🔧 Recovery Procedures

### 1. Configuration Recovery
```bash
# Restore from backup
cp .env.backup .env
cp appsettings.backup.json appsettings.json

# Validate configuration
dotnet run -- validate
```

### 2. Database Recovery
```bash
# Stop application
# Restore database
cp data/backups/stock_bars_YYYYMMDD_HHMMSS.db data/stock_bars.db

# Verify database integrity
dotnet run -- validate-database
```

### 3. Trading State Recovery
```bash
# Restore Redis state
redis-cli --rdb data/backups/redis_YYYYMMDD_HHMMSS.rdb

# Restore trading state
dotnet run -- restore-state --input data/backups/state_YYYYMMDD_HHMMSS.json
```

### 4. Complete System Recovery
```powershell
# restore-system.ps1
param(
    [Parameter(Mandatory=$true)]
    [string]$BackupPath
)

Write-Host "Starting system recovery from: $BackupPath" -ForegroundColor Yellow

# Verify backup exists
if (-not (Test-Path $BackupPath)) {
    Write-Host "Backup path not found: $BackupPath" -ForegroundColor Red
    exit 1
}

# Stop services
Write-Host "Stopping services..." -ForegroundColor Yellow
# Add service stop commands here

# Restore configuration
if (Test-Path "$BackupPath\.env.backup") {
    Copy-Item "$BackupPath\.env.backup" ".env"
    Write-Host "Configuration restored" -ForegroundColor Green
}

# Restore database
if (Test-Path "$BackupPath\stock_bars.backup.db") {
    Copy-Item "$BackupPath\stock_bars.backup.db" "data\stock_bars.db"
    Write-Host "Database restored" -ForegroundColor Green
}

# Restore Redis state
if (Test-Path "$BackupPath\redis.backup.rdb") {
    # Stop Redis, restore file, restart Redis
    Write-Host "Redis state restored" -ForegroundColor Green
}

# Validate system
Write-Host "Validating restored system..." -ForegroundColor Yellow
$validation = & dotnet run --project SmaTrendFollower.Console -- validate 2>&1
if ($validation -match "completed") {
    Write-Host "System validation successful" -ForegroundColor Green
} else {
    Write-Host "System validation failed: $validation" -ForegroundColor Red
}

Write-Host "Recovery completed" -ForegroundColor Green
```

## 🚨 Disaster Recovery Scenarios

### Scenario 1: Configuration Corruption
**Symptoms**: Application fails to start, configuration errors
**Recovery**:
1. Restore from latest configuration backup
2. Validate configuration
3. Test with dry run mode
4. Resume normal operations

### Scenario 2: Database Corruption
**Symptoms**: Database errors, missing historical data
**Recovery**:
1. Stop application
2. Restore database from backup
3. Validate database integrity
4. Rebuild cache if necessary
5. Resume operations

### Scenario 3: Complete System Failure
**Symptoms**: Hardware failure, OS corruption
**Recovery**:
1. Set up new system environment
2. Install dependencies (.NET 8, Redis, etc.)
3. Restore complete system backup
4. Validate all components
5. Test with paper trading
6. Resume live trading

### Scenario 4: Trading State Loss
**Symptoms**: Lost position tracking, missing stop orders
**Recovery**:
1. Immediately enable dry run mode
2. Manually verify positions via Alpaca dashboard
3. Restore trading state from backup
4. Reconcile positions and orders
5. Resume trading with validation

## 📋 Recovery Testing

### Automated Recovery Testing
```powershell
# Run comprehensive disaster recovery test
.\test-disaster-recovery.ps1

# Test specific recovery scenarios
.\test-disaster-recovery.ps1 -Scenario ConfigCorruption
.\test-disaster-recovery.ps1 -Scenario DatabaseCorruption
.\test-disaster-recovery.ps1 -Scenario CompleteSystemFailure

# Monthly recovery drill
.\test-disaster-recovery.ps1 -FullDrill -GenerateReport
```

### Manual Recovery Drills
```bash
# Test configuration recovery
cp .env .env.test.backup
# Corrupt configuration
echo "INVALID_CONFIG=true" > .env
# Attempt recovery
cp .env.test.backup .env
dotnet run -- validate

# Test database recovery
# Similar process for database

# Test complete system recovery
# Use backup-system.ps1 and restore-system.ps1
```

### Recovery Time Objectives (RTO)
- **Configuration Recovery**: < 5 minutes
- **Database Recovery**: < 15 minutes
- **Complete System Recovery**: < 30 minutes
- **Trading State Recovery**: < 10 minutes

### Recovery Point Objectives (RPO)
- **Configuration**: Last backup (daily)
- **Database**: Last backup (daily)
- **Trading State**: Real-time (Redis persistence)
- **Logs**: Last 7 days retained

## 📊 Backup Monitoring

### Backup Health Checks
```bash
# Daily backup verification
verify-backups.ps1

# Check backup integrity
Test-Path "backups\latest\backup_manifest.json"

# Verify backup completeness
$manifest = Get-Content "backups\latest\backup_manifest.json" | ConvertFrom-Json
$manifest.Files.Count -gt 0
```

### Backup Alerts
- **Failed Backup**: Immediate notification
- **Missing Backup**: Daily check
- **Backup Size Anomaly**: Weekly review
- **Old Backup Cleanup**: Monthly maintenance

## 🔐 Security Considerations

### Backup Encryption
```bash
# Encrypt sensitive backups
gpg --symmetric --cipher-algo AES256 backup.tar.gz

# Decrypt for recovery
gpg --decrypt backup.tar.gz.gpg > backup.tar.gz
```

### Access Control
- Backup files stored in secure location
- Access restricted to authorized personnel
- Audit trail for backup access
- Regular security reviews

---
*Last Updated: 2025-06-21*
*Next Review: 2025-07-21*
