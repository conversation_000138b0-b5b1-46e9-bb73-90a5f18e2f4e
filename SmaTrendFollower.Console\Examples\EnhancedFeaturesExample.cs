using SmaTrendFollower.Services;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;

namespace SmaTrendFollower.Console.Examples;

/// <summary>
/// Example demonstrating the enhanced features of SmaTrendFollower
/// </summary>
public static class EnhancedFeaturesExample
{
    public static async Task RunExampleAsync(IServiceProvider serviceProvider)
    {
        var logger = serviceProvider.GetRequiredService<ILogger<Program>>();
        
        logger.LogInformation("=== SmaTrendFollower Enhanced Features Demo ===");

        // 1. Demonstrate VIX-based volatility analysis
        await DemonstrateVolatilityAnalysisAsync(serviceProvider, logger);

        // 2. Demonstrate enhanced signal generation
        await DemonstrateEnhancedSignalsAsync(serviceProvider, logger);

        // 3. Demonstrate options strategies
        await DemonstrateOptionsStrategiesAsync(serviceProvider, logger);

        // 4. Demonstrate Discord notifications
        await DemonstrateDiscordNotificationsAsync(serviceProvider, logger);

        // 5. Demonstrate enhanced market data
        await DemonstrateEnhancedMarketDataAsync(serviceProvider, logger);

        logger.LogInformation("=== Enhanced Features Demo Complete ===");
    }

    private static async Task DemonstrateVolatilityAnalysisAsync(IServiceProvider serviceProvider, ILogger logger)
    {
        logger.LogInformation("--- VIX-Based Volatility Analysis ---");

        var volatilityManager = serviceProvider.GetRequiredService<IVolatilityManager>();

        try
        {
            // Get current volatility regime
            var regime = await volatilityManager.GetCurrentRegimeAsync();
            logger.LogInformation("Current Volatility Regime: {RegimeName}", regime.RegimeName);
            logger.LogInformation("VIX Level: {CurrentVix:F1} (30-day SMA: {VixSma30:F1})", 
                regime.CurrentVix, regime.VixSma30);
            logger.LogInformation("Position Size Multiplier: {Multiplier:P1}", regime.PositionSizeMultiplier - 1);

            // Check for VIX spike
            var isSpike = await volatilityManager.IsVixSpikeDetectedAsync();
            logger.LogInformation("VIX Spike Detected: {IsSpike}", isSpike);

            // Demonstrate IV-adjusted stop calculation
            var adjustedStop = await volatilityManager.GetIvAdjustedStopAsync("SPY", 450m, 5m);
            logger.LogInformation("IV-Adjusted Stop for SPY: ${AdjustedStop:F2} (vs standard ${StandardStop:F2})", 
                adjustedStop, 450m - (2m * 5m));
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Error in volatility analysis demo");
        }
    }

    private static async Task DemonstrateEnhancedSignalsAsync(IServiceProvider serviceProvider, ILogger logger)
    {
        logger.LogInformation("--- Enhanced Signal Generation ---");

        var signalGenerator = serviceProvider.GetRequiredService<ISignalGenerator>();

        try
        {
            // Generate signals with VIX-based adjustments
            var signals = await signalGenerator.RunAsync(10);
            var signalList = signals.ToList();

            logger.LogInformation("Generated {Count} trading signals with VIX adjustments", signalList.Count);

            foreach (var signal in signalList.Take(3)) // Show first 3 signals
            {
                logger.LogInformation("Signal: {Symbol} - Price: ${Price:F2}, ATR: ${ATR:F2}, 6M Return: {Return:P2}",
                    signal.Symbol, signal.Price, signal.Atr, signal.SixMonthReturn);
            }
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Error in enhanced signals demo");
        }
    }

    private static async Task DemonstrateOptionsStrategiesAsync(IServiceProvider serviceProvider, ILogger logger)
    {
        logger.LogInformation("--- Options Overlay Strategies ---");

        var optionsManager = serviceProvider.GetRequiredService<IOptionsStrategyManager>();
        var marketDataService = serviceProvider.GetRequiredService<IMarketDataService>();

        try
        {
            // Demonstrate protective put evaluation
            var putResult = await optionsManager.EvaluateProtectivePutAsync("SPY", 100000m, 450m);
            logger.LogInformation("Protective Put Analysis for SPY:");
            logger.LogInformation("  Should Execute: {ShouldExecute}", putResult.ShouldExecute);
            logger.LogInformation("  Reason: {Reason}", putResult.Reason);
            if (putResult.ShouldExecute)
            {
                logger.LogInformation("  Strike: ${Strike:F2}, Premium: ${Premium:F2}, Protection: {Protection:P1}",
                    putResult.Strike, putResult.Premium, putResult.ProtectionLevel);
            }

            // Demonstrate covered call evaluation
            var callResult = await optionsManager.EvaluateCoveredCallAsync("SPY", 100m, 450m);
            logger.LogInformation("Covered Call Analysis for SPY (100 shares):");
            logger.LogInformation("  Should Execute: {ShouldExecute}", callResult.ShouldExecute);
            logger.LogInformation("  Reason: {Reason}", callResult.Reason);
            if (callResult.ShouldExecute)
            {
                logger.LogInformation("  Strike: ${Strike:F2}, Premium: ${Premium:F2}, Annualized Yield: {Yield:P1}",
                    callResult.Strike, callResult.Premium, callResult.AnnualizedYield);
            }

            // Demonstrate delta-efficient exposure
            var deltaResult = await optionsManager.EvaluateDeltaEfficientExposureAsync("SPY", 50000m, 450m);
            logger.LogInformation("Delta-Efficient Exposure Analysis:");
            logger.LogInformation("  Should Execute: {ShouldExecute}", deltaResult.ShouldExecute);
            logger.LogInformation("  Reason: {Reason}", deltaResult.Reason);
            if (deltaResult.ShouldExecute)
            {
                logger.LogInformation("  Capital Efficiency: {Efficiency:F1}x, Effective Delta: {Delta:P1}",
                    deltaResult.CapitalEfficiency, deltaResult.EffectiveDelta);
            }
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Error in options strategies demo");
        }
    }

    private static async Task DemonstrateDiscordNotificationsAsync(IServiceProvider serviceProvider, ILogger logger)
    {
        logger.LogInformation("--- Discord Notifications ---");

        var discordService = serviceProvider.GetRequiredService<IDiscordNotificationService>();

        try
        {
            // Send sample trade notification
            await discordService.SendTradeNotificationAsync("SPY", "BUY", 10m, 450.25m, 0m);
            logger.LogInformation("Sent sample trade notification to Discord");

            // Send sample portfolio snapshot
            await discordService.SendPortfolioSnapshotAsync(125000m, 1250m, 5000m, 8);
            logger.LogInformation("Sent sample portfolio snapshot to Discord");

            // Send sample VIX spike alert
            await discordService.SendVixSpikeAlertAsync(28.5m, 25.0m, "Reducing position sizes");
            logger.LogInformation("Sent sample VIX spike alert to Discord");

            // Send sample options notification
            await discordService.SendOptionsNotificationAsync("Protective Put", "SPY", 
                "Recommended: $440 strike, $3.50 premium, 2.2% protection");
            logger.LogInformation("Sent sample options notification to Discord");
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Error in Discord notifications demo");
        }
    }

    private static async Task DemonstrateEnhancedMarketDataAsync(IServiceProvider serviceProvider, ILogger logger)
    {
        logger.LogInformation("--- Enhanced Market Data ---");

        var marketDataService = serviceProvider.GetRequiredService<IMarketDataService>();

        try
        {
            // Demonstrate VIX analysis
            var vixAnalysis = await marketDataService.GetVixAnalysisAsync();
            logger.LogInformation("VIX Analysis:");
            logger.LogInformation("  Current VIX: {CurrentVix:F1}", vixAnalysis.CurrentVix);
            logger.LogInformation("  30-day SMA: {VixSma30:F1}", vixAnalysis.VixSma30);
            logger.LogInformation("  Above SMA: {IsAboveSma}", vixAnalysis.IsAboveSma);
            logger.LogInformation("  Spike Detected: {IsSpike}", vixAnalysis.IsSpike);

            // Demonstrate universe filtering
            var universe = await marketDataService.GetUniverseWithAdvFilterAsync();
            var universeList = universe.ToList();
            logger.LogInformation("Universe with ADV >$20M: {Count} symbols", universeList.Count);
            logger.LogInformation("Sample symbols: {Symbols}", string.Join(", ", universeList.Take(10)));

            // Demonstrate options data
            var spyOptions = await marketDataService.GetProtectivePutOptionsAsync("SPY", 30);
            var spyOptionsList = spyOptions.ToList();
            logger.LogInformation("SPY Protective Put Options (30 DTE): {Count} contracts", spyOptionsList.Count);

            var spyCalls = await marketDataService.GetCoveredCallOptionsAsync("SPY", 450m, 7);
            var spyCallsList = spyCalls.ToList();
            logger.LogInformation("SPY Covered Call Options (7 DTE): {Count} contracts", spyCallsList.Count);
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Error in enhanced market data demo");
        }
    }
}
