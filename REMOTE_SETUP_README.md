# SmaTrendFollower Remote Environment Setup

This directory contains setup scripts to prepare the remote environment for the Augment Agent to work effectively with the SmaTrendFollower trading system.

## 🚀 Quick Start

### Windows (PowerShell)
```powershell
# Make script executable and run
Set-ExecutionPolicy -ExecutionPolicy RemoteSigned -Scope CurrentUser
.\setup-remote-environment.ps1
```

### Linux/macOS (Bash)
```bash
# Make script executable and run
chmod +x setup-remote-environment.sh
./setup-remote-environment.sh
```

## 📋 What the Setup Scripts Do

The setup scripts perform the following operations:

### 1. **Prerequisites Check**
- ✅ Verify .NET 8 SDK installation
- ✅ Check PowerShell/Bash version
- ✅ Validate system compatibility

### 2. **Project Structure Validation**
- ✅ Verify solution file exists
- ✅ Check project files are present
- ✅ Validate .env configuration file

### 3. **Environment Setup**
- ✅ Create required directories (`logs/`, `data/`, `cache/`)
- ✅ Validate environment variables
- ✅ Create .env template if missing

### 4. **Build Process**
- ✅ Clean previous builds
- ✅ Restore NuGet packages
- ✅ Build solution in Release mode
- ✅ Run unit tests (optional)

### 5. **Validation**
- ✅ Verify build artifacts
- ✅ Test application startup
- ✅ Provide setup summary

## 🔧 Script Options

### PowerShell Script Options
```powershell
# Skip tests during setup
.\setup-remote-environment.ps1 -SkipTests

# Enable verbose output
.\setup-remote-environment.ps1 -Verbose

# Combine options
.\setup-remote-environment.ps1 -SkipTests -Verbose
```

### Bash Script Options
```bash
# Skip tests during setup
./setup-remote-environment.sh --skip-tests

# Enable verbose output
./setup-remote-environment.sh --verbose

# Show help
./setup-remote-environment.sh --help
```

## 📁 Directory Structure After Setup

```
SmaTrendFollower/
├── SmaTrendFollower.Console/          # Main trading application
├── SmaTrendFollower.Tests/            # Unit and integration tests
├── logs/                              # Application logs (created)
├── data/                              # Data files (created)
├── cache/                             # Cache files (created)
├── .env                               # Environment configuration
├── setup-remote-environment.ps1      # Windows setup script
├── setup-remote-environment.sh       # Linux/macOS setup script
└── REMOTE_SETUP_README.md            # This file
```

## 🔑 Environment Variables

The setup script validates the following environment variables in `.env`:

### Required Variables
- `APCA_API_KEY_ID` - Alpaca API Key ID
- `APCA_API_SECRET_KEY` - Alpaca API Secret Key
- `APCA_API_ENV` - Alpaca environment (`paper` or `live`)
- `POLY_API_KEY` - Polygon.io API Key

### Optional Variables
- `MAX_POSITION_SIZE_PERCENT` - Maximum position size (default: 5%)
- `STOP_LOSS_PERCENT` - Stop loss percentage (default: 2%)
- `TAKE_PROFIT_PERCENT` - Take profit percentage (default: 6%)

## 🧪 Testing Commands

After setup, the Augment Agent can use these commands:

### Build Commands
```bash
# Clean build
dotnet clean

# Restore packages
dotnet restore

# Build solution
dotnet build

# Build in Release mode
dotnet build --configuration Release
```

### Test Commands
```bash
# Run all tests
dotnet test

# Run tests with verbose output
dotnet test --verbosity normal

# Run specific test project
dotnet test SmaTrendFollower.Tests/
```

### Run Commands
```bash
# Run console application
dotnet run --project SmaTrendFollower.Console

# Run with specific configuration
dotnet run --project SmaTrendFollower.Console --configuration Release
```

## 🔍 Troubleshooting

### Common Issues

#### 1. .NET SDK Not Found
**Error**: `.NET SDK not found`
**Solution**: Install .NET 8 SDK from https://dotnet.microsoft.com/download

#### 2. Permission Denied (Linux/macOS)
**Error**: `Permission denied`
**Solution**: Make script executable with `chmod +x setup-remote-environment.sh`

#### 3. Execution Policy (Windows)
**Error**: `Execution policy restriction`
**Solution**: Run `Set-ExecutionPolicy -ExecutionPolicy RemoteSigned -Scope CurrentUser`

#### 4. Missing Environment Variables
**Error**: Environment variables not configured
**Solution**: Edit `.env` file with your actual API keys

#### 5. Build Failures
**Error**: Build fails with package restore issues
**Solution**: 
- Clear NuGet cache: `dotnet nuget locals all --clear`
- Restore packages: `dotnet restore --force`
- Rebuild: `dotnet build --no-incremental`

### Validation Commands

```bash
# Check .NET version
dotnet --version
dotnet --info

# Verify project structure
ls -la SmaTrendFollower.Console/
ls -la SmaTrendFollower.Tests/

# Test environment variables
cat .env

# Check build output
ls -la SmaTrendFollower.Console/bin/Release/net8.0/
```

## 🚨 Security Notes

⚠️ **Important Security Considerations:**

1. **API Keys**: Never commit real API keys to version control
2. **Environment**: Use `paper` environment for testing
3. **Permissions**: Limit file permissions on `.env` file
4. **Network**: Ensure secure network connection for API calls

## 🤖 For Augment Agent

After running the setup script, the environment will be ready for:

- **Code Analysis**: Full project structure available
- **Building**: `dotnet build` commands work
- **Testing**: `dotnet test` commands work  
- **Running**: `dotnet run` commands work
- **Debugging**: Logs available in `logs/` directory
- **Data Access**: Cache and data directories available

The agent can safely make changes and test them using the standard .NET CLI commands.

## 📞 Support

If you encounter issues with the setup scripts:

1. Check the troubleshooting section above
2. Verify all prerequisites are installed
3. Ensure API keys are properly configured
4. Review the verbose output for specific error messages

The setup scripts are designed to be idempotent - you can run them multiple times safely.
