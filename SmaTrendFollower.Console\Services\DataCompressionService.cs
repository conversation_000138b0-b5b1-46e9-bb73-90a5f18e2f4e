using System.IO.Compression;
using System.Text;
using Microsoft.Extensions.Logging;

namespace SmaTrendFollower.Services;

/// <summary>
/// Service for compressing and decompressing historical bar data using Brotli compression
/// Provides significant space savings for historical data while maintaining fast decompression
/// </summary>
public sealed class DataCompressionService : IDataCompressionService
{
    private readonly ILogger<DataCompressionService> _logger;

    public DataCompressionService(ILogger<DataCompressionService> logger)
    {
        _logger = logger;
    }

    public byte[] Compress(byte[] data)
    {
        if (data == null || data.Length == 0)
            return Array.Empty<byte>();

        try
        {
            using var output = new MemoryStream();
            using (var brotli = new BrotliStream(output, CompressionLevel.Optimal))
            {
                brotli.Write(data, 0, data.Length);
            }

            var compressed = output.ToArray();
            var ratio = GetCompressionRatio(data.Length, compressed.Length);
            
            _logger.LogDebug("Compressed {OriginalSize} bytes to {CompressedSize} bytes (ratio: {Ratio:P2})",
                data.Length, compressed.Length, ratio);

            return compressed;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error compressing data of size {Size} bytes", data.Length);
            throw;
        }
    }

    public byte[] Decompress(byte[] compressedData)
    {
        if (compressedData == null || compressedData.Length == 0)
            return Array.Empty<byte>();

        try
        {
            using var input = new MemoryStream(compressedData);
            using var brotli = new BrotliStream(input, CompressionMode.Decompress);
            using var output = new MemoryStream();
            
            brotli.CopyTo(output);
            var decompressed = output.ToArray();

            _logger.LogDebug("Decompressed {CompressedSize} bytes to {DecompressedSize} bytes",
                compressedData.Length, decompressed.Length);

            return decompressed;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error decompressing data of size {Size} bytes", compressedData.Length);
            throw;
        }
    }

    public byte[] CompressJson(string jsonData)
    {
        if (string.IsNullOrEmpty(jsonData))
            return Array.Empty<byte>();

        var bytes = Encoding.UTF8.GetBytes(jsonData);
        return Compress(bytes);
    }

    public string DecompressToJson(byte[] compressedData)
    {
        if (compressedData == null || compressedData.Length == 0)
            return string.Empty;

        var decompressed = Decompress(compressedData);
        return Encoding.UTF8.GetString(decompressed);
    }

    public double GetCompressionRatio(int originalSize, int compressedSize)
    {
        if (originalSize == 0)
            return 0.0;

        return (double)compressedSize / originalSize;
    }
}
