#!/usr/bin/env pwsh

<#
.SYNOPSIS
    Adds test category traits to test methods for proper test separation
.DESCRIPTION
    This script categorizes tests as Unit, Integration, Performance, etc.
    based on their content and file patterns.
.EXAMPLE
    ./categorize-tests.ps1
#>

param(
    [switch]$DryRun = $false,
    [switch]$Verbose = $false
)

# Define test categorization rules
$CategoryRules = @{
    # Integration tests - files that contain these patterns
    'Integration' = @(
        '*Integration*Tests.cs',
        '*StreamingDataService*Tests.cs',
        '*PolygonApi*Tests.cs',
        '*AlpacaApi*Tests.cs'
    )
    
    # Performance tests - files or methods that contain these patterns
    'Performance' = @(
        '*Performance*Tests.cs',
        '*Benchmark*Tests.cs'
    )
    
    # Database tests - files that work with databases
    'Database' = @(
        '*Cache*Tests.cs',
        '*Redis*Tests.cs'
    )
    
    # Network tests - files that make network calls
    'Network' = @(
        '*RateLimit*Tests.cs',
        '*Client*Tests.cs'
    )
    
    # Unit tests - everything else (default)
    'Unit' = @('*Tests.cs')
}

function Get-TestCategory {
    param(
        [string]$FileName,
        [string]$FileContent
    )
    
    # Check for explicit integration test markers
    if ($FileContent -match 'TestConfiguration\.ShouldRun.*Tests' -or
        $FileContent -match '\[Trait\("Category",\s*"Integration"\)\]' -or
        $FileName -like '*Integration*') {
        return 'Integration'
    }
    
    # Check for performance test markers
    if ($FileContent -match 'Performance|Benchmark|Load|Stress' -and
        ($FileName -like '*Performance*' -or $FileContent -match '\[Trait\("Category",\s*"Performance"\)\]')) {
        return 'Performance'
    }
    
    # Check for database test markers
    if ($FileName -like '*Cache*' -or $FileName -like '*Redis*' -or
        $FileContent -match 'DbContext|Database|SQLite|Redis') {
        return 'Database'
    }
    
    # Check for network test markers
    if ($FileName -like '*RateLimit*' -or $FileName -like '*Client*' -or
        $FileContent -match 'HttpClient|Network|Api') {
        return 'Network'
    }
    
    # Default to Unit tests
    return 'Unit'
}

function Add-CategoryToTest {
    param(
        [string]$FilePath,
        [string]$Content
    )
    
    $fileName = Split-Path $FilePath -Leaf
    $category = Get-TestCategory -FileName $fileName -FileContent $Content
    $modified = $false
    $lines = $Content -split "`n"
    $newLines = @()
    
    for ($i = 0; $i -lt $lines.Count; $i++) {
        $line = $lines[$i]
        $newLines += $line
        
        # Look for [TestTimeout] attributes that don't already have [Trait] for category
        if ($line -match '^\s*\[TestTimeout\(' -and $i -lt $lines.Count - 1) {
            # Check if the next few lines already have a category trait
            $hasCategory = $false
            for ($j = $i + 1; $j -lt [Math]::Min($lines.Count, $i + 5); $j++) {
                if ($lines[$j] -match '\[Trait\("Category"') {
                    $hasCategory = $true
                    break
                }
                if ($lines[$j] -match '^\s*\[(Fact|Theory)\]') {
                    break
                }
            }
            
            if (-not $hasCategory) {
                $indent = if ($line -match '^(\s*)') { $matches[1] } else { "    " }
                $categoryLine = "$indent[Trait(`"Category`", TestCategories.$category)]"
                $newLines += $categoryLine
                $modified = $true
                
                if ($Verbose) {
                    Write-Host "  Added $category category to test at line $($i + 1)" -ForegroundColor Green
                }
            }
        }
    }
    
    if ($modified) {
        return ($newLines -join "`n")
    }
    
    return $null
}

# Get the script directory and find test files
$scriptDir = Split-Path -Parent $MyInvocation.MyCommand.Path
$testDir = $scriptDir

# Get all test files
$testFiles = Get-ChildItem -Path $testDir -Recurse -Filter "*Tests.cs" | Where-Object {
    $_.FullName -notlike "*\bin\*" -and $_.FullName -notlike "*\obj\*"
}

Write-Host "Found $($testFiles.Count) test files to categorize" -ForegroundColor Cyan

$processedCount = 0
$modifiedCount = 0

foreach ($file in $testFiles) {
    $processedCount++
    Write-Host "[$processedCount/$($testFiles.Count)] Processing: $($file.Name)" -ForegroundColor Yellow
    
    try {
        $content = Get-Content -Path $file.FullName -Raw -Encoding UTF8
        
        # Skip if file already has comprehensive category traits
        $categoryCount = ($content | Select-String '\[Trait\("Category"' -AllMatches).Matches.Count
        $testCount = ($content | Select-String '\[Fact\]|\[Theory\]' -AllMatches).Matches.Count
        
        if ($categoryCount -ge $testCount * 0.8) {
            if ($Verbose) {
                Write-Host "  Skipping - already has sufficient category traits ($categoryCount/$testCount)" -ForegroundColor Gray
            }
            continue
        }
        
        $newContent = Add-CategoryToTest -FilePath $file.FullName -Content $content
        
        if ($newContent) {
            if (-not $DryRun) {
                Set-Content -Path $file.FullName -Value $newContent -Encoding UTF8 -NoNewline
                Write-Host "  Modified: $($file.Name)" -ForegroundColor Green
            } else {
                Write-Host "  Would modify: $($file.Name)" -ForegroundColor Magenta
            }
            $modifiedCount++
        } else {
            if ($Verbose) {
                Write-Host "  No changes needed" -ForegroundColor Gray
            }
        }
    }
    catch {
        Write-Host "  Error processing $($file.Name): $($_.Exception.Message)" -ForegroundColor Red
    }
}

Write-Host "`nSummary:" -ForegroundColor Cyan
Write-Host "  Processed: $processedCount files" -ForegroundColor White
Write-Host "  Modified: $modifiedCount files" -ForegroundColor Green

if ($DryRun) {
    Write-Host "  (Dry run - no files were actually modified)" -ForegroundColor Yellow
}

Write-Host "`nTest categories added:" -ForegroundColor Cyan
Write-Host "  Unit - Fast unit tests with mocks" -ForegroundColor White
Write-Host "  Integration - Tests requiring external dependencies" -ForegroundColor White
Write-Host "  Performance - Performance and benchmark tests" -ForegroundColor White
Write-Host "  Database - Tests using SQLite/Redis" -ForegroundColor White
Write-Host "  Network - Tests making network calls" -ForegroundColor White
