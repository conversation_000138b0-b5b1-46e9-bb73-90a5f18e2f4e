using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Configuration;
using System.Text.Json;
using System.Text.Json.Serialization;

namespace SmaTrendFollower.Services;

/// <summary>
/// Macro economic filter using FRED API to pause trading during bad macro regimes
/// Monitors T10Y2Y yield curve, unemployment rate, and federal funds rate
/// </summary>
public sealed class MacroFilter : IMacroFilter, IDisposable
{
    private readonly HttpClient _httpClient;
    private readonly ILiveStateStore _liveStateStore;
    private readonly ILogger<MacroFilter> _logger;
    private readonly MacroFilterConfig _config;
    private readonly SemaphoreSlim _cacheLock = new(1, 1);

    private MacroIndicators? _cachedIndicators;
    private DateTime _lastUpdate = DateTime.MinValue;

    public MacroFilter(
        HttpClient httpClient,
        ILiveStateStore liveStateStore,
        IConfiguration configuration,
        ILogger<MacroFilter> logger)
    {
        _httpClient = httpClient;
        _liveStateStore = liveStateStore;
        _logger = logger;
        
        var fredApiKey = configuration["FRED_API_KEY"] ?? throw new InvalidOperationException("FRED_API_KEY not configured");
        
        _config = new MacroFilterConfig(
            FredApiKey: fredApiKey,
            CacheExpiry: TimeSpan.FromHours(int.Parse(configuration["MACRO_CACHE_HOURS"] ?? "6")),
            YieldCurveInversionThreshold: decimal.Parse(configuration["MACRO_YIELD_CURVE_THRESHOLD"] ?? "-0.5"),
            UnemploymentRateThreshold: decimal.Parse(configuration["MACRO_UNEMPLOYMENT_THRESHOLD"] ?? "6.0"),
            FedFundsRateChangeThreshold: decimal.Parse(configuration["MACRO_FED_FUNDS_CHANGE_THRESHOLD"] ?? "0.5")
        );

        _httpClient.BaseAddress = new Uri("https://api.stlouisfed.org/fred/");
        _httpClient.DefaultRequestHeaders.Add("User-Agent", "SmaTrendFollower/1.0");
    }

    /// <summary>
    /// Determines if trading is allowed based on current macro conditions
    /// </summary>
    public async Task<bool> IsEligibleAsync(CancellationToken cancellationToken = default)
    {
        try
        {
            var indicators = await GetMacroIndicatorsAsync(cancellationToken);
            var analysis = AnalyzeMacroConditions(indicators);

            if (analysis.IsEligible)
            {
                _logger.LogDebug("Macro filter PASSED: {Reasoning}", analysis.Reasoning);
            }
            else
            {
                _logger.LogInformation("Macro filter BLOCKED trading: {Reasoning}", analysis.Reasoning);
            }

            // Cache the analysis
            await _liveStateStore.SetMarketStateAsync("macro_analysis", analysis, TimeSpan.FromHours(1));

            return analysis.IsEligible;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error in macro filter analysis");
            return true; // Fail open - allow trading on error
        }
    }

    /// <summary>
    /// Gets current macro analysis
    /// </summary>
    public async Task<MacroAnalysis> GetMacroAnalysisAsync(CancellationToken cancellationToken = default)
    {
        try
        {
            var indicators = await GetMacroIndicatorsAsync(cancellationToken);
            return AnalyzeMacroConditions(indicators);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting macro analysis");
            return new MacroAnalysis(
                MacroRegime.Unknown,
                false,
                $"Error: {ex.Message}",
                null,
                DateTime.UtcNow
            );
        }
    }

    /// <summary>
    /// Gets macro indicators from FRED API with caching
    /// </summary>
    private async Task<MacroIndicators> GetMacroIndicatorsAsync(CancellationToken cancellationToken)
    {
        await _cacheLock.WaitAsync(cancellationToken);
        try
        {
            // Check cache
            if (_cachedIndicators != null && DateTime.UtcNow - _lastUpdate < _config.CacheExpiry)
            {
                return _cachedIndicators;
            }

            // Fetch fresh data
            var tasks = new[]
            {
                FetchFredSeriesAsync("T10Y2Y", cancellationToken),  // 10-Year Treasury Constant Maturity Minus 2-Year
                FetchFredSeriesAsync("UNRATE", cancellationToken),  // Unemployment Rate
                FetchFredSeriesAsync("FEDFUNDS", cancellationToken) // Federal Funds Rate
            };

            var results = await Task.WhenAll(tasks);

            _cachedIndicators = new MacroIndicators(
                YieldCurveSpread: results[0]?.Value ?? 0,
                UnemploymentRate: results[1]?.Value ?? 0,
                FedFundsRate: results[2]?.Value ?? 0,
                YieldCurveDate: results[0]?.Date ?? DateTime.MinValue,
                UnemploymentDate: results[1]?.Date ?? DateTime.MinValue,
                FedFundsDate: results[2]?.Date ?? DateTime.MinValue,
                LastUpdated: DateTime.UtcNow
            );

            _lastUpdate = DateTime.UtcNow;

            _logger.LogInformation("Updated macro indicators: YieldCurve={YieldCurve:F2}, Unemployment={Unemployment:F1}%, FedFunds={FedFunds:F2}%",
                _cachedIndicators.YieldCurveSpread, _cachedIndicators.UnemploymentRate, _cachedIndicators.FedFundsRate);

            return _cachedIndicators;
        }
        finally
        {
            _cacheLock.Release();
        }
    }

    /// <summary>
    /// Fetches a single FRED data series
    /// </summary>
    private async Task<FredDataPoint?> FetchFredSeriesAsync(string seriesId, CancellationToken cancellationToken)
    {
        try
        {
            var url = $"series/observations?series_id={seriesId}&api_key={_config.FredApiKey}&file_type=json&limit=1&sort_order=desc";
            
            var response = await _httpClient.GetAsync(url, cancellationToken);
            response.EnsureSuccessStatusCode();

            var json = await response.Content.ReadAsStringAsync(cancellationToken);
            var fredResponse = JsonSerializer.Deserialize<FredResponse>(json);

            if (fredResponse?.Observations?.Any() == true)
            {
                var observation = fredResponse.Observations.First();
                if (decimal.TryParse(observation.Value, out var value) && 
                    DateTime.TryParse(observation.Date, out var date))
                {
                    return new FredDataPoint(value, date);
                }
            }

            _logger.LogWarning("No valid data found for FRED series {SeriesId}", seriesId);
            return null;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error fetching FRED series {SeriesId}", seriesId);
            return null;
        }
    }

    /// <summary>
    /// Analyzes macro conditions and determines trading eligibility
    /// </summary>
    private MacroAnalysis AnalyzeMacroConditions(MacroIndicators indicators)
    {
        var reasons = new List<string>();
        var warnings = new List<string>();
        var isEligible = true;

        // 1. Yield Curve Analysis
        if (indicators.YieldCurveSpread < _config.YieldCurveInversionThreshold)
        {
            reasons.Add($"Yield curve deeply inverted ({indicators.YieldCurveSpread:F2}%)");
            isEligible = false;
        }
        else if (indicators.YieldCurveSpread < 0)
        {
            warnings.Add($"Yield curve inverted ({indicators.YieldCurveSpread:F2}%)");
        }

        // 2. Unemployment Rate Analysis
        if (indicators.UnemploymentRate > _config.UnemploymentRateThreshold)
        {
            reasons.Add($"High unemployment rate ({indicators.UnemploymentRate:F1}%)");
            isEligible = false;
        }

        // 3. Federal Funds Rate Analysis (rapid changes indicate policy uncertainty)
        // Note: This would require historical data to calculate rate of change
        // For now, we'll use absolute level as a proxy
        if (indicators.FedFundsRate > 5.0m)
        {
            warnings.Add($"High federal funds rate ({indicators.FedFundsRate:F2}%)");
        }

        // 4. Data freshness check
        var oldestData = new[] { indicators.YieldCurveDate, indicators.UnemploymentDate, indicators.FedFundsDate }.Min();
        if (DateTime.UtcNow - oldestData > TimeSpan.FromDays(60))
        {
            warnings.Add("Macro data is stale (>60 days old)");
        }

        // Determine regime
        var regime = DetermineMacroRegime(indicators);

        var reasoning = isEligible ? 
            (warnings.Any() ? $"Eligible with warnings: {string.Join("; ", warnings)}" : "Favorable macro conditions") :
            $"Blocked: {string.Join("; ", reasons)}";

        return new MacroAnalysis(
            regime,
            isEligible,
            reasoning,
            indicators,
            DateTime.UtcNow
        );
    }

    /// <summary>
    /// Determines macro economic regime
    /// </summary>
    private MacroRegime DetermineMacroRegime(MacroIndicators indicators)
    {
        // Simplified regime classification
        if (indicators.YieldCurveSpread < -1.0m && indicators.UnemploymentRate > 5.0m)
            return MacroRegime.Recession;
        
        if (indicators.YieldCurveSpread < 0)
            return MacroRegime.Tightening;
        
        if (indicators.UnemploymentRate < 4.0m && indicators.FedFundsRate < 2.0m)
            return MacroRegime.Expansion;
        
        return MacroRegime.Neutral;
    }

    public void Dispose()
    {
        _cacheLock?.Dispose();
        _httpClient?.Dispose();
    }
}

/// <summary>
/// Interface for macro economic filter
/// </summary>
public interface IMacroFilter
{
    Task<bool> IsEligibleAsync(CancellationToken cancellationToken = default);
    Task<MacroAnalysis> GetMacroAnalysisAsync(CancellationToken cancellationToken = default);
}

/// <summary>
/// Configuration for macro filter
/// </summary>
public record MacroFilterConfig(
    string FredApiKey,
    TimeSpan CacheExpiry,
    decimal YieldCurveInversionThreshold,
    decimal UnemploymentRateThreshold,
    decimal FedFundsRateChangeThreshold
);

/// <summary>
/// Macro economic indicators
/// </summary>
public record MacroIndicators(
    decimal YieldCurveSpread,
    decimal UnemploymentRate,
    decimal FedFundsRate,
    DateTime YieldCurveDate,
    DateTime UnemploymentDate,
    DateTime FedFundsDate,
    DateTime LastUpdated
);

/// <summary>
/// Macro analysis result
/// </summary>
public record MacroAnalysis(
    MacroRegime Regime,
    bool IsEligible,
    string Reasoning,
    MacroIndicators? Indicators,
    DateTime AnalyzedAt
);

/// <summary>
/// FRED data point
/// </summary>
public record FredDataPoint(decimal Value, DateTime Date);

/// <summary>
/// Macro economic regime classification
/// </summary>
public enum MacroRegime
{
    Unknown,
    Expansion,
    Neutral,
    Tightening,
    Recession
}

/// <summary>
/// FRED API response structure
/// </summary>
public class FredResponse
{
    [JsonPropertyName("observations")]
    public List<FredObservation>? Observations { get; set; }
}

/// <summary>
/// FRED observation structure
/// </summary>
public class FredObservation
{
    [JsonPropertyName("date")]
    public string Date { get; set; } = string.Empty;

    [JsonPropertyName("value")]
    public string Value { get; set; } = string.Empty;
}
