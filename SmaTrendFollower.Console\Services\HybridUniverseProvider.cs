using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;

namespace SmaTrendFollower.Services;

/// <summary>
/// Hybrid universe provider that can use either static file-based or dynamic filtering approaches
/// </summary>
public sealed class HybridUniverseProvider : IUniverseProvider
{
    private readonly IDynamicUniverseProvider _dynamicProvider;
    private readonly FileUniverseProvider _fileProvider;
    private readonly IConfiguration _configuration;
    private readonly ILogger<HybridUniverseProvider> _logger;
    private readonly bool _useDynamicUniverse;

    public HybridUniverseProvider(
        IDynamicUniverseProvider dynamicProvider,
        IConfiguration configuration,
        ILogger<HybridUniverseProvider> logger)
    {
        _dynamicProvider = dynamicProvider;
        _fileProvider = new FileUniverseProvider();
        _configuration = configuration;
        _logger = logger;
        
        // Check configuration to determine which approach to use
        _useDynamicUniverse = configuration.GetValue<bool>("USE_DYNAMIC_UNIVERSE", true);
        
        _logger.LogInformation("HybridUniverseProvider initialized with {Mode} mode", 
            _useDynamicUniverse ? "dynamic" : "static file");
    }

    public async Task<IEnumerable<string>> GetSymbolsAsync()
    {
        try
        {
            if (_useDynamicUniverse)
            {
                _logger.LogDebug("Using dynamic universe provider");
                return await _dynamicProvider.GetCachedUniverseAsync();
            }
            else
            {
                _logger.LogDebug("Using static file universe provider");
                return await _fileProvider.GetSymbolsAsync();
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting symbols from {Mode} provider, falling back to {Fallback}", 
                _useDynamicUniverse ? "dynamic" : "file", 
                _useDynamicUniverse ? "file" : "dynamic");
            
            // Fallback to the other provider
            try
            {
                if (_useDynamicUniverse)
                {
                    return await _fileProvider.GetSymbolsAsync();
                }
                else
                {
                    return await _dynamicProvider.GetCachedUniverseAsync();
                }
            }
            catch (Exception fallbackEx)
            {
                _logger.LogError(fallbackEx, "Fallback provider also failed, using default symbols");
                return GetDefaultSymbols();
            }
        }
    }

    /// <summary>
    /// Forces a refresh of the dynamic universe (only applicable when using dynamic mode)
    /// </summary>
    public async Task<IEnumerable<string>> RefreshUniverseAsync()
    {
        if (!_useDynamicUniverse)
        {
            _logger.LogWarning("RefreshUniverseAsync called but dynamic universe is disabled");
            return await GetSymbolsAsync();
        }

        try
        {
            _logger.LogInformation("Refreshing dynamic universe");
            return await _dynamicProvider.RefreshUniverseAsync();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error refreshing dynamic universe, falling back to cached version");
            return await _dynamicProvider.GetCachedUniverseAsync();
        }
    }

    /// <summary>
    /// Gets universe details (only applicable when using dynamic mode)
    /// </summary>
    public async Task<Models.RedisUniverse?> GetUniverseDetailsAsync()
    {
        if (!_useDynamicUniverse)
        {
            return null;
        }

        return await _dynamicProvider.GetUniverseDetailsAsync();
    }

    /// <summary>
    /// Checks if the current universe cache is valid (only applicable when using dynamic mode)
    /// </summary>
    public async Task<bool> IsCacheValidAsync()
    {
        if (!_useDynamicUniverse)
        {
            return true; // File-based universe is always "valid"
        }

        return await _dynamicProvider.IsCacheValidAsync();
    }

    /// <summary>
    /// Gets the current mode (dynamic or static)
    /// </summary>
    public bool IsUsingDynamicUniverse => _useDynamicUniverse;

    private static IEnumerable<string> GetDefaultSymbols()
    {
        return new[] { "SPY", "QQQ", "AAPL", "MSFT", "NVDA", "TSLA", "GOOGL", "AMZN", "META", "NFLX" };
    }
}
