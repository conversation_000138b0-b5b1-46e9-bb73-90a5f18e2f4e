#!/usr/bin/env pwsh
# Simple Discord Bot Token Test

Write-Host "Testing Discord Bot Token Configuration..." -ForegroundColor Cyan

# Check if .env file exists
if (-not (Test-Path ".env")) {
    Write-Host "ERROR: .env file not found!" -ForegroundColor Red
    exit 1
}

# Load environment variables
$envContent = Get-Content ".env"
$botToken = $null
$channelId = $null

foreach ($line in $envContent) {
    if ($line -match "^DISCORD_BOT_TOKEN=(.+)$") {
        $botToken = $matches[1].Trim()
    }
    if ($line -match "^DISCORD_CHANNEL_ID=(.+)$") {
        $channelId = $matches[1].Trim()
    }
}

Write-Host "Configuration Check:" -ForegroundColor Yellow
if ($botToken) {
    Write-Host "  Bot Token: CONFIGURED (length: $($botToken.Length))" -ForegroundColor Green
} else {
    Write-Host "  Bot Token: MISSING" -ForegroundColor Red
}

if ($channelId) {
    Write-Host "  Channel ID: $channelId" -ForegroundColor Green
} else {
    Write-Host "  Channel ID: MISSING" -ForegroundColor Red
}

if (-not $botToken -or -not $channelId) {
    Write-Host "ERROR: Discord configuration incomplete!" -ForegroundColor Red
    exit 1
}

# Test bot identity
Write-Host "`nTesting bot identity..." -ForegroundColor Cyan

try {
    $headers = @{
        "Authorization" = "Bot $botToken"
        "Content-Type" = "application/json"
    }
    
    $botInfo = Invoke-RestMethod -Uri "https://discord.com/api/v10/users/@me" -Method Get -Headers $headers
    Write-Host "SUCCESS: Bot authenticated!" -ForegroundColor Green
    Write-Host "  Bot Name: $($botInfo.username)" -ForegroundColor Gray
    Write-Host "  Bot ID: $($botInfo.id)" -ForegroundColor Gray
    
} catch {
    Write-Host "ERROR: Bot authentication failed!" -ForegroundColor Red
    Write-Host "  Error: $($_.Exception.Message)" -ForegroundColor Red
    exit 1
}

# Test sending a message
Write-Host "`nTesting message sending..." -ForegroundColor Cyan

try {
    $testMessage = @{
        embeds = @(
            @{
                title = "SmaTrendFollower Test"
                description = "Testing Discord bot token configuration"
                color = 0x00FF00
                timestamp = (Get-Date).ToUniversalTime().ToString("yyyy-MM-ddTHH:mm:ss.fffZ")
            }
        )
    } | ConvertTo-Json -Depth 10
    
    $apiUrl = "https://discord.com/api/v10/channels/$channelId/messages"
    $response = Invoke-RestMethod -Uri $apiUrl -Method Post -Headers $headers -Body $testMessage
    
    Write-Host "SUCCESS: Test message sent!" -ForegroundColor Green
    Write-Host "  Message ID: $($response.id)" -ForegroundColor Gray
    
} catch {
    Write-Host "ERROR: Message sending failed!" -ForegroundColor Red
    Write-Host "  Error: $($_.Exception.Message)" -ForegroundColor Red
    
    if ($_.Exception.Response) {
        $statusCode = $_.Exception.Response.StatusCode
        Write-Host "  Status Code: $statusCode" -ForegroundColor Red
    }
    exit 1
}

Write-Host "`nSUCCESS: Discord Bot Token configuration is working!" -ForegroundColor Green
Write-Host "Daily Reports are now ACTIVE" -ForegroundColor Green
