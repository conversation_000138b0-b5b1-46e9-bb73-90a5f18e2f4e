using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using System.Diagnostics;
using System.Net;
using System.Text.Json;

namespace SmaTrendFollower.Services;

/// <summary>
/// Monitors API health and connectivity for Alpaca and Polygon services
/// </summary>
public sealed class ApiHealthMonitor : IApiHealthMonitor, IDisposable
{
    private readonly IAlpacaClientFactory _alpacaFactory;
    private readonly IPolygonClientFactory _polygonFactory;
    private readonly ILogger<ApiHealthMonitor> _logger;
    private readonly IConfiguration _configuration;
    private readonly Timer _healthCheckTimer;
    private readonly Dictionary<string, ApiHealthStatus> _lastHealthStatus = new();
    private readonly object _lockObject = new();
    private bool _disposed;

    public event EventHandler<ApiHealthChangedEventArgs>? HealthStatusChanged;

    public ApiHealthMonitor(
        IAlpacaClientFactory alpacaFactory,
        IPolygonClientFactory polygonFactory,
        ILogger<ApiHealthMonitor> logger,
        IConfiguration configuration)
    {
        _alpacaFactory = alpacaFactory;
        _polygonFactory = polygonFactory;
        _logger = logger;
        _configuration = configuration;

        // Initialize health status
        _lastHealthStatus["Alpaca"] = new ApiHealthStatus { ApiName = "Alpaca", IsHealthy = true };
        _lastHealthStatus["Polygon"] = new ApiHealthStatus { ApiName = "Polygon", IsHealthy = true };

        // Start periodic health checks every 5 minutes
        _healthCheckTimer = new Timer(PerformPeriodicHealthCheck, null, TimeSpan.Zero, TimeSpan.FromMinutes(5));
    }

    public async Task<ApiHealthStatus> GetAlpacaHealthAsync(CancellationToken cancellationToken = default)
    {
        var stopwatch = Stopwatch.StartNew();
        var status = new ApiHealthStatus
        {
            ApiName = "Alpaca",
            LastChecked = DateTime.UtcNow
        };

        try
        {
            using var client = _alpacaFactory.CreateTradingClient();
            
            // Simple health check - get account info
            var account = await client.GetAccountAsync(cancellationToken);
            
            stopwatch.Stop();
            status.ResponseTime = stopwatch.Elapsed;
            status.IsHealthy = account != null;
            status.AdditionalMetrics["AccountStatus"] = account?.Status.ToString() ?? "Unknown";
            status.ConsecutiveFailures = 0;

            _logger.LogDebug("Alpaca health check passed in {ResponseTime}ms", status.ResponseTime.TotalMilliseconds);
        }
        catch (Exception ex)
        {
            stopwatch.Stop();
            status.ResponseTime = stopwatch.Elapsed;
            status.IsHealthy = false;
            status.ErrorMessage = ex.Message;
            
            lock (_lockObject)
            {
                if (_lastHealthStatus.TryGetValue("Alpaca", out var lastStatus))
                {
                    status.ConsecutiveFailures = lastStatus.ConsecutiveFailures + 1;
                }
            }

            _logger.LogWarning(ex, "Alpaca health check failed after {ResponseTime}ms", status.ResponseTime.TotalMilliseconds);
        }

        UpdateHealthStatus("Alpaca", status);
        return status;
    }

    public async Task<ApiHealthStatus> GetPolygonHealthAsync(CancellationToken cancellationToken = default)
    {
        var stopwatch = Stopwatch.StartNew();
        var status = new ApiHealthStatus
        {
            ApiName = "Polygon",
            LastChecked = DateTime.UtcNow
        };

        try
        {
            var client = _polygonFactory.CreateClient();
            var apiKey = _configuration["POLY_API_KEY"];

            if (string.IsNullOrEmpty(apiKey))
            {
                throw new InvalidOperationException("Polygon API key not configured");
            }

            // Simple health check - get market status
            var url = $"v1/marketstatus/now?apikey={apiKey}";
            var response = await client.GetAsync(url, cancellationToken);

            stopwatch.Stop();
            status.ResponseTime = stopwatch.Elapsed;

            if (response.IsSuccessStatusCode)
            {
                var content = await response.Content.ReadAsStringAsync(cancellationToken);
                var jsonDoc = JsonDocument.Parse(content);

                status.IsHealthy = true;
                status.ConsecutiveFailures = 0;

                if (jsonDoc.RootElement.TryGetProperty("market", out var market))
                {
                    status.AdditionalMetrics["MarketStatus"] = market.GetString() ?? "Unknown";
                }

                _logger.LogDebug("Polygon health check passed in {ResponseTime}ms", status.ResponseTime.TotalMilliseconds);
            }
            else
            {
                status.IsHealthy = false;
                status.ErrorMessage = $"HTTP {response.StatusCode}: {response.ReasonPhrase}";

                lock (_lockObject)
                {
                    if (_lastHealthStatus.TryGetValue("Polygon", out var lastStatus))
                    {
                        status.ConsecutiveFailures = lastStatus.ConsecutiveFailures + 1;
                    }
                }

                _logger.LogWarning("Polygon health check failed with status {StatusCode}", response.StatusCode);
            }
        }
        catch (Exception ex)
        {
            stopwatch.Stop();
            status.ResponseTime = stopwatch.Elapsed;
            status.IsHealthy = false;
            status.ErrorMessage = ex.Message;
            
            lock (_lockObject)
            {
                if (_lastHealthStatus.TryGetValue("Polygon", out var lastStatus))
                {
                    status.ConsecutiveFailures = lastStatus.ConsecutiveFailures + 1;
                }
            }

            _logger.LogWarning(ex, "Polygon health check failed after {ResponseTime}ms", status.ResponseTime.TotalMilliseconds);
        }

        UpdateHealthStatus("Polygon", status);
        return status;
    }

    public async Task<OverallHealthStatus> GetOverallHealthAsync(CancellationToken cancellationToken = default)
    {
        var alpacaHealth = await GetAlpacaHealthAsync(cancellationToken);
        var polygonHealth = await GetPolygonHealthAsync(cancellationToken);

        var overallStatus = new OverallHealthStatus
        {
            LastChecked = DateTime.UtcNow,
            ApiStatuses = new List<ApiHealthStatus> { alpacaHealth, polygonHealth }
        };

        // Overall health is true if at least one API is healthy (for fallback scenarios)
        overallStatus.IsHealthy = alpacaHealth.IsHealthy || polygonHealth.IsHealthy;

        // Generate summary
        var healthyApis = overallStatus.ApiStatuses.Count(s => s.IsHealthy);
        var totalApis = overallStatus.ApiStatuses.Count;
        
        overallStatus.Summary = healthyApis switch
        {
            0 => "All APIs are down - trading operations may be severely impacted",
            var count when count == totalApis => "All APIs are healthy",
            _ => $"{healthyApis}/{totalApis} APIs are healthy - fallback mechanisms available"
        };

        _logger.LogInformation("Overall API health: {Summary}", overallStatus.Summary);
        return overallStatus;
    }

    private void UpdateHealthStatus(string apiName, ApiHealthStatus newStatus)
    {
        ApiHealthStatus? previousStatus = null;
        
        lock (_lockObject)
        {
            if (_lastHealthStatus.TryGetValue(apiName, out previousStatus))
            {
                // Check if health status changed
                if (previousStatus.IsHealthy != newStatus.IsHealthy)
                {
                    var eventArgs = new ApiHealthChangedEventArgs
                    {
                        ApiName = apiName,
                        WasHealthy = previousStatus.IsHealthy,
                        IsHealthy = newStatus.IsHealthy,
                        ErrorMessage = newStatus.ErrorMessage,
                        Timestamp = DateTime.UtcNow
                    };

                    // Raise event outside of lock
                    Task.Run(() => HealthStatusChanged?.Invoke(this, eventArgs));
                    
                    _logger.LogWarning("API health status changed for {ApiName}: {WasHealthy} -> {IsHealthy}. Error: {Error}",
                        apiName, previousStatus.IsHealthy, newStatus.IsHealthy, newStatus.ErrorMessage);
                }
            }

            _lastHealthStatus[apiName] = newStatus;
        }
    }

    private async void PerformPeriodicHealthCheck(object? state)
    {
        try
        {
            _logger.LogDebug("Performing periodic API health check");
            await GetOverallHealthAsync();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error during periodic health check");
        }
    }

    public void Dispose()
    {
        if (!_disposed)
        {
            _healthCheckTimer?.Dispose();
            _disposed = true;
        }
    }
}
