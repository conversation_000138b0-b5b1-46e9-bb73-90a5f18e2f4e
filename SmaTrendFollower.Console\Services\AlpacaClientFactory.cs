using Alpaca.Markets;
using Microsoft.Extensions.Logging;

namespace SmaTrendFollower.Services;

public sealed class AlpacaClientFactory : IAlpacaClientFactory
{
    private readonly ILogger<AlpacaClientFactory> _logger;
    private readonly AlpacaRateLimitHelper _rateLimitHelper;

    public AlpacaClientFactory(ILogger<AlpacaClientFactory> logger, ILogger<AlpacaRateLimitHelper> rateLimitLogger)
    {
        _logger = logger;
        _rateLimitHelper = new AlpacaRateLimitHelper(rateLimitLogger);
    }

    public IAlpacaTradingClient CreateTradingClient()
    {
        var keyId = Environment.GetEnvironmentVariable("APCA_API_KEY_ID")
            ?? throw new InvalidOperationException("APCA_API_KEY_ID environment variable not set");

        var secretKey = Environment.GetEnvironmentVariable("APCA_API_SECRET_KEY")
            ?? throw new InvalidOperationException("APCA_API_SECRET_KEY environment variable not set");

        var environment = Environment.GetEnvironmentVariable("APCA_API_ENV");
        var isPaper = string.Equals(environment, "paper", StringComparison.OrdinalIgnoreCase);

        _logger.LogInformation("Creating Alpaca trading client for {Environment} environment",
            isPaper ? "paper" : "live");
        _logger.LogInformation("Using API Key ID: {KeyId}", keyId);

        // Use the correct environment based on the setting
        var alpacaEnvironment = isPaper ? Environments.Paper : Environments.Live;
        return alpacaEnvironment.GetAlpacaTradingClient(new SecretKey(keyId, secretKey));
    }

    public IAlpacaDataClient CreateDataClient()
    {
        var keyId = Environment.GetEnvironmentVariable("APCA_API_KEY_ID")
            ?? throw new InvalidOperationException("APCA_API_KEY_ID environment variable not set");

        var secretKey = Environment.GetEnvironmentVariable("APCA_API_SECRET_KEY")
            ?? throw new InvalidOperationException("APCA_API_SECRET_KEY environment variable not set");

        var environment = Environment.GetEnvironmentVariable("APCA_API_ENV");
        var isPaper = string.Equals(environment, "paper", StringComparison.OrdinalIgnoreCase);

        // Use the correct environment based on the setting
        var alpacaEnvironment = isPaper ? Environments.Paper : Environments.Live;
        return alpacaEnvironment.GetAlpacaDataClient(new SecretKey(keyId, secretKey));
    }

    public IAlpacaStreamingClient CreateStreamingClient()
    {
        var keyId = Environment.GetEnvironmentVariable("APCA_API_KEY_ID")
            ?? throw new InvalidOperationException("APCA_API_KEY_ID environment variable not set");

        var secretKey = Environment.GetEnvironmentVariable("APCA_API_SECRET_KEY")
            ?? throw new InvalidOperationException("APCA_API_SECRET_KEY environment variable not set");

        var environment = Environment.GetEnvironmentVariable("APCA_API_ENV");
        var isPaper = string.Equals(environment, "paper", StringComparison.OrdinalIgnoreCase);

        _logger.LogInformation("Creating Alpaca streaming client for {Environment} environment",
            isPaper ? "paper" : "live");

        // Use the correct environment based on the setting
        var alpacaEnvironment = isPaper ? Environments.Paper : Environments.Live;
        return alpacaEnvironment.GetAlpacaStreamingClient(new SecretKey(keyId, secretKey));
    }

    public IAlpacaDataStreamingClient CreateDataStreamingClient()
    {
        var keyId = Environment.GetEnvironmentVariable("APCA_API_KEY_ID")
            ?? throw new InvalidOperationException("APCA_API_KEY_ID environment variable not set");

        var secretKey = Environment.GetEnvironmentVariable("APCA_API_SECRET_KEY")
            ?? throw new InvalidOperationException("APCA_API_SECRET_KEY environment variable not set");

        var environment = Environment.GetEnvironmentVariable("APCA_API_ENV");
        var isPaper = string.Equals(environment, "paper", StringComparison.OrdinalIgnoreCase);

        _logger.LogInformation("Creating Alpaca data streaming client for {Environment} environment",
            isPaper ? "paper" : "live");

        // Use the correct environment based on the setting
        var alpacaEnvironment = isPaper ? Environments.Paper : Environments.Live;
        return alpacaEnvironment.GetAlpacaDataStreamingClient(new SecretKey(keyId, secretKey));
    }

    public IAlpacaRateLimitHelper GetRateLimitHelper() => _rateLimitHelper;

    public void Dispose()
    {
        _rateLimitHelper?.Dispose();
    }
}
