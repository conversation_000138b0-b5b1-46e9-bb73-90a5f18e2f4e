using Alpaca.Markets;
using Microsoft.Extensions.Logging;

namespace SmaTrendFollower.Services;

/// <summary>
/// Filters trading signals based on momentum characteristics.
/// Only allows trades when momentum conditions are favorable.
/// </summary>
public sealed class MomentumFilter : IMomentumFilter
{
    private readonly ILogger<MomentumFilter> _logger;
    private readonly MomentumFilterConfig _config;

    public MomentumFilter(ILogger<MomentumFilter> logger, MomentumFilterConfig? config = null)
    {
        _logger = logger;
        _config = config ?? new MomentumFilterConfig();
    }

    /// <summary>
    /// Determines if a symbol is eligible for trading based on momentum criteria
    /// </summary>
    public bool IsEligible(string symbol, IReadOnlyList<IBar> bars)
    {
        try
        {
            if (bars.Count < _config.MinimumBarsRequired)
            {
                _logger.LogDebug("Insufficient bars for momentum analysis: {Symbol} has {Count} bars, need {Required}",
                    symbol, bars.Count, _config.MinimumBarsRequired);
                return false;
            }

            var analysis = AnalyzeMomentum(symbol, bars);
            
            var isEligible = analysis.SixMonthReturn >= _config.MinimumSixMonthReturn &&
                           analysis.ThreeMonthReturn >= _config.MinimumThreeMonthReturn &&
                           analysis.OneMonthReturn >= _config.MinimumOneMonthReturn &&
                           analysis.RelativeStrength >= _config.MinimumRelativeStrength &&
                           analysis.MomentumScore >= _config.MinimumMomentumScore;

            if (isEligible)
            {
                _logger.LogDebug("Momentum filter PASSED for {Symbol}: 6M={SixMonth:F1}%, 3M={ThreeMonth:F1}%, 1M={OneMonth:F1}%, RS={RS:F2}, Score={Score:F2}",
                    symbol, analysis.SixMonthReturn * 100, analysis.ThreeMonthReturn * 100, 
                    analysis.OneMonthReturn * 100, analysis.RelativeStrength, analysis.MomentumScore);
            }
            else
            {
                _logger.LogDebug("Momentum filter FAILED for {Symbol}: 6M={SixMonth:F1}%, 3M={ThreeMonth:F1}%, 1M={OneMonth:F1}%, RS={RS:F2}, Score={Score:F2}",
                    symbol, analysis.SixMonthReturn * 100, analysis.ThreeMonthReturn * 100, 
                    analysis.OneMonthReturn * 100, analysis.RelativeStrength, analysis.MomentumScore);
            }

            return isEligible;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error analyzing momentum for {Symbol}", symbol);
            return false;
        }
    }

    /// <summary>
    /// Performs comprehensive momentum analysis on price data
    /// </summary>
    public MomentumAnalysis AnalyzeMomentum(string symbol, IReadOnlyList<IBar> bars)
    {
        var currentPrice = bars.Last().Close;
        
        // Calculate returns over different periods
        var sixMonthReturn = CalculateReturn(bars, 126); // ~6 months
        var threeMonthReturn = CalculateReturn(bars, 63); // ~3 months
        var oneMonthReturn = CalculateReturn(bars, 21);   // ~1 month
        var twoWeekReturn = CalculateReturn(bars, 10);    // ~2 weeks

        // Calculate relative strength (price vs moving average)
        var sma50 = CalculateSMA(bars, 50);
        var sma200 = CalculateSMA(bars, 200);
        var relativeStrength = sma50 > 0 ? currentPrice / sma50 : 0;

        // Calculate momentum score (weighted combination)
        var momentumScore = CalculateMomentumScore(sixMonthReturn, threeMonthReturn, oneMonthReturn, twoWeekReturn);

        // Calculate trend consistency
        var trendConsistency = CalculateTrendConsistency(bars, 20);

        // Calculate volume momentum
        var volumeMomentum = CalculateVolumeMomentum(bars, 20);

        return new MomentumAnalysis(
            symbol,
            currentPrice,
            sixMonthReturn,
            threeMonthReturn,
            oneMonthReturn,
            twoWeekReturn,
            relativeStrength,
            momentumScore,
            trendConsistency,
            volumeMomentum,
            sma50,
            sma200,
            DateTime.UtcNow
        );
    }

    /// <summary>
    /// Calculates return over specified number of periods
    /// </summary>
    private decimal CalculateReturn(IReadOnlyList<IBar> bars, int periods)
    {
        if (bars.Count <= periods) return 0;

        var currentPrice = bars.Last().Close;
        var pastPrice = bars[bars.Count - 1 - periods].Close;
        
        return pastPrice > 0 ? (currentPrice - pastPrice) / pastPrice : 0;
    }

    /// <summary>
    /// Calculates Simple Moving Average
    /// </summary>
    private decimal CalculateSMA(IReadOnlyList<IBar> bars, int periods)
    {
        if (bars.Count < periods) return 0;

        var sum = 0m;
        for (int i = bars.Count - periods; i < bars.Count; i++)
        {
            sum += bars[i].Close;
        }

        return sum / periods;
    }

    /// <summary>
    /// Calculates weighted momentum score
    /// </summary>
    private decimal CalculateMomentumScore(decimal sixMonth, decimal threeMonth, decimal oneMonth, decimal twoWeek)
    {
        // Weighted scoring: longer-term momentum weighted more heavily
        return (sixMonth * 0.4m) + (threeMonth * 0.3m) + (oneMonth * 0.2m) + (twoWeek * 0.1m);
    }

    /// <summary>
    /// Calculates trend consistency (percentage of up days)
    /// </summary>
    private decimal CalculateTrendConsistency(IReadOnlyList<IBar> bars, int periods)
    {
        if (bars.Count <= periods) return 0;

        var upDays = 0;
        for (int i = bars.Count - periods; i < bars.Count - 1; i++)
        {
            if (bars[i + 1].Close > bars[i].Close)
                upDays++;
        }

        return (decimal)upDays / (periods - 1);
    }

    /// <summary>
    /// Calculates volume momentum (current vs average volume)
    /// </summary>
    private decimal CalculateVolumeMomentum(IReadOnlyList<IBar> bars, int periods)
    {
        if (bars.Count <= periods) return 1;

        var currentVolume = bars.Last().Volume;
        var avgVolume = 0m;
        
        for (int i = bars.Count - periods; i < bars.Count - 1; i++)
        {
            avgVolume += bars[i].Volume;
        }
        
        avgVolume /= (periods - 1);
        
        return avgVolume > 0 ? currentVolume / avgVolume : 1;
    }
}

/// <summary>
/// Interface for momentum filtering
/// </summary>
public interface IMomentumFilter
{
    bool IsEligible(string symbol, IReadOnlyList<IBar> bars);
    MomentumAnalysis AnalyzeMomentum(string symbol, IReadOnlyList<IBar> bars);
}

/// <summary>
/// Configuration for momentum filter
/// </summary>
public record MomentumFilterConfig(
    decimal MinimumSixMonthReturn = 0.05m,    // 5% minimum 6-month return
    decimal MinimumThreeMonthReturn = 0.02m,  // 2% minimum 3-month return
    decimal MinimumOneMonthReturn = -0.05m,   // Allow -5% 1-month return (temporary pullbacks)
    decimal MinimumRelativeStrength = 1.02m,  // Price must be 2% above SMA50
    decimal MinimumMomentumScore = 0.03m,     // 3% minimum weighted momentum score
    int MinimumBarsRequired = 200             // Need 200+ bars for analysis
);

/// <summary>
/// Comprehensive momentum analysis results
/// </summary>
public record MomentumAnalysis(
    string Symbol,
    decimal CurrentPrice,
    decimal SixMonthReturn,
    decimal ThreeMonthReturn,
    decimal OneMonthReturn,
    decimal TwoWeekReturn,
    decimal RelativeStrength,
    decimal MomentumScore,
    decimal TrendConsistency,
    decimal VolumeMomentum,
    decimal SMA50,
    decimal SMA200,
    DateTime AnalysisTime
);
