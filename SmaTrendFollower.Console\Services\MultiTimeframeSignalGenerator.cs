using Microsoft.Extensions.Logging;
using SmaTrendFollower.Models;

namespace SmaTrendFollower.Services;

/// <summary>
/// Timeframe enumeration for multi-timeframe analysis
/// </summary>
public enum Timeframe
{
    Daily,
    Weekly,
    Monthly,
    Hourly,
    FifteenMinute
}

/// <summary>
/// Multi-timeframe signal generator for comprehensive trend analysis
/// </summary>
public sealed class MultiTimeframeSignalGenerator
{
    private readonly ILogger<MultiTimeframeSignalGenerator> _logger;
    private readonly IMarketDataService _marketDataService;
    private readonly ISignalGenerator _signalGenerator;
    private readonly Random _random;

    public MultiTimeframeSignalGenerator(
        ILogger<MultiTimeframeSignalGenerator> logger,
        IMarketDataService marketDataService,
        ISignalGenerator signalGenerator)
    {
        _logger = logger;
        _marketDataService = marketDataService;
        _signalGenerator = signalGenerator;
        _random = new Random(42); // Fixed seed for reproducibility
    }

    /// <summary>
    /// Generates signals across multiple timeframes for enhanced analysis
    /// </summary>
    public async Task<IEnumerable<MultiTimeframeSignal>> GenerateSignalsAsync(IEnumerable<string> symbols)
    {
        var symbolsList = symbols.ToList();
        _logger.LogInformation("Generating multi-timeframe signals for {Count} symbols", symbolsList.Count);

        try
        {
            var signals = new List<MultiTimeframeSignal>();

            foreach (var symbol in symbolsList)
            {
                var multiTimeframeSignal = await AnalyzeSymbolAcrossTimeframes(symbol);
                if (multiTimeframeSignal != null)
                {
                    signals.Add(multiTimeframeSignal);
                }
            }

            // Sort by overall strength descending
            var sortedSignals = signals.OrderByDescending(s => s.OverallStrength).ToList();

            _logger.LogInformation("Generated {Count} multi-timeframe signals", sortedSignals.Count);
            return sortedSignals;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Multi-timeframe signal generation failed");
            throw;
        }
    }

    /// <summary>
    /// Runs multi-timeframe analysis and returns top signals
    /// </summary>
    public async Task<IEnumerable<TradingSignal>> RunAsync(int maxSignals = 10)
    {
        _logger.LogInformation("Running multi-timeframe analysis for top {Count} signals", maxSignals);

        try
        {
            // Get base signals from the primary signal generator
            var baseSignals = await _signalGenerator.RunAsync(maxSignals * 2); // Get more to filter
            var symbols = baseSignals.Select(s => s.Symbol).ToList();

            // Generate multi-timeframe analysis
            var multiTimeframeSignals = await GenerateSignalsAsync(symbols);

            // Convert back to TradingSignal format with enhanced data
            var enhancedSignals = multiTimeframeSignals
                .Take(maxSignals)
                .Select(mts => new TradingSignal(
                    mts.Symbol,
                    mts.Price,
                    mts.Atr,
                    mts.SixMonthReturn * (1 + mts.OverallStrength * 0.1m) // Boost return based on strength
                ))
                .ToList();

            _logger.LogInformation("Multi-timeframe analysis completed, returning {Count} enhanced signals", 
                enhancedSignals.Count);

            return enhancedSignals;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Multi-timeframe analysis run failed");
            throw;
        }
    }

    private async Task<MultiTimeframeSignal?> AnalyzeSymbolAcrossTimeframes(string symbol)
    {
        try
        {
            _logger.LogDebug("Analyzing {Symbol} across multiple timeframes", symbol);

            // Simulate multi-timeframe analysis
            await Task.Delay(50); // Simulate data retrieval and analysis time

            var timeframeAnalyses = new List<TimeframeAnalysis>();

            // Analyze each timeframe
            foreach (var timeframe in Enum.GetValues<Timeframe>())
            {
                var analysis = await AnalyzeTimeframe(symbol, timeframe);
                timeframeAnalyses.Add(analysis);
            }

            // Calculate overall metrics
            var overallStrength = CalculateOverallStrength(timeframeAnalyses);
            var confidence = CalculateConfidence(timeframeAnalyses);
            var trendAlignment = CalculateTrendAlignment(timeframeAnalyses);

            // Get current price data (simulated)
            var price = 50m + (decimal)(_random.NextDouble() * 200); // $50-$250
            var atr = price * (0.02m + (decimal)(_random.NextDouble() * 0.03)); // 2-5% ATR
            var sixMonthReturn = -0.2m + (decimal)(_random.NextDouble() * 0.6); // -20% to +40%

            var signal = new MultiTimeframeSignal(
                symbol,
                price,
                atr,
                sixMonthReturn,
                timeframeAnalyses,
                overallStrength,
                confidence,
                trendAlignment,
                DateTime.UtcNow
            );

            _logger.LogDebug("Multi-timeframe analysis for {Symbol}: Strength={Strength:F2}, " +
                           "Confidence={Confidence:F2}, Alignment={Alignment:F2}",
                symbol, overallStrength, confidence, trendAlignment);

            return signal;
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "Failed to analyze {Symbol} across timeframes", symbol);
            return null;
        }
    }

    private async Task<TimeframeAnalysis> AnalyzeTimeframe(string symbol, Timeframe timeframe)
    {
        // Simulate timeframe-specific analysis
        await Task.Delay(10);

        var trendDirection = _random.NextDouble() > 0.5 ? TrendDirection.Up : TrendDirection.Down;
        var strength = (decimal)(_random.NextDouble()); // 0-1 strength
        var momentum = -0.5m + (decimal)(_random.NextDouble()); // -0.5 to +0.5 momentum
        var volatility = 0.1m + (decimal)(_random.NextDouble() * 0.3); // 10-40% volatility
        var volume = 0.5m + (decimal)(_random.NextDouble()); // 0.5-1.5 volume ratio

        // Adjust strength based on timeframe (longer timeframes get higher weight)
        var timeframeWeight = timeframe switch
        {
            Timeframe.Monthly => 1.0m,
            Timeframe.Weekly => 0.8m,
            Timeframe.Daily => 0.6m,
            Timeframe.Hourly => 0.4m,
            Timeframe.FifteenMinute => 0.2m,
            _ => 0.5m
        };

        strength *= timeframeWeight;

        return new TimeframeAnalysis(
            timeframe,
            trendDirection,
            strength,
            momentum,
            volatility,
            volume,
            DateTime.UtcNow
        );
    }

    private decimal CalculateOverallStrength(List<TimeframeAnalysis> analyses)
    {
        if (!analyses.Any()) return 0;

        // Weight longer timeframes more heavily
        var weightedStrength = 0m;
        var totalWeight = 0m;

        foreach (var analysis in analyses)
        {
            var weight = analysis.Timeframe switch
            {
                Timeframe.Monthly => 3.0m,
                Timeframe.Weekly => 2.5m,
                Timeframe.Daily => 2.0m,
                Timeframe.Hourly => 1.0m,
                Timeframe.FifteenMinute => 0.5m,
                _ => 1.0m
            };

            weightedStrength += analysis.Strength * weight;
            totalWeight += weight;
        }

        return totalWeight > 0 ? weightedStrength / totalWeight : 0;
    }

    private decimal CalculateConfidence(List<TimeframeAnalysis> analyses)
    {
        if (!analyses.Any()) return 0;

        // Confidence based on consistency across timeframes
        var upTrends = analyses.Count(a => a.TrendDirection == TrendDirection.Up);
        var totalAnalyses = analyses.Count;
        
        var directionConsistency = Math.Max(upTrends, totalAnalyses - upTrends) / (decimal)totalAnalyses;
        
        // Also consider strength consistency
        var avgStrength = analyses.Average(a => a.Strength);
        var strengthVariance = analyses.Average(a => Math.Abs(a.Strength - avgStrength));
        var strengthConsistency = Math.Max(0, 1 - strengthVariance * 2);

        return (directionConsistency * 0.7m) + (strengthConsistency * 0.3m);
    }

    private decimal CalculateTrendAlignment(List<TimeframeAnalysis> analyses)
    {
        if (!analyses.Any()) return 0;

        // Calculate how well trends align across timeframes
        var upTrends = analyses.Count(a => a.TrendDirection == TrendDirection.Up);
        var downTrends = analyses.Count(a => a.TrendDirection == TrendDirection.Down);
        var totalAnalyses = analyses.Count;

        // Perfect alignment = all same direction
        var maxAlignment = Math.Max(upTrends, downTrends);
        return maxAlignment / (decimal)totalAnalyses;
    }
}

/// <summary>
/// Trend direction enumeration
/// </summary>
public enum TrendDirection
{
    Up,
    Down,
    Sideways
}

/// <summary>
/// Multi-timeframe signal with comprehensive analysis
/// </summary>
public record MultiTimeframeSignal(
    string Symbol,
    decimal Price,
    decimal Atr,
    decimal SixMonthReturn,
    List<TimeframeAnalysis> TimeframeAnalyses,
    decimal OverallStrength,
    decimal Confidence,
    decimal TrendAlignment,
    DateTime GeneratedAt
);

/// <summary>
/// Analysis for a specific timeframe
/// </summary>
public record TimeframeAnalysis(
    Timeframe Timeframe,
    TrendDirection TrendDirection,
    decimal Strength,
    decimal Momentum,
    decimal Volatility,
    decimal VolumeRatio,
    DateTime AnalyzedAt
);
