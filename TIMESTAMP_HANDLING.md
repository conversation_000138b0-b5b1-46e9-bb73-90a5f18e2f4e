# Timestamp Handling and Clock Alignment

## Overview

The SmaTrendFollower system integrates data from multiple sources (Alpaca Markets and Polygon.io) that use different timestamp formats. This document outlines how we ensure proper clock alignment and timezone consistency across all data sources.

## Timestamp Formats by Data Source

### Alpaca Markets
- **Format**: `DateTime` with `DateTimeKind.Utc`
- **Interface**: `IBar.TimeUtc` property
- **Usage**: Stock/ETF bars, account data, positions, orders
- **Example**: `2022-01-01T15:30:00.000Z`

### Polygon.io
- **Format**: Milliseconds since Unix epoch (UTC)
- **Raw Value**: `long` (e.g., `*************`)
- **Usage**: Index data (SPX, VIX), stock bars (fallback), options data
- **Example**: `*************` = December 31, 2021 14:30:00 UTC

## Clock Alignment Strategy

### Critical Conversion Process

All Polygon timestamps are converted using this exact pattern:

```csharp
// CRITICAL: Always convert Polygon timestamps through DateTimeOffset first
var timestampMs = timestampElement.GetInt64();
var timestamp = DateTimeOffset.FromUnixTimeMilliseconds(timestampMs).UtcDateTime;
```

### Why DateTimeOffset First?

1. **Precision**: `DateTimeOffset.FromUnixTimeMilliseconds()` handles leap seconds and timezone edge cases correctly
2. **Consistency**: Ensures all timestamps are properly normalized to UTC
3. **Compatibility**: Results in `DateTime` with `DateTimeKind.Utc` that matches Alpaca's format

## Implementation Details

### MarketDataService.cs

#### TryParsePolygonBar Method
```csharp
private static bool TryParsePolygonBar(JsonElement barElement, string symbol, out PolygonBar polygonBar)
{
    // Convert Polygon timestamp (milliseconds since epoch, UTC) to DateTimeOffset first,
    // then to UTC DateTime to ensure proper timezone alignment with Alpaca data
    var timestampMs = timestampElement.GetInt64();
    var timestamp = DateTimeOffset.FromUnixTimeMilliseconds(timestampMs).UtcDateTime;
    
    polygonBar = new PolygonBar(symbol, timestamp, open, high, low, close, volume);
    return true;
}
```

#### TryParseIndexBar Method
```csharp
private static bool TryParseIndexBar(JsonElement barElement, out IndexBar indexBar)
{
    // CRITICAL: Convert Polygon timestamp (milliseconds since epoch, UTC) to DateTimeOffset first,
    // then to UTC DateTime to ensure proper timezone alignment with Alpaca data
    var timestampMs = timestampElement.GetInt64();
    var timestamp = DateTimeOffset.FromUnixTimeMilliseconds(timestampMs).UtcDateTime;
    
    indexBar = new IndexBar(timestamp, open, high, low, close, volume);
    return true;
}
```

### PolygonModels.cs

#### PolygonBar Record
```csharp
/// <summary>
/// Represents a bar from Polygon API with proper timezone handling.
/// TimeUtc is converted from Polygon's milliseconds-since-epoch format to DateTime UTC
/// to ensure consistency with Alpaca bar timestamps when mixing data sources.
/// </summary>
public readonly record struct PolygonBar(
    string Symbol,
    DateTime TimeUtc, // Always UTC, converted from Polygon's milliseconds since epoch
    decimal Open,
    decimal High,
    decimal Low,
    decimal Close,
    long Volume
);
```

## Testing Strategy

### Timestamp Conversion Tests

We have comprehensive tests to ensure clock alignment:

1. **Basic Conversion Test**: Verifies milliseconds-to-DateTime conversion accuracy
2. **Market Hours Test**: Tests conversion during actual trading hours
3. **Consistency Test**: Ensures Polygon and Alpaca bars have identical timestamps

### Example Test Cases

```csharp
[Fact]
public void PolygonTimestampConversion_ShouldConvertCorrectly()
{
    // Test specific timestamp: January 1, 2022 00:00:00 UTC
    var polygonTimestampMs = 1640995200000L;
    var expectedUtcDateTime = new DateTime(2022, 1, 1, 0, 0, 0, DateTimeKind.Utc);
    
    var convertedDateTime = DateTimeOffset.FromUnixTimeMilliseconds(polygonTimestampMs).UtcDateTime;
    
    convertedDateTime.Should().Be(expectedUtcDateTime);
    convertedDateTime.Kind.Should().Be(DateTimeKind.Utc);
}
```

## Data Mixing Scenarios

### Scenario 1: Alpaca Primary, Polygon Fallback
When Alpaca throttles minute bar requests, we fall back to Polygon:

```csharp
// Primary: Alpaca bars (already UTC)
var alpacaBars = await dataClient.ListHistoricalBarsAsync(request);

// Fallback: Polygon bars (converted to UTC)
var polygonBars = await GetStockBarsFromPolygonFallbackAsync(symbol, startDate, endDate);

// Both return IPage<IBar> with consistent UTC timestamps
```

### Scenario 2: Mixed Data Analysis
When analyzing signals that combine stock data (Alpaca) and index data (Polygon):

```csharp
// Stock data from Alpaca (UTC)
var stockBars = await marketDataService.GetStockBarsAsync("AAPL", startDate, endDate);

// Index data from Polygon (converted to UTC)
var spyBars = await marketDataService.GetStockBarsAsync("SPY", startDate, endDate);
var vixBars = await marketDataService.GetIndexBarsAsync("I:VIX", startDate, endDate);

// All timestamps are now consistently in UTC and can be safely compared
```

## Best Practices

### 1. Always Use UTC
- All internal calculations use UTC timestamps
- Convert to local time only for display purposes
- Never mix local time with UTC in calculations

### 2. Consistent Conversion Pattern
- Always use `DateTimeOffset.FromUnixTimeMilliseconds()` for Polygon data
- Never use `DateTime.FromBinary()` or manual epoch calculations
- Document timezone handling in all timestamp-related methods

### 3. Testing Requirements
- Test timestamp conversion with known values
- Verify timezone consistency between data sources
- Test edge cases (market holidays, daylight saving transitions)

### 4. Error Handling
- Catch specific exceptions: `JsonException`, `FormatException`, `OverflowException`
- Log timestamp parsing errors for debugging
- Fail gracefully when timestamp conversion fails

## Validation

### Build and Test Status
- ✅ All timestamp conversion tests pass
- ✅ No timezone-related compilation warnings
- ✅ Consistent UTC handling across all data sources
- ✅ Proper error handling for malformed timestamps

### Manual Verification
To manually verify timestamp alignment:

1. Run the application with both Alpaca and Polygon data
2. Compare timestamps in logs for the same time period
3. Verify all timestamps show UTC timezone
4. Check that data from different sources aligns correctly

## Troubleshooting

### Common Issues

1. **Timestamp Mismatch**: If timestamps don't align, verify the conversion uses `DateTimeOffset.FromUnixTimeMilliseconds()`
2. **Timezone Confusion**: Ensure all `DateTime` objects have `DateTimeKind.Utc`
3. **Precision Loss**: Use `long` for millisecond timestamps, not `int`

### Debug Commands

```csharp
// Log timestamp details for debugging
_logger.LogDebug("Polygon timestamp: {TimestampMs}ms -> {ConvertedUtc:yyyy-MM-dd HH:mm:ss.fff} UTC", 
    timestampMs, convertedDateTime);
```

This ensures all timestamp handling maintains proper clock alignment between Alpaca and Polygon data sources.
