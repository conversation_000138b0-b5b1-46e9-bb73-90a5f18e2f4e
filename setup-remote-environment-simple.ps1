#!/usr/bin/env pwsh
<#
.SYNOPSIS
    Simple setup script for SmaTrendFollower remote environment
.DESCRIPTION
    This script prepares the remote environment for Augment Agent
.PARAMETER SkipTests
    Skip running tests during setup
.EXAMPLE
    .\setup-remote-environment-simple.ps1
    .\setup-remote-environment-simple.ps1 -SkipTests
#>

param(
    [switch]$SkipTests
)

$ErrorActionPreference = "Stop"

Write-Host "Setting up SmaTrendFollower remote environment for Augment Agent..." -ForegroundColor Green

# Function to check if command exists
function Test-Command {
    param([string]$Command)
    try {
        Get-Command $Command -ErrorAction Stop | Out-Null
        return $true
    }
    catch {
        return $false
    }
}

Write-Host "`nChecking Prerequisites..." -ForegroundColor Yellow

# Check .NET 8 SDK
if (-not (Test-Command "dotnet")) {
    Write-Host "ERROR: .NET SDK not found. Please install .NET 8 SDK" -ForegroundColor Red
    exit 1
}

$dotnetVersion = dotnet --version
Write-Host "SUCCESS: .NET SDK version: $dotnetVersion" -ForegroundColor Green

Write-Host "`nValidating Project Structure..." -ForegroundColor Yellow

$requiredFiles = @(
    "SmaTrendFollower.sln",
    "SmaTrendFollower.Console/SmaTrendFollower.Console.csproj",
    "SmaTrendFollower.Tests/SmaTrendFollower.Tests.csproj"
)

foreach ($file in $requiredFiles) {
    if (Test-Path $file) {
        Write-Host "SUCCESS: Found $file" -ForegroundColor Green
    } else {
        Write-Host "ERROR: Missing $file" -ForegroundColor Red
        exit 1
    }
}

Write-Host "`nCreating Required Directories..." -ForegroundColor Yellow

$directories = @("logs", "data", "cache")
foreach ($dir in $directories) {
    if (-not (Test-Path $dir)) {
        New-Item -ItemType Directory -Path $dir -Force | Out-Null
        Write-Host "SUCCESS: Created directory: $dir" -ForegroundColor Green
    } else {
        Write-Host "SUCCESS: Directory exists: $dir" -ForegroundColor Green
    }
}

Write-Host "`nSetting up Environment Variables..." -ForegroundColor Yellow

if (Test-Path ".env") {
    Write-Host "SUCCESS: .env file found" -ForegroundColor Green
} else {
    Write-Host "WARNING: .env file not found. Creating template..." -ForegroundColor Yellow
    $envTemplate = @"
# Alpaca API Configuration
APCA_API_KEY_ID=your_alpaca_key_here
APCA_API_SECRET_KEY=your_alpaca_secret_here
APCA_API_ENV=paper

# Polygon API Configuration
POLY_API_KEY=your_polygon_key_here

# Trading Configuration (optional)
# MAX_POSITION_SIZE_PERCENT=0.05
# STOP_LOSS_PERCENT=0.02
# TAKE_PROFIT_PERCENT=0.06
"@
    $envTemplate | Out-File -FilePath ".env" -Encoding UTF8
    Write-Host "SUCCESS: Created .env template. Please configure with your API keys." -ForegroundColor Yellow
}

Write-Host "`nCleaning Previous Builds..." -ForegroundColor Yellow
try {
    dotnet clean | Out-Null
    Write-Host "SUCCESS: Cleaned solution" -ForegroundColor Green
} catch {
    Write-Host "WARNING: Clean failed: $($_.Exception.Message)" -ForegroundColor Yellow
}

Write-Host "`nRestoring NuGet Packages..." -ForegroundColor Yellow
try {
    dotnet restore
    Write-Host "SUCCESS: Restored NuGet packages" -ForegroundColor Green
} catch {
    Write-Host "ERROR: Package restore failed: $($_.Exception.Message)" -ForegroundColor Red
    exit 1
}

Write-Host "`nBuilding Solution..." -ForegroundColor Yellow
try {
    dotnet build --configuration Release --no-restore
    Write-Host "SUCCESS: Built solution in Release mode" -ForegroundColor Green
} catch {
    Write-Host "ERROR: Build failed: $($_.Exception.Message)" -ForegroundColor Red
    exit 1
}

if (-not $SkipTests) {
    Write-Host "`nRunning Tests..." -ForegroundColor Yellow
    try {
        dotnet test --configuration Release --no-build --verbosity normal
        Write-Host "SUCCESS: Tests completed" -ForegroundColor Green
    } catch {
        Write-Host "WARNING: Some tests may have failed: $($_.Exception.Message)" -ForegroundColor Yellow
    }
} else {
    Write-Host "`nSkipping tests as requested" -ForegroundColor Yellow
}

Write-Host "`nValidating Setup..." -ForegroundColor Yellow

$consoleExe = "SmaTrendFollower.Console/bin/Release/net8.0/SmaTrendFollower.Console.dll"
if (Test-Path $consoleExe) {
    Write-Host "SUCCESS: Console application built successfully" -ForegroundColor Green
} else {
    Write-Host "ERROR: Console application build failed" -ForegroundColor Red
}

Write-Host "`nSetup Summary" -ForegroundColor Magenta
Write-Host "=============" -ForegroundColor Magenta
Write-Host "SUCCESS: .NET SDK Available" -ForegroundColor Green
Write-Host "SUCCESS: Project Structure Valid" -ForegroundColor Green
Write-Host "SUCCESS: Dependencies Restored" -ForegroundColor Green
Write-Host "SUCCESS: Build Successful" -ForegroundColor Green
Write-Host "SUCCESS: Directories Created" -ForegroundColor Green

if (-not $SkipTests) {
    Write-Host "SUCCESS: Tests Executed" -ForegroundColor Green
}

Write-Host "`nRemote environment setup completed successfully!" -ForegroundColor Green
Write-Host "`nAvailable Commands for Augment Agent:" -ForegroundColor Cyan
Write-Host "  Build:           dotnet build" -ForegroundColor White
Write-Host "  Test:            dotnet test" -ForegroundColor White
Write-Host "  Run Console:     dotnet run --project SmaTrendFollower.Console" -ForegroundColor White
Write-Host "  Clean:           dotnet clean" -ForegroundColor White
Write-Host "  Restore:         dotnet restore" -ForegroundColor White

Write-Host "`nProject Structure:" -ForegroundColor Cyan
Write-Host "  SmaTrendFollower.Console/    - Main trading application" -ForegroundColor White
Write-Host "  SmaTrendFollower.Tests/      - Unit and integration tests" -ForegroundColor White
Write-Host "  logs/                        - Application logs" -ForegroundColor White
Write-Host "  data/                        - Data files" -ForegroundColor White
Write-Host "  cache/                       - Cache files" -ForegroundColor White

Write-Host "`nEnvironment Configuration:" -ForegroundColor Cyan
Write-Host "  .env file contains API keys and configuration" -ForegroundColor White
Write-Host "  Ensure API keys are properly configured before trading" -ForegroundColor White

Write-Host "`nImportant Notes:" -ForegroundColor Yellow
Write-Host "  This is a live trading system - use with caution" -ForegroundColor Red
Write-Host "  Always test changes thoroughly before deployment" -ForegroundColor Yellow
Write-Host "  Monitor logs for any issues during operation" -ForegroundColor Yellow

Write-Host "`nEnvironment is ready for Augment Agent!" -ForegroundColor Green
