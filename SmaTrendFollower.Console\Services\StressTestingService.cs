using Microsoft.Extensions.Logging;
using SmaTrendFollower.Models;

namespace SmaTrendFollower.Services;

/// <summary>
/// Interface for stress testing and risk analysis
/// </summary>
public interface IStressTestingService
{
    /// <summary>
    /// Performs comprehensive stress testing on a portfolio
    /// </summary>
    Task<StressTestResults> PerformStressTestAsync(OptimizedPortfolio portfolio);

    /// <summary>
    /// Runs Monte Carlo simulation for portfolio performance
    /// </summary>
    Task<MonteCarloResults> RunMonteCarloSimulationAsync(
        OptimizedPortfolio portfolio, 
        int simulations = 1000, 
        int tradingDays = 252);

    /// <summary>
    /// Analyzes portfolio performance under historical crisis scenarios
    /// </summary>
    Task<CrisisAnalysisResults> AnalyzeCrisisScenariosAsync(OptimizedPortfolio portfolio);

    /// <summary>
    /// Calculates tail risk metrics (VaR, Expected Shortfall, etc.)
    /// </summary>
    Task<TailRiskMetrics> CalculateTailRiskAsync(OptimizedPortfolio portfolio);

    /// <summary>
    /// Performs scenario analysis with custom market conditions
    /// </summary>
    Task<ScenarioAnalysisResults> PerformScenarioAnalysisAsync(
        OptimizedPortfolio portfolio, 
        List<MarketScenario> scenarios);
}

/// <summary>
/// Stress testing service for portfolio risk analysis
/// </summary>
public sealed class StressTestingService : IStressTestingService
{
    private readonly ILogger<StressTestingService> _logger;
    private readonly Random _random;
    private readonly List<HistoricalCrisis> _historicalCrises;

    public StressTestingService(ILogger<StressTestingService> logger)
    {
        _logger = logger;
        _random = new Random(42); // Fixed seed for reproducibility
        _historicalCrises = InitializeHistoricalCrises();
    }

    public async Task<StressTestResults> PerformStressTestAsync(OptimizedPortfolio portfolio)
    {
        _logger.LogInformation("Performing stress test on portfolio with {Count} positions", portfolio.Positions.Count);

        try
        {
            await Task.Delay(800); // Simulate computation time

            var scenarios = GenerateStressScenarios();
            var scenarioResults = new List<StressScenarioResult>();

            foreach (var scenario in scenarios)
            {
                var portfolioReturn = CalculatePortfolioReturnUnderStress(portfolio, scenario);
                scenarioResults.Add(new StressScenarioResult(
                    scenario.Name,
                    scenario.Description,
                    portfolioReturn,
                    scenario.Probability
                ));
            }

            var worstCase = scenarioResults.OrderBy(r => r.PortfolioReturn).First();
            var averageStressReturn = scenarioResults.Average(r => r.PortfolioReturn);
            var stressVaR = scenarioResults.OrderBy(r => r.PortfolioReturn).Skip((int)(scenarioResults.Count * 0.05)).First().PortfolioReturn;

            var results = new StressTestResults(
                scenarioResults,
                worstCase,
                averageStressReturn,
                stressVaR,
                DateTime.UtcNow
            );

            _logger.LogInformation("Stress test completed. Worst case: {WorstCase:P2}, Average stress return: {Average:P2}",
                worstCase.PortfolioReturn, averageStressReturn);

            return results;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Stress testing failed");
            throw;
        }
    }

    public async Task<MonteCarloResults> RunMonteCarloSimulationAsync(
        OptimizedPortfolio portfolio, 
        int simulations = 1000, 
        int tradingDays = 252)
    {
        _logger.LogInformation("Running Monte Carlo simulation: {Simulations} simulations over {Days} trading days", 
            simulations, tradingDays);

        try
        {
            await Task.Delay(1500); // Simulate computation time

            var simulationResults = new List<decimal>();
            var maxDrawdowns = new List<decimal>();

            for (int sim = 0; sim < simulations; sim++)
            {
                var dailyReturns = GenerateDailyReturns(portfolio, tradingDays);
                var cumulativeReturn = dailyReturns.Aggregate(1m, (acc, ret) => acc * (1 + ret)) - 1;
                var maxDrawdown = CalculateMaxDrawdown(dailyReturns);

                simulationResults.Add(cumulativeReturn);
                maxDrawdowns.Add(maxDrawdown);
            }

            var sortedReturns = simulationResults.OrderBy(r => r).ToArray();
            var meanReturn = simulationResults.Average();
            var percentile5 = sortedReturns[(int)(simulations * 0.05)];
            var percentile95 = sortedReturns[(int)(simulations * 0.95)];
            var probabilityOfLoss = (decimal)simulationResults.Count(r => r < 0) / simulations;
            var averageMaxDrawdown = maxDrawdowns.Average();

            var results = new MonteCarloResults(
                simulations,
                tradingDays,
                meanReturn,
                percentile5,
                percentile95,
                probabilityOfLoss,
                averageMaxDrawdown,
                simulationResults,
                DateTime.UtcNow
            );

            _logger.LogInformation("Monte Carlo completed. Mean return: {Mean:P2}, P5: {P5:P2}, P95: {P95:P2}",
                meanReturn, percentile5, percentile95);

            return results;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Monte Carlo simulation failed");
            throw;
        }
    }

    public async Task<CrisisAnalysisResults> AnalyzeCrisisScenariosAsync(OptimizedPortfolio portfolio)
    {
        _logger.LogInformation("Analyzing portfolio performance under {Count} historical crisis scenarios", 
            _historicalCrises.Count);

        try
        {
            await Task.Delay(600);

            var crisisResults = new List<CrisisImpactResult>();

            foreach (var crisis in _historicalCrises)
            {
                var portfolioImpact = CalculatePortfolioImpactDuringCrisis(portfolio, crisis);
                var recoveryDays = EstimateRecoveryTime(crisis, portfolioImpact);

                crisisResults.Add(new CrisisImpactResult(
                    crisis.CrisisName,
                    crisis.StartDate,
                    crisis.EndDate,
                    portfolioImpact,
                    recoveryDays,
                    crisis.MarketDecline
                ));
            }

            var worstCrisis = crisisResults.OrderBy(r => r.PortfolioImpact).First();
            var averageImpact = crisisResults.Average(r => r.PortfolioImpact);
            var averageRecoveryDays = (decimal)crisisResults.Average(r => r.RecoveryDays);

            var results = new CrisisAnalysisResults(
                crisisResults,
                worstCrisis,
                averageImpact,
                averageRecoveryDays,
                DateTime.UtcNow
            );

            _logger.LogInformation("Crisis analysis completed. Worst crisis: {Crisis} ({Impact:P2})",
                worstCrisis.CrisisName, worstCrisis.PortfolioImpact);

            return results;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Crisis analysis failed");
            throw;
        }
    }

    public async Task<TailRiskMetrics> CalculateTailRiskAsync(OptimizedPortfolio portfolio)
    {
        _logger.LogInformation("Calculating tail risk metrics for portfolio");

        try
        {
            await Task.Delay(400);

            // Generate return distribution
            var returns = GenerateReturnDistribution(portfolio, 10000);
            var sortedReturns = returns.OrderBy(r => r).ToArray();

            var var99 = sortedReturns[(int)(returns.Length * 0.01)];
            var expectedShortfall99 = sortedReturns.Take((int)(returns.Length * 0.01)).Average();
            var tailRatio = Math.Abs(sortedReturns[(int)(returns.Length * 0.01)] / sortedReturns[(int)(returns.Length * 0.05)]);
            var maxLossProbability = (decimal)returns.Count(r => r < -0.20m) / returns.Length; // Probability of >20% loss

            var metrics = new TailRiskMetrics(
                var99,
                expectedShortfall99,
                (decimal)tailRatio,
                maxLossProbability,
                DateTime.UtcNow
            );

            _logger.LogInformation("Tail risk calculated. VaR(99%): {VaR:P2}, ES(99%): {ES:P2}",
                var99, expectedShortfall99);

            return metrics;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Tail risk calculation failed");
            throw;
        }
    }

    public async Task<ScenarioAnalysisResults> PerformScenarioAnalysisAsync(
        OptimizedPortfolio portfolio, 
        List<MarketScenario> scenarios)
    {
        _logger.LogInformation("Performing scenario analysis with {Count} custom scenarios", scenarios.Count);

        try
        {
            await Task.Delay(300);

            var scenarioResults = new List<ScenarioResult>();

            foreach (var scenario in scenarios)
            {
                var portfolioReturn = CalculatePortfolioReturnUnderScenario(portfolio, scenario);
                scenarioResults.Add(new ScenarioResult(
                    scenario.Name,
                    scenario.Description,
                    portfolioReturn,
                    scenario.Probability
                ));
            }

            var bestCase = scenarioResults.OrderByDescending(r => r.Return).First();
            var worstCase = scenarioResults.OrderBy(r => r.Return).First();
            var expectedReturn = scenarioResults.Sum(r => r.Return * r.Probability);

            var results = new ScenarioAnalysisResults(
                scenarioResults,
                bestCase,
                worstCase,
                expectedReturn,
                DateTime.UtcNow
            );

            _logger.LogInformation("Scenario analysis completed. Expected return: {Expected:P2}",
                expectedReturn);

            return results;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Scenario analysis failed");
            throw;
        }
    }

    private List<StressScenario> GenerateStressScenarios()
    {
        return new List<StressScenario>
        {
            new("Market Crash", "Severe market decline (-30%)", -0.30m, 0.02m),
            new("Interest Rate Shock", "Rapid interest rate increase (+300bp)", -0.15m, 0.05m),
            new("Inflation Spike", "Unexpected inflation surge", -0.12m, 0.08m),
            new("Geopolitical Crisis", "Major geopolitical event", -0.20m, 0.03m),
            new("Currency Crisis", "Major currency devaluation", -0.18m, 0.04m),
            new("Credit Crunch", "Liquidity crisis in credit markets", -0.25m, 0.03m),
            new("Tech Bubble Burst", "Technology sector collapse", -0.35m, 0.02m),
            new("Banking Crisis", "Systemic banking sector problems", -0.28m, 0.02m)
        };
    }

    private decimal CalculatePortfolioReturnUnderStress(OptimizedPortfolio portfolio, StressScenario scenario)
    {
        // Simplified stress calculation - apply scenario impact with some randomness
        var baseImpact = scenario.MarketImpact;
        var portfolioBeta = CalculatePortfolioBeta(portfolio);
        var adjustedImpact = baseImpact * portfolioBeta;
        
        // Add some randomness
        var randomFactor = 1 + (decimal)(_random.NextDouble() * 0.4 - 0.2); // ±20% randomness
        
        return adjustedImpact * randomFactor;
    }

    private decimal CalculatePortfolioBeta(OptimizedPortfolio portfolio)
    {
        // Simplified beta calculation - assume most stocks have beta around 1.0
        return 0.8m + (decimal)(_random.NextDouble() * 0.6); // 0.8 to 1.4
    }

    private decimal[] GenerateDailyReturns(OptimizedPortfolio portfolio, int days)
    {
        var returns = new decimal[days];
        var dailyVol = portfolio.Risk / (decimal)Math.Sqrt(252); // Convert annual to daily volatility
        
        for (int i = 0; i < days; i++)
        {
            returns[i] = (decimal)_random.NextGaussian() * dailyVol;
        }
        
        return returns;
    }

    private decimal CalculateMaxDrawdown(decimal[] returns)
    {
        decimal peak = 0;
        decimal maxDrawdown = 0;
        decimal cumulative = 0;

        foreach (var ret in returns)
        {
            cumulative += ret;
            if (cumulative > peak) peak = cumulative;
            var drawdown = peak - cumulative;
            if (drawdown > maxDrawdown) maxDrawdown = drawdown;
        }

        return maxDrawdown;
    }

    private decimal CalculatePortfolioImpactDuringCrisis(OptimizedPortfolio portfolio, HistoricalCrisis crisis)
    {
        // Simplified crisis impact calculation
        var portfolioBeta = CalculatePortfolioBeta(portfolio);
        return crisis.MarketDecline * portfolioBeta * (1 + (decimal)(_random.NextDouble() * 0.3 - 0.15));
    }

    private int EstimateRecoveryTime(HistoricalCrisis crisis, decimal portfolioImpact)
    {
        // Estimate recovery time based on crisis severity and portfolio impact
        var baseDays = crisis.RecoveryDays;
        var impactMultiplier = Math.Abs(portfolioImpact) / Math.Abs(crisis.MarketDecline);
        return (int)(baseDays * (double)impactMultiplier);
    }

    private decimal[] GenerateReturnDistribution(OptimizedPortfolio portfolio, int samples)
    {
        var returns = new decimal[samples];
        var annualVol = portfolio.Risk;
        
        for (int i = 0; i < samples; i++)
        {
            returns[i] = (decimal)_random.NextGaussian() * annualVol;
        }
        
        return returns;
    }

    private decimal CalculatePortfolioReturnUnderScenario(OptimizedPortfolio portfolio, MarketScenario scenario)
    {
        // Simplified scenario calculation
        var portfolioBeta = CalculatePortfolioBeta(portfolio);
        return scenario.MarketReturn * portfolioBeta;
    }

    private List<HistoricalCrisis> InitializeHistoricalCrises()
    {
        return new List<HistoricalCrisis>
        {
            new("2008 Financial Crisis", new DateTime(2008, 9, 15), new DateTime(2009, 3, 9), -0.57m, 504),
            new("2020 COVID-19 Crash", new DateTime(2020, 2, 19), new DateTime(2020, 3, 23), -0.34m, 126),
            new("2000 Dot-Com Crash", new DateTime(2000, 3, 10), new DateTime(2002, 10, 9), -0.49m, 929),
            new("1987 Black Monday", new DateTime(1987, 10, 19), new DateTime(1987, 10, 19), -0.22m, 63),
            new("2011 European Debt Crisis", new DateTime(2011, 7, 22), new DateTime(2011, 10, 3), -0.19m, 189),
            new("2018 Q4 Selloff", new DateTime(2018, 10, 3), new DateTime(2018, 12, 24), -0.20m, 91)
        };
    }
}

/// <summary>
/// Stress test results
/// </summary>
public record StressTestResults(
    List<StressScenarioResult> Scenarios,
    StressScenarioResult WorstCaseScenario,
    decimal AverageStressReturn,
    decimal StressVaR,
    DateTime TestedAt
);

/// <summary>
/// Individual stress scenario result
/// </summary>
public record StressScenarioResult(
    string Name,
    string Description,
    decimal PortfolioReturn,
    decimal Probability
);

/// <summary>
/// Stress scenario definition
/// </summary>
public record StressScenario(
    string Name,
    string Description,
    decimal MarketImpact,
    decimal Probability
);

/// <summary>
/// Monte Carlo simulation results
/// </summary>
public record MonteCarloResults(
    int Simulations,
    int TradingDays,
    decimal MeanReturn,
    decimal Percentile5,
    decimal Percentile95,
    decimal ProbabilityOfLoss,
    decimal AverageMaxDrawdown,
    List<decimal> AllResults,
    DateTime SimulatedAt
);

/// <summary>
/// Crisis analysis results
/// </summary>
public record CrisisAnalysisResults(
    List<CrisisImpactResult> CrisisResults,
    CrisisImpactResult WorstCrisis,
    decimal AverageImpact,
    decimal AverageRecoveryDays,
    DateTime AnalyzedAt
);

/// <summary>
/// Individual crisis impact result
/// </summary>
public record CrisisImpactResult(
    string CrisisName,
    DateTime StartDate,
    DateTime EndDate,
    decimal PortfolioImpact,
    int RecoveryDays,
    decimal MarketDecline
);

/// <summary>
/// Historical crisis data
/// </summary>
public record HistoricalCrisis(
    string CrisisName,
    DateTime StartDate,
    DateTime EndDate,
    decimal MarketDecline,
    int RecoveryDays
);

/// <summary>
/// Tail risk metrics
/// </summary>
public record TailRiskMetrics(
    decimal VaR99,
    decimal ExpectedShortfall99,
    decimal TailRatio,
    decimal MaxLossProbability,
    DateTime CalculatedAt
);

/// <summary>
/// Scenario analysis results
/// </summary>
public record ScenarioAnalysisResults(
    List<ScenarioResult> Results,
    ScenarioResult BestCase,
    ScenarioResult WorstCase,
    decimal ExpectedReturn,
    DateTime AnalyzedAt
);

/// <summary>
/// Individual scenario result
/// </summary>
public record ScenarioResult(
    string Name,
    string Description,
    decimal Return,
    decimal Probability
);

/// <summary>
/// Market scenario definition
/// </summary>
public record MarketScenario(
    string Name,
    string Description,
    decimal MarketReturn,
    decimal Probability
);
