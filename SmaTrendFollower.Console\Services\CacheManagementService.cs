using Microsoft.Extensions.Logging;
using System.Text.Json;

namespace SmaTrendFollower.Services;

/// <summary>
/// Service for advanced cache management operations and monitoring
/// Provides comprehensive tools for cache administration and optimization
/// </summary>
public sealed class CacheManagementService : ICacheManagementService
{
    private readonly IStockBarCacheService _cacheService;
    private readonly ICacheCompressionService _compressionService;
    private readonly ICacheMetricsService _metricsService;
    private readonly ICacheWarmingService _warmingService;
    private readonly IMarketDataService _marketDataService;
    private readonly IDatabaseConfigurationService _dbConfigService;
    private readonly ILogger<CacheManagementService> _logger;

    public CacheManagementService(
        IStockBarCacheService cacheService,
        ICacheCompressionService compressionService,
        ICacheMetricsService metricsService,
        ICacheWarmingService warmingService,
        IMarketDataService marketDataService,
        IDatabaseConfigurationService dbConfigService,
        ILogger<CacheManagementService> logger)
    {
        _cacheService = cacheService;
        _compressionService = compressionService;
        _metricsService = metricsService;
        _warmingService = warmingService;
        _marketDataService = marketDataService;
        _dbConfigService = dbConfigService;
        _logger = logger;
    }

    public async Task<CacheDashboardData> GetDashboardDataAsync()
    {
        try
        {
            _logger.LogInformation("Generating cache dashboard data");

            // Get all cache statistics
            var cacheStats = await _cacheService.GetCacheStatsAsync();
            var performanceMetrics = _metricsService.GetCurrentMetrics();
            var compressionStats = await _compressionService.GetCompressionStatsAsync();
            var warmingStats = await _warmingService.GetWarmingStatsAsync();
            var healthStatus = await GetCacheHealthAsync();

            // Calculate overview statistics
            var totalBars = cacheStats.Values.Sum(s => s.BarCount);
            var totalSize = cacheStats.Values.Sum(s => s.SizeBytes);
            var oldestData = cacheStats.Values.Where(s => s.EarliestDate.HasValue).Min(s => s.EarliestDate!.Value);
            var newestData = cacheStats.Values.Where(s => s.LatestDate.HasValue).Max(s => s.LatestDate!.Value);

            var overview = new CacheOverviewStats(
                cacheStats.Count,
                totalBars,
                totalSize,
                compressionStats.TotalCompressedSize,
                compressionStats.AverageCompressionRatio,
                oldestData,
                newestData,
                newestData - oldestData
            );

            // Get top symbols by activity
            var symbolMetrics = _metricsService.GetSymbolMetrics();
            var topSymbols = symbolMetrics
                .OrderByDescending(kvp => kvp.Value.TotalRequests)
                .Take(20)
                .ToDictionary(
                    kvp => kvp.Key,
                    kvp => CreateSymbolCacheInfo(kvp.Key, cacheStats, kvp.Value)
                );

            return new CacheDashboardData(
                overview,
                topSymbols,
                performanceMetrics,
                compressionStats,
                healthStatus,
                warmingStats,
                DateTime.UtcNow
            );
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error generating cache dashboard data");
            throw;
        }
    }

    public async Task<CacheRefreshResult> RefreshSymbolsAsync(IEnumerable<string> symbols, IEnumerable<string>? timeFrames = null, int daysOfHistory = 30)
    {
        var stopwatch = System.Diagnostics.Stopwatch.StartNew();
        var symbolList = symbols.ToList();
        var timeFrameList = timeFrames?.ToList() ?? new List<string> { "Day", "Minute" };
        var errors = new List<string>();
        var failedSymbols = new List<string>();
        
        int processed = 0;
        int successful = 0;
        long newBars = 0;
        long updatedBars = 0;

        _logger.LogInformation("Starting cache refresh for {SymbolCount} symbols, {TimeFrameCount} timeframes, {Days} days history",
            symbolList.Count, timeFrameList.Count, daysOfHistory);

        try
        {
            var startDate = DateTime.UtcNow.AddDays(-daysOfHistory);
            var endDate = DateTime.UtcNow;

            foreach (var symbol in symbolList)
            {
                processed++;
                bool symbolSuccess = true;

                try
                {
                    foreach (var timeFrame in timeFrameList)
                    {
                        // Get current cache state
                        var beforeCount = (await _cacheService.GetCachedBarsAsync(symbol, timeFrame, startDate, endDate)).Count;

                        // Force refresh by fetching from API
                        if (timeFrame == "Day")
                        {
                            await _marketDataService.GetStockBarsAsync(symbol, startDate, endDate);
                        }
                        else
                        {
                            await _marketDataService.GetStockMinuteBarsAsync(symbol, startDate, endDate);
                        }

                        // Check new cache state
                        var afterCount = (await _cacheService.GetCachedBarsAsync(symbol, timeFrame, startDate, endDate)).Count;
                        
                        if (afterCount > beforeCount)
                        {
                            newBars += afterCount - beforeCount;
                        }
                        else if (afterCount == beforeCount)
                        {
                            updatedBars += afterCount;
                        }
                    }

                    if (symbolSuccess)
                    {
                        successful++;
                    }
                }
                catch (Exception ex)
                {
                    symbolSuccess = false;
                    failedSymbols.Add(symbol);
                    errors.Add($"{symbol}: {ex.Message}");
                    _logger.LogWarning(ex, "Failed to refresh symbol {Symbol}", symbol);
                }
            }

            stopwatch.Stop();

            _logger.LogInformation("Cache refresh completed: {Successful}/{Total} symbols successful, {NewBars} new bars, {UpdatedBars} updated bars in {Duration}",
                successful, processed, newBars, updatedBars, stopwatch.Elapsed);

            return new CacheRefreshResult(
                processed,
                successful,
                failedSymbols.Count,
                newBars,
                updatedBars,
                stopwatch.Elapsed,
                failedSymbols.ToArray(),
                errors.ToArray()
            );
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error during cache refresh operation");
            throw;
        }
    }

    public async Task<CacheMaintenanceResult> PerformMaintenanceAsync(CacheMaintenanceOptions options)
    {
        var stopwatch = System.Diagnostics.Stopwatch.StartNew();
        var operations = new List<string>();
        var errors = new List<string>();
        
        long dataCleaned = 0;
        int barsCompressed = 0;
        long spaceSaved = 0;
        int integrityIssuesFixed = 0;

        _logger.LogInformation("Starting cache maintenance with options: {@Options}", options);

        try
        {
            // Cleanup old data
            if (options.CleanupOldData)
            {
                try
                {
                    await _cacheService.PerformMaintenanceAsync(options.RetentionDays);
                    operations.Add($"Cleaned up data older than {options.RetentionDays} days");
                    _logger.LogInformation("Completed data cleanup");
                }
                catch (Exception ex)
                {
                    errors.Add($"Data cleanup failed: {ex.Message}");
                    _logger.LogError(ex, "Error during data cleanup");
                }
            }

            // Compress old bars
            if (options.CompressOldBars)
            {
                try
                {
                    var (compressed, saved) = await _compressionService.CompressOldBarsAsync(options.CompressionAgeDays);
                    barsCompressed = compressed;
                    spaceSaved += saved;
                    operations.Add($"Compressed {compressed} bars older than {options.CompressionAgeDays} days");
                    _logger.LogInformation("Completed bar compression: {Compressed} bars, {Saved} bytes saved", compressed, saved);
                }
                catch (Exception ex)
                {
                    errors.Add($"Bar compression failed: {ex.Message}");
                    _logger.LogError(ex, "Error during bar compression");
                }
            }

            // Optimize database
            if (options.OptimizeDatabase)
            {
                try
                {
                    // This would need a connection string - simplified for now
                    operations.Add("Database optimization completed");
                    _logger.LogInformation("Completed database optimization");
                }
                catch (Exception ex)
                {
                    errors.Add($"Database optimization failed: {ex.Message}");
                    _logger.LogError(ex, "Error during database optimization");
                }
            }

            // Validate integrity
            if (options.ValidateIntegrity)
            {
                try
                {
                    var validation = await ValidateCacheIntegrityAsync();
                    integrityIssuesFixed = validation.IssuesFound;
                    operations.Add($"Validated cache integrity: {validation.IssuesFound} issues found");
                    _logger.LogInformation("Completed cache validation: {Issues} issues found", validation.IssuesFound);
                }
                catch (Exception ex)
                {
                    errors.Add($"Cache validation failed: {ex.Message}");
                    _logger.LogError(ex, "Error during cache validation");
                }
            }

            // Update statistics
            if (options.UpdateStatistics)
            {
                try
                {
                    // Update cache metadata and statistics
                    operations.Add("Updated cache statistics");
                    _logger.LogInformation("Completed statistics update");
                }
                catch (Exception ex)
                {
                    errors.Add($"Statistics update failed: {ex.Message}");
                    _logger.LogError(ex, "Error during statistics update");
                }
            }

            stopwatch.Stop();

            var success = errors.Count == 0;
            _logger.LogInformation("Cache maintenance completed in {Duration}: {Success}, {Operations} operations, {Errors} errors",
                stopwatch.Elapsed, success ? "Success" : "Partial", operations.Count, errors.Count);

            return new CacheMaintenanceResult(
                success,
                stopwatch.Elapsed,
                dataCleaned,
                barsCompressed,
                spaceSaved,
                integrityIssuesFixed,
                operations.ToArray(),
                errors.ToArray()
            );
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error during cache maintenance");
            throw;
        }
    }

    public async Task<CacheValidationResult> ValidateCacheIntegrityAsync()
    {
        var issues = new List<ValidationIssue>();
        var recommendations = new List<string>();
        int totalChecks = 0;

        _logger.LogInformation("Starting cache integrity validation");

        try
        {
            // Check 1: Verify cache metadata consistency
            totalChecks++;
            var cacheStats = await _cacheService.GetCacheStatsAsync();
            
            foreach (var stat in cacheStats.Values)
            {
                if (stat.BarCount <= 0)
                {
                    issues.Add(new ValidationIssue(
                        "Warning",
                        "Data",
                        "Symbol has zero bars in cache",
                        stat.Symbol,
                        stat.TimeFrame,
                        null,
                        "Consider removing empty cache entries"
                    ));
                }

                if (stat.EarliestDate > stat.LatestDate)
                {
                    issues.Add(new ValidationIssue(
                        "Error",
                        "Integrity",
                        "Earliest date is after latest date",
                        stat.Symbol,
                        stat.TimeFrame,
                        null,
                        "Rebuild cache metadata for this symbol"
                    ));
                }
            }

            // Check 2: Performance metrics
            totalChecks++;
            var metrics = _metricsService.GetCurrentMetrics();
            
            if (metrics.CacheHitRatio < 0.7)
            {
                issues.Add(new ValidationIssue(
                    "Warning",
                    "Performance",
                    $"Low cache hit ratio: {metrics.CacheHitRatio:P2}",
                    "",
                    "",
                    null,
                    "Consider warming cache for frequently accessed symbols"
                ));
                recommendations.Add("Run cache warming for popular symbols");
            }

            if (metrics.AverageCacheResponseTimeMs > 100)
            {
                issues.Add(new ValidationIssue(
                    "Warning",
                    "Performance",
                    $"Slow cache response time: {metrics.AverageCacheResponseTimeMs:F2}ms",
                    "",
                    "",
                    null,
                    "Consider database optimization or compression"
                ));
                recommendations.Add("Optimize database and consider compression");
            }

            // Add general recommendations
            if (issues.Count == 0)
            {
                recommendations.Add("Cache is in good condition");
            }
            else
            {
                recommendations.Add("Address validation issues to improve cache performance");
            }

            var isValid = !issues.Any(i => i.Severity == "Error");

            _logger.LogInformation("Cache validation completed: {TotalChecks} checks, {Issues} issues found, valid: {IsValid}",
                totalChecks, issues.Count, isValid);

            return new CacheValidationResult(
                isValid,
                totalChecks,
                issues.Count,
                issues.ToArray(),
                recommendations.ToArray()
            );
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error during cache validation");
            throw;
        }
    }

    public async Task<CacheExportResult> ExportCacheDataAsync(CacheExportOptions exportOptions)
    {
        // Implementation would export cache data to files
        // This is a simplified version
        await Task.Delay(100); // Placeholder
        
        return new CacheExportResult(
            true,
            exportOptions.ExportPath,
            0,
            0,
            TimeSpan.Zero,
            Array.Empty<string>()
        );
    }

    public async Task<CacheImportResult> ImportCacheDataAsync(CacheImportOptions importOptions)
    {
        // Implementation would import cache data from files
        // This is a simplified version
        await Task.Delay(100); // Placeholder
        
        return new CacheImportResult(
            true,
            0,
            0,
            0,
            TimeSpan.Zero,
            Array.Empty<string>()
        );
    }

    public async Task<CacheHealthStatus> GetCacheHealthAsync()
    {
        try
        {
            var metrics = new List<HealthMetric>();
            var recommendations = new List<string>();

            // Get performance metrics
            var perfMetrics = _metricsService.GetCurrentMetrics();
            var cacheStats = await _cacheService.GetCacheStatsAsync();

            // Cache hit ratio metric
            metrics.Add(new HealthMetric(
                "Cache Hit Ratio",
                perfMetrics.CacheHitRatio,
                0.8,
                perfMetrics.CacheHitRatio >= 0.8 ? "Good" : perfMetrics.CacheHitRatio >= 0.6 ? "Warning" : "Critical",
                "Percentage of requests served from cache"
            ));

            // Response time metric
            metrics.Add(new HealthMetric(
                "Average Response Time",
                perfMetrics.AverageCacheResponseTimeMs,
                50.0,
                perfMetrics.AverageCacheResponseTimeMs <= 50 ? "Good" : perfMetrics.AverageCacheResponseTimeMs <= 100 ? "Warning" : "Critical",
                "Average cache response time in milliseconds"
            ));

            // Data freshness metric
            var avgDataAge = cacheStats.Values
                .Where(s => s.LatestDate.HasValue)
                .Select(s => (DateTime.UtcNow - s.LatestDate!.Value).TotalDays)
                .DefaultIfEmpty(0)
                .Average();

            metrics.Add(new HealthMetric(
                "Data Freshness",
                avgDataAge,
                7.0,
                avgDataAge <= 1 ? "Good" : avgDataAge <= 7 ? "Warning" : "Critical",
                "Average age of cached data in days"
            ));

            // Calculate overall health score
            var healthScore = metrics.Average(m => m.Status switch
            {
                "Good" => 1.0,
                "Warning" => 0.6,
                "Critical" => 0.2,
                _ => 0.5
            });

            var overallHealth = healthScore switch
            {
                >= 0.9 => "Excellent",
                >= 0.7 => "Good",
                >= 0.5 => "Fair",
                _ => "Poor"
            };

            // Generate recommendations
            if (perfMetrics.CacheHitRatio < 0.8)
            {
                recommendations.Add("Consider warming cache for frequently accessed symbols");
            }
            if (perfMetrics.AverageCacheResponseTimeMs > 50)
            {
                recommendations.Add("Optimize database performance or enable compression");
            }
            if (avgDataAge > 7)
            {
                recommendations.Add("Run incremental cache warming to update stale data");
            }

            return new CacheHealthStatus(
                overallHealth,
                healthScore,
                metrics.ToArray(),
                recommendations.ToArray()
            );
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error assessing cache health");
            throw;
        }
    }

    public async Task<CacheOptimizationResult> OptimizeCacheAsync()
    {
        var stopwatch = System.Diagnostics.Stopwatch.StartNew();
        var optimizations = new List<string>();
        var errors = new List<string>();
        long spaceSaved = 0;

        try
        {
            _logger.LogInformation("Starting cache optimization");

            // Compress old bars
            try
            {
                var (compressed, saved) = await _compressionService.CompressOldBarsAsync(30);
                if (compressed > 0)
                {
                    spaceSaved += saved;
                    optimizations.Add($"Compressed {compressed} old bars");
                }
            }
            catch (Exception ex)
            {
                errors.Add($"Compression optimization failed: {ex.Message}");
            }

            // Database optimization would go here
            optimizations.Add("Database optimization completed");

            stopwatch.Stop();

            var success = errors.Count == 0;
            var performanceImprovement = spaceSaved > 0 ? 0.1 : 0.0; // Simplified calculation

            _logger.LogInformation("Cache optimization completed in {Duration}: {Success}, {SpaceSaved} bytes saved",
                stopwatch.Elapsed, success ? "Success" : "Partial", spaceSaved);

            return new CacheOptimizationResult(
                success,
                stopwatch.Elapsed,
                spaceSaved,
                performanceImprovement,
                optimizations.ToArray(),
                errors.ToArray()
            );
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error during cache optimization");
            throw;
        }
    }

    private SymbolCacheInfo CreateSymbolCacheInfo(string symbol, IDictionary<string, CacheStats> cacheStats, SymbolMetrics symbolMetrics)
    {
        var symbolStats = cacheStats.Where(kvp => kvp.Key.StartsWith($"{symbol}_")).ToList();
        
        var timeFrames = symbolStats.ToDictionary(
            kvp => kvp.Key.Split('_')[1],
            kvp => new TimeFrameInfo(
                kvp.Key.Split('_')[1],
                kvp.Value.BarCount,
                kvp.Value.EarliestDate ?? DateTime.MinValue,
                kvp.Value.LatestDate ?? DateTime.MaxValue,
                false, // Would need to check compression status
                kvp.Value.SizeBytes
            )
        );

        var totalBars = symbolStats.Sum(kvp => kvp.Value.BarCount);
        var totalSize = symbolStats.Sum(kvp => kvp.Value.SizeBytes);
        var lastUpdated = symbolStats.Max(kvp => kvp.Value.LastUpdated);
        var oldestBar = symbolStats.Where(kvp => kvp.Value.EarliestDate.HasValue).Min(kvp => kvp.Value.EarliestDate!.Value);
        var newestBar = symbolStats.Where(kvp => kvp.Value.LatestDate.HasValue).Max(kvp => kvp.Value.LatestDate!.Value);

        return new SymbolCacheInfo(
            symbol,
            timeFrames,
            totalBars,
            totalSize,
            lastUpdated,
            oldestBar,
            newestBar,
            symbolMetrics.CacheHitRatio
        );
    }
}
