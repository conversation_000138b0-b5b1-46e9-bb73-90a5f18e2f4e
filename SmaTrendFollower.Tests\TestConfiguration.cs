using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;

namespace SmaTrendFollower.Tests;

/// <summary>
/// Test configuration helper for managing test credentials and settings
/// </summary>
public static class TestConfiguration
{
    private static readonly IConfiguration Configuration;

    static TestConfiguration()
    {
        Configuration = new ConfigurationBuilder()
            .AddEnvironmentVariables()
            .AddInMemoryCollection(new Dictionary<string, string?>
            {
                // Default test values - override with environment variables for real testing
                {"POLY_API_KEY", "test_polygon_key"},
                {"APCA_API_KEY_ID", "test_alpaca_key"},
                {"APCA_API_SECRET_KEY", "test_alpaca_secret"},
                {"APCA_API_ENV", "paper"},
                {"REDIS_URL", ""},
                {"REDIS_DATABASE", "0"}
            })
            .Build();
    }

    /// <summary>
    /// Gets whether integration tests should run based on available credentials
    /// </summary>
    public static bool ShouldRunIntegrationTests =>
        HasValidPolygonKey || HasValidAlpacaKeys;

    /// <summary>
    /// Gets whether Polygon integration tests should run
    /// </summary>
    public static bool ShouldRunPolygonTests =>
        HasValidPolygonKey;

    /// <summary>
    /// Gets whether Alpaca integration tests should run
    /// </summary>
    public static bool ShouldRunAlpacaTests =>
        HasValidAlpacaKeys;

    /// <summary>
    /// Gets the Polygon API key for testing
    /// </summary>
    public static string PolygonApiKey =>
        Configuration["POLY_API_KEY"] ?? "test_polygon_key";

    /// <summary>
    /// Gets the Alpaca API key for testing
    /// </summary>
    public static string AlpacaApiKey =>
        Configuration["APCA_API_KEY_ID"] ?? "test_alpaca_key";

    /// <summary>
    /// Gets the Alpaca secret key for testing
    /// </summary>
    public static string AlpacaSecretKey =>
        Configuration["APCA_API_SECRET_KEY"] ?? "test_alpaca_secret";

    /// <summary>
    /// Gets the Redis URL for testing
    /// </summary>
    public static string RedisUrl =>
        Configuration["REDIS_URL"] ?? "";

    /// <summary>
    /// Gets whether database initialization should be skipped in tests
    /// </summary>
    public static bool SkipDatabaseInitialization =>
        Configuration.GetValue<bool>("SKIP_DB_INIT", false);

    /// <summary>
    /// Configures test services with proper database initialization
    /// </summary>
    public static void ConfigureTestServices(IServiceCollection services, bool useInMemoryDatabases = true)
    {
        if (useInMemoryDatabases)
        {
            TestDatabaseHelper.ConfigureTestDatabases(services);
        }
        else
        {
            TestDatabaseHelper.ConfigureIntegrationTestDatabases(services);
        }
    }

    private static bool HasValidPolygonKey
    {
        get
        {
            var key = Configuration["POLY_API_KEY"];
            return !string.IsNullOrEmpty(key) && 
                   key != "test_polygon_key" && 
                   key != "REPLACE_ME";
        }
    }

    private static bool HasValidAlpacaKeys
    {
        get
        {
            var keyId = Configuration["APCA_API_KEY_ID"];
            var secret = Configuration["APCA_API_SECRET_KEY"];
            return !string.IsNullOrEmpty(keyId) && 
                   !string.IsNullOrEmpty(secret) &&
                   keyId != "test_alpaca_key" && 
                   secret != "test_alpaca_secret" &&
                   keyId != "REPLACE_ME" && 
                   secret != "REPLACE_ME";
        }
    }
}

/// <summary>
/// Test categories for organizing tests
/// </summary>
public static class TestCategories
{
    public const string Unit = "Unit";
    public const string Integration = "Integration";
    public const string Performance = "Performance";
    public const string Database = "Database";
    public const string Network = "Network";
    public const string Polygon = "Polygon";
    public const string Alpaca = "Alpaca";
    public const string Redis = "Redis";
    public const string Streaming = "Streaming";
}
