namespace SmaTrendFollower.Services.ErrorHandling;

/// <summary>
/// Centralized error handling interface for the trading system
/// </summary>
public interface IErrorHandler
{
    /// <summary>
    /// Handle an exception with context information
    /// </summary>
    /// <param name="exception">The exception to handle</param>
    /// <param name="context">Error context information</param>
    /// <returns>Error handling result with recovery strategy</returns>
    Task<ErrorHandlingResult> HandleErrorAsync(Exception exception, ErrorContext context);

    /// <summary>
    /// Handle a trading-specific exception
    /// </summary>
    /// <param name="tradingException">The trading exception to handle</param>
    /// <param name="context">Error context information</param>
    /// <returns>Error handling result with recovery strategy</returns>
    Task<ErrorHandlingResult> HandleTradingErrorAsync(TradingException tradingException, ErrorContext context);

    /// <summary>
    /// Register a custom error handler for specific exception types
    /// </summary>
    /// <typeparam name="T">Exception type</typeparam>
    /// <param name="handler">Custom handler function</param>
    void RegisterHandler<T>(Func<T, ErrorContext, Task<ErrorHandlingResult>> handler) where T : Exception;

    /// <summary>
    /// Register a custom error handler for specific error categories
    /// </summary>
    /// <param name="category">Error category</param>
    /// <param name="handler">Custom handler function</param>
    void RegisterCategoryHandler(ErrorCategory category, Func<TradingException, ErrorContext, Task<ErrorHandlingResult>> handler);

    /// <summary>
    /// Check if an exception should be retried
    /// </summary>
    /// <param name="exception">The exception to check</param>
    /// <param name="attemptCount">Current attempt count</param>
    /// <returns>True if should retry, false otherwise</returns>
    bool ShouldRetry(Exception exception, int attemptCount);

    /// <summary>
    /// Calculate retry delay with exponential backoff
    /// </summary>
    /// <param name="exception">The exception</param>
    /// <param name="attemptCount">Current attempt count</param>
    /// <returns>Delay before next retry</returns>
    TimeSpan CalculateRetryDelay(Exception exception, int attemptCount);

    /// <summary>
    /// Log error with appropriate severity level
    /// </summary>
    /// <param name="exception">The exception to log</param>
    /// <param name="context">Error context</param>
    /// <param name="additionalMessage">Additional message to include</param>
    Task LogErrorAsync(Exception exception, ErrorContext context, string? additionalMessage = null);
}

/// <summary>
/// Error handler configuration options
/// </summary>
public sealed class ErrorHandlerOptions
{
    /// <summary>
    /// Maximum number of retry attempts for retriable errors
    /// </summary>
    public int MaxRetryAttempts { get; set; } = 3;

    /// <summary>
    /// Base delay for exponential backoff (in milliseconds)
    /// </summary>
    public int BaseDelayMs { get; set; } = 1000;

    /// <summary>
    /// Maximum delay for exponential backoff (in milliseconds)
    /// </summary>
    public int MaxDelayMs { get; set; } = 30000;

    /// <summary>
    /// Whether to add jitter to retry delays
    /// </summary>
    public bool UseJitter { get; set; } = true;

    /// <summary>
    /// Maximum jitter amount (in milliseconds)
    /// </summary>
    public int MaxJitterMs { get; set; } = 1000;

    /// <summary>
    /// Whether to enable circuit breaker functionality
    /// </summary>
    public bool EnableCircuitBreaker { get; set; } = true;

    /// <summary>
    /// Number of consecutive failures before opening circuit breaker
    /// </summary>
    public int CircuitBreakerThreshold { get; set; } = 5;

    /// <summary>
    /// Duration to keep circuit breaker open (in minutes)
    /// </summary>
    public int CircuitBreakerTimeoutMinutes { get; set; } = 5;

    /// <summary>
    /// Whether to send notifications for critical errors
    /// </summary>
    public bool EnableNotifications { get; set; } = true;

    /// <summary>
    /// Minimum severity level for notifications
    /// </summary>
    public ErrorSeverity NotificationThreshold { get; set; } = ErrorSeverity.High;

    /// <summary>
    /// Whether to enable detailed error logging
    /// </summary>
    public bool EnableDetailedLogging { get; set; } = true;

    /// <summary>
    /// Whether to include stack traces in error logs
    /// </summary>
    public bool IncludeStackTrace { get; set; } = true;

    /// <summary>
    /// Custom error handlers by exception type
    /// </summary>
    public Dictionary<Type, object> CustomHandlers { get; set; } = new();

    /// <summary>
    /// Custom error handlers by category
    /// </summary>
    public Dictionary<ErrorCategory, object> CategoryHandlers { get; set; } = new();
}
