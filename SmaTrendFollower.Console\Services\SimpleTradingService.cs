using SmaTrendFollower.Models;
using Microsoft.Extensions.Logging;

namespace SmaTrendFollower.Services;

/// <summary>
/// Simple trading service for testing purposes.
/// Implements the basic trading cycle flow expected by tests.
/// </summary>
public sealed class SimpleTradingService : ITradingService
{
    private readonly ISignalGenerator _signalGenerator;
    private readonly IRiskManager _riskManager;
    private readonly IPortfolioGate _portfolioGate;
    private readonly ITradeExecutor _tradeExecutor;
    private readonly IStopManager _stopManager;
    private readonly ITradingSafetyGuard _safetyGuard;
    private readonly IMarketRegimeService _regimeService;
    private readonly ILogger<SimpleTradingService> _logger;

    public SimpleTradingService(
        ISignalGenerator signalGenerator,
        IRiskManager riskManager,
        IPortfolioGate portfolioGate,
        ITradeExecutor tradeExecutor,
        IStopManager stopManager,
        ITradingSafetyGuard safetyGuard,
        IMarketRegimeService regimeService,
        ILogger<SimpleTradingService> logger)
    {
        _signalGenerator = signalGenerator;
        _riskManager = riskManager;
        _portfolioGate = portfolioGate;
        _tradeExecutor = tradeExecutor;
        _stopManager = stopManager;
        _safetyGuard = safetyGuard;
        _regimeService = regimeService;
        _logger = logger;
    }

    public async Task ExecuteCycleAsync(CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogInformation("Starting simple trading cycle");

            // Step 1: Safety guard validation (check first before any operations)
            var safetyResult = await _safetyGuard.ValidateTradingCycleAsync();
            if (!safetyResult.IsAllowed)
            {
                _logger.LogWarning("Safety guard blocked trading: {Reason}", safetyResult.Reason);
                return;
            }

            // Step 2: Update trailing stops (only after safety guard passes)
            await _stopManager.UpdateTrailingStopsAsync(cancellationToken);

            // Step 3: Check market regime
            var regimeAllowed = await _regimeService.IsTradingAllowedAsync(cancellationToken);
            if (!regimeAllowed)
            {
                var regime = await _regimeService.GetCachedRegimeAsync(cancellationToken);
                _logger.LogInformation("Market regime {Regime} blocks trading", regime);
                return;
            }

            // Step 4: Check portfolio gate (SPY SMA200)
            if (!await _portfolioGate.ShouldTradeAsync())
            {
                _logger.LogInformation("Portfolio gate blocked trading - SPY below SMA200");
                return;
            }

            // Step 5: Generate trading signals
            var signals = await _signalGenerator.RunAsync(10);
            var signalList = signals.ToList();

            _logger.LogInformation("Generated {Count} trading signals", signalList.Count);

            // Step 6: Execute trades
            foreach (var signal in signalList)
            {
                try
                {
                    var quantity = await _riskManager.CalculateQuantityAsync(signal);
                    if (quantity > 0)
                    {
                        await _tradeExecutor.ExecuteTradeAsync(signal, quantity);
                        _logger.LogInformation("Executed trade for {Symbol}: {Quantity} shares", signal.Symbol, quantity);
                    }
                    else
                    {
                        _logger.LogDebug("Skipped trade for {Symbol}: zero quantity", signal.Symbol);
                    }
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "Error executing trade for {Symbol}", signal.Symbol);
                }
            }

            _logger.LogInformation("Simple trading cycle completed successfully");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error in simple trading cycle");
            throw;
        }
    }
}
