#!/bin/bash

# Setup script for SmaTrendFollower remote environment for Augment Agent
# This script prepares the remote environment by:
# - Checking prerequisites (.NET 8 SDK)
# - Restoring NuGet packages
# - Building the solution
# - Running tests
# - Setting up environment variables
# - Creating necessary directories
# - Validating the setup

set -e  # Exit on any error

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
MAGENTA='\033[0;35m'
CYAN='\033[0;36m'
WHITE='\033[1;37m'
NC='\033[0m' # No Color

# Parse command line arguments
SKIP_TESTS=false
VERBOSE=false

while [[ $# -gt 0 ]]; do
    case $1 in
        --skip-tests)
            SKIP_TESTS=true
            shift
            ;;
        --verbose)
            VERBOSE=true
            shift
            ;;
        -h|--help)
            echo "Usage: $0 [--skip-tests] [--verbose]"
            echo "  --skip-tests    Skip running tests during setup"
            echo "  --verbose       Enable verbose output"
            exit 0
            ;;
        *)
            echo "Unknown option $1"
            exit 1
            ;;
    esac
done

# Function to print colored output
print_status() {
    local color=$1
    local message=$2
    echo -e "${color}${message}${NC}"
}

# Function to check if command exists
command_exists() {
    command -v "$1" >/dev/null 2>&1
}

# Function to run command with error handling
run_command() {
    local description=$1
    local command=$2
    local continue_on_error=${3:-false}
    
    print_status $CYAN "📋 $description"
    if [ "$VERBOSE" = true ]; then
        echo "Executing: $command"
    fi
    
    if eval "$command"; then
        print_status $GREEN "✅ $description completed successfully"
    else
        print_status $RED "❌ $description failed"
        if [ "$continue_on_error" = false ]; then
            exit 1
        fi
    fi
}

print_status $GREEN "🚀 Setting up SmaTrendFollower remote environment for Augment Agent..."

# Step 1: Check Prerequisites
print_status $YELLOW "\n🔍 Checking Prerequisites..."

# Check .NET 8 SDK
if ! command_exists dotnet; then
    print_status $RED "❌ .NET SDK not found. Please install .NET 8 SDK from https://dotnet.microsoft.com/download"
    exit 1
fi

DOTNET_VERSION=$(dotnet --version)
print_status $GREEN "✅ .NET SDK version: $DOTNET_VERSION"

# Verify .NET 8 is available
if ! dotnet --info | grep -q "8\.0\."; then
    print_status $YELLOW "⚠️  .NET 8 SDK not detected. Current version: $DOTNET_VERSION"
    print_status $YELLOW "Please ensure .NET 8 SDK is installed for optimal compatibility"
fi

# Check bash version
print_status $GREEN "✅ Bash version: $BASH_VERSION"

# Step 2: Validate Project Structure
print_status $YELLOW "\n📁 Validating Project Structure..."

REQUIRED_FILES=(
    "SmaTrendFollower.sln"
    "SmaTrendFollower.Console/SmaTrendFollower.Console.csproj"
    "SmaTrendFollower.Tests/SmaTrendFollower.Tests.csproj"
    ".env"
)

for file in "${REQUIRED_FILES[@]}"; do
    if [ -f "$file" ]; then
        print_status $GREEN "✅ Found: $file"
    else
        print_status $RED "❌ Missing: $file"
        exit 1
    fi
done

# Step 3: Create Required Directories
print_status $YELLOW "\n📂 Creating Required Directories..."

DIRECTORIES=("logs" "data" "cache")
for dir in "${DIRECTORIES[@]}"; do
    if [ ! -d "$dir" ]; then
        mkdir -p "$dir"
        print_status $GREEN "✅ Created directory: $dir"
    else
        print_status $GREEN "✅ Directory exists: $dir"
    fi
done

# Step 4: Environment Variables Setup
print_status $YELLOW "\n🔧 Setting up Environment Variables..."

if [ -f ".env" ]; then
    print_status $GREEN "✅ .env file found"
    
    # Load and validate .env file
    REQUIRED_ENV_VARS=("APCA_API_KEY_ID" "APCA_API_SECRET_KEY" "APCA_API_ENV" "POLY_API_KEY")
    
    for env_var in "${REQUIRED_ENV_VARS[@]}"; do
        if grep -q "^$env_var=" .env; then
            print_status $GREEN "✅ Environment variable configured: $env_var"
        else
            print_status $YELLOW "⚠️  Environment variable missing: $env_var"
        fi
    done
else
    print_status $YELLOW "⚠️  .env file not found. Creating template..."
    cat > .env << 'EOF'
# Alpaca API Configuration
APCA_API_KEY_ID=your_alpaca_key_here
APCA_API_SECRET_KEY=your_alpaca_secret_here
APCA_API_ENV=paper

# Polygon API Configuration
POLY_API_KEY=your_polygon_key_here

# Trading Configuration (optional)
# MAX_POSITION_SIZE_PERCENT=0.05
# STOP_LOSS_PERCENT=0.02
# TAKE_PROFIT_PERCENT=0.06
EOF
    print_status $YELLOW "📝 Created .env template. Please configure with your API keys."
fi

# Step 5: Clean Previous Builds
print_status $YELLOW "\n🧹 Cleaning Previous Builds..."
run_command "Cleaning solution" "dotnet clean"

# Step 6: Restore NuGet Packages
print_status $YELLOW "\n📦 Restoring NuGet Packages..."
run_command "Restoring NuGet packages" "dotnet restore"

# Step 7: Build Solution
print_status $YELLOW "\n🔨 Building Solution..."
run_command "Building solution in Release mode" "dotnet build --configuration Release --no-restore"

# Step 8: Run Tests (if not skipped)
if [ "$SKIP_TESTS" = false ]; then
    print_status $YELLOW "\n🧪 Running Tests..."
    run_command "Running unit tests" "dotnet test --configuration Release --no-build --verbosity normal" true
else
    print_status $YELLOW "\n⏭️  Skipping tests as requested"
fi

# Step 9: Validate Setup
print_status $YELLOW "\n✅ Validating Setup..."

# Check if executables were built
CONSOLE_EXE="SmaTrendFollower.Console/bin/Release/net8.0/SmaTrendFollower.Console.dll"
if [ -f "$CONSOLE_EXE" ]; then
    print_status $GREEN "✅ Console application built successfully"
else
    print_status $RED "❌ Console application build failed"
fi

# Test basic functionality (dry run)
print_status $YELLOW "\n🔍 Testing Basic Functionality..."
if dotnet run --project SmaTrendFollower.Console --configuration Release -- --help >/dev/null 2>&1 || 
   dotnet run --project SmaTrendFollower.Console --configuration Release -- --help 2>&1 | grep -q -i "help\|usage\|options"; then
    print_status $GREEN "✅ Application can start successfully"
else
    print_status $YELLOW "⚠️  Application may have startup issues"
fi

# Step 10: Setup Summary
print_status $MAGENTA "\n📋 Setup Summary"
print_status $MAGENTA "==========================================="
print_status $GREEN "✅ .NET SDK: Available"
print_status $GREEN "✅ Project Structure: Valid"
print_status $GREEN "✅ Dependencies: Restored"
print_status $GREEN "✅ Build: Successful"
print_status $GREEN "✅ Directories: Created"

if [ "$SKIP_TESTS" = false ]; then
    print_status $GREEN "✅ Tests: Executed"
fi

print_status $GREEN "\n🎉 Remote environment setup completed successfully!"
print_status $CYAN "\n📚 Available Commands for Augment Agent:"
print_status $WHITE "  • Build:           dotnet build"
print_status $WHITE "  • Test:            dotnet test"
print_status $WHITE "  • Run Console:     dotnet run --project SmaTrendFollower.Console"
print_status $WHITE "  • Clean:           dotnet clean"
print_status $WHITE "  • Restore:         dotnet restore"

print_status $CYAN "\n📁 Project Structure:"
print_status $WHITE "  • SmaTrendFollower.Console/    - Main trading application"
print_status $WHITE "  • SmaTrendFollower.Tests/      - Unit and integration tests"
print_status $WHITE "  • logs/                        - Application logs"
print_status $WHITE "  • data/                        - Data files"
print_status $WHITE "  • cache/                       - Cache files"

print_status $CYAN "\n🔧 Environment Configuration:"
print_status $WHITE "  • .env file contains API keys and configuration"
print_status $WHITE "  • Ensure API keys are properly configured before trading"

print_status $YELLOW "\n⚠️  Important Notes:"
print_status $RED "  • This is a live trading system - use with caution"
print_status $YELLOW "  • Always test changes thoroughly before deployment"
print_status $YELLOW "  • Monitor logs for any issues during operation"

print_status $GREEN "\n🚀 Environment is ready for Augment Agent!"
