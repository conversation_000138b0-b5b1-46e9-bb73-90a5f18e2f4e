#!/usr/bin/env pwsh
# Test Discord Bot Token Configuration
# This script tests the Discord notification service with bot token authentication

Write-Host "🤖 Testing Discord Bot Token Configuration..." -ForegroundColor Cyan

# Check if .env file exists
if (-not (Test-Path ".env")) {
    Write-Host "❌ .env file not found!" -ForegroundColor Red
    exit 1
}

# Load environment variables
$envContent = Get-Content ".env"
$botToken = $null
$channelId = $null

foreach ($line in $envContent) {
    if ($line -match "^DISCORD_BOT_TOKEN=(.+)$") {
        $botToken = $matches[1].Trim()
    }
    if ($line -match "^DISCORD_CHANNEL_ID=(.+)$") {
        $channelId = $matches[1].Trim()
    }
}

Write-Host "📋 Configuration Check:" -ForegroundColor Yellow
Write-Host "  Bot Token: $(if ($botToken) { "✅ Configured (${botToken.Substring(0,20)}...)" } else { "❌ Missing" })"
Write-Host "  Channel ID: $(if ($channelId) { "✅ $channelId" } else { "❌ Missing" })"
Write-Host "  Token Length: $(if ($botToken) { $botToken.Length } else { 0 })" -ForegroundColor Gray

if (-not $botToken -or -not $channelId) {
    Write-Host "❌ Discord configuration incomplete!" -ForegroundColor Red
    exit 1
}

# Test bot identity first
Write-Host "`n🔍 Testing bot identity..." -ForegroundColor Cyan

try {
    $headers = @{
        "Authorization" = "Bot $botToken"
        "Content-Type" = "application/json"
    }

    $botInfo = Invoke-RestMethod -Uri "https://discord.com/api/v10/users/@me" -Method Get -Headers $headers
    Write-Host "✅ Bot authenticated successfully!" -ForegroundColor Green
    Write-Host "  Bot Name: $($botInfo.username)#$($botInfo.discriminator)" -ForegroundColor Gray
    Write-Host "  Bot ID: $($botInfo.id)" -ForegroundColor Gray

} catch {
    Write-Host "❌ Bot authentication failed!" -ForegroundColor Red
    Write-Host "  Error: $($_.Exception.Message)" -ForegroundColor Red
    Write-Host "  This suggests the bot token is invalid or malformed" -ForegroundColor Red
    exit 1
}

# Test Discord API connectivity
Write-Host "`n🔗 Testing Discord message sending..." -ForegroundColor Cyan

try {
    $headers = @{
        "Authorization" = "Bot $botToken"
        "Content-Type" = "application/json"
    }
    
    $testMessage = @{
        embeds = @(
            @{
                title = "🧪 SmaTrendFollower Test"
                description = "Testing Discord bot token configuration - $(Get-Date -Format 'yyyy-MM-dd HH:mm:ss')"
                color = 0x00FF00
                timestamp = (Get-Date).ToUniversalTime().ToString("yyyy-MM-ddTHH:mm:ss.fffZ")
                footer = @{
                    text = "SmaTrendFollower Bot"
                    icon_url = "https://cdn.discordapp.com/embed/avatars/0.png"
                }
            }
        )
    } | ConvertTo-Json -Depth 10
    
    $apiUrl = "https://discord.com/api/v10/channels/$channelId/messages"
    
    Write-Host "  API URL: $apiUrl" -ForegroundColor Gray
    Write-Host "  Sending test message..." -ForegroundColor Gray
    
    $response = Invoke-RestMethod -Uri $apiUrl -Method Post -Headers $headers -Body $testMessage
    
    Write-Host "✅ Discord test message sent successfully!" -ForegroundColor Green
    Write-Host "  Message ID: $($response.id)" -ForegroundColor Gray
    Write-Host "  Channel: $($response.channel_id)" -ForegroundColor Gray
    
} catch {
    Write-Host "❌ Discord API test failed!" -ForegroundColor Red
    Write-Host "  Error: $($_.Exception.Message)" -ForegroundColor Red
    
    if ($_.Exception.Response) {
        $statusCode = $_.Exception.Response.StatusCode
        Write-Host "  Status Code: $statusCode" -ForegroundColor Red
        
        try {
            $errorContent = $_.Exception.Response.GetResponseStream()
            $reader = New-Object System.IO.StreamReader($errorContent)
            $errorBody = $reader.ReadToEnd()
            Write-Host "  Response: $errorBody" -ForegroundColor Red
        } catch {
            Write-Host "  Could not read error response" -ForegroundColor Red
        }
    }
    exit 1
}

Write-Host "`n🎉 Discord Bot Token configuration is working!" -ForegroundColor Green
Write-Host "✅ Daily Reports are now ACTIVE" -ForegroundColor Green
