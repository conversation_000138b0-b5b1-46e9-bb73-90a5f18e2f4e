using Microsoft.Extensions.Logging;
using System.Collections.Concurrent;
using System.Diagnostics;

namespace SmaTrendFollower.Services.ErrorHandling;

/// <summary>
/// Health monitoring service implementation with comprehensive dependency tracking
/// </summary>
public sealed class HealthMonitoringService : IHealthMonitoringService, IDisposable
{
    private readonly ILogger<HealthMonitoringService> _logger;
    private readonly ConcurrentDictionary<string, ServiceHealthContext> _services = new();
    private readonly Timer? _monitoringTimer;
    private readonly SemaphoreSlim _monitoringSemaphore = new(1, 1);
    private volatile bool _isMonitoring;
    private volatile bool _disposed;

    public event EventHandler<HealthStatusChangedEventArgs>? HealthStatusChanged;

    public HealthMonitoringService(ILogger<HealthMonitoringService> logger)
    {
        _logger = logger;
        
        // Start monitoring timer (runs every 30 seconds)
        _monitoringTimer = new Timer(PerformScheduledHealthChecks, null, 
            TimeSpan.FromSeconds(30), TimeSpan.FromSeconds(30));
    }

    public async Task<HealthCheckResult> CheckServiceHealthAsync(string serviceName, CancellationToken cancellationToken = default)
    {
        if (!_services.TryGetValue(serviceName, out var context))
        {
            return HealthCheckResult.Unhealthy(serviceName, "Service not registered for health monitoring");
        }

        return await PerformHealthCheckAsync(context, cancellationToken);
    }

    public async Task<OverallHealthStatus> CheckOverallHealthAsync(CancellationToken cancellationToken = default)
    {
        var stopwatch = Stopwatch.StartNew();
        var results = new Dictionary<string, HealthCheckResult>();

        // Run all health checks in parallel
        var tasks = _services.Values.Select(async context =>
        {
            try
            {
                var result = await PerformHealthCheckAsync(context, cancellationToken);
                return new { ServiceName = context.ServiceName, Result = result };
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Health check failed for service {ServiceName}", context.ServiceName);
                return new { 
                    ServiceName = context.ServiceName, 
                    Result = HealthCheckResult.Unhealthy(context.ServiceName, "Health check exception", ex) 
                };
            }
        });

        var completedTasks = await Task.WhenAll(tasks);
        
        foreach (var task in completedTasks)
        {
            results[task.ServiceName] = task.Result;
        }

        stopwatch.Stop();

        // Determine overall status
        var overallStatus = DetermineOverallStatus(results.Values);

        return new OverallHealthStatus
        {
            Status = overallStatus,
            TotalCheckTime = stopwatch.Elapsed,
            ServiceResults = results
        };
    }

    public void RegisterHealthCheck(string serviceName, Func<CancellationToken, Task<HealthCheckResult>> healthCheck, HealthCheckOptions? options = null)
    {
        if (string.IsNullOrEmpty(serviceName))
            throw new ArgumentException("Service name cannot be null or empty", nameof(serviceName));

        if (healthCheck == null)
            throw new ArgumentNullException(nameof(healthCheck));

        var context = new ServiceHealthContext
        {
            ServiceName = serviceName,
            HealthCheck = healthCheck,
            Options = options ?? new HealthCheckOptions(),
            LastCheckTime = DateTime.UtcNow
        };

        _services.AddOrUpdate(serviceName, context, (_, _) => context);
        
        _logger.LogInformation("Registered health check for service {ServiceName}", serviceName);
    }

    public async Task StartMonitoringAsync(CancellationToken cancellationToken = default)
    {
        await _monitoringSemaphore.WaitAsync(cancellationToken);
        try
        {
            if (_isMonitoring)
            {
                _logger.LogWarning("Health monitoring is already running");
                return;
            }

            _isMonitoring = true;
            _logger.LogInformation("Started health monitoring for {ServiceCount} services", _services.Count);

            // Perform initial health check
            await CheckOverallHealthAsync(cancellationToken);
        }
        finally
        {
            _monitoringSemaphore.Release();
        }
    }

    public async Task StopMonitoringAsync()
    {
        await _monitoringSemaphore.WaitAsync();
        try
        {
            _isMonitoring = false;
            _logger.LogInformation("Stopped health monitoring");
        }
        finally
        {
            _monitoringSemaphore.Release();
        }
    }

    public IEnumerable<HealthCheckResult> GetHealthHistory(string serviceName, TimeSpan timeRange)
    {
        if (!_services.TryGetValue(serviceName, out var context))
        {
            return Enumerable.Empty<HealthCheckResult>();
        }

        var cutoffTime = DateTime.UtcNow - timeRange;
        
        lock (context.Lock)
        {
            return context.History
                .Where(h => h.Timestamp >= cutoffTime)
                .OrderByDescending(h => h.Timestamp)
                .ToList();
        }
    }

    public Dictionary<string, HealthCheckResult> GetCurrentHealthStatus()
    {
        var results = new Dictionary<string, HealthCheckResult>();
        
        foreach (var context in _services.Values)
        {
            lock (context.Lock)
            {
                var latestResult = context.History.LastOrDefault();
                if (latestResult != null)
                {
                    results[context.ServiceName] = latestResult;
                }
                else
                {
                    // No history yet, create a placeholder
                    results[context.ServiceName] = new HealthCheckResult
                    {
                        ServiceName = context.ServiceName,
                        Status = HealthStatus.Healthy,
                        Description = "No health check performed yet",
                        Timestamp = context.LastCheckTime
                    };
                }
            }
        }

        return results;
    }

    private async Task<HealthCheckResult> PerformHealthCheckAsync(ServiceHealthContext context, CancellationToken cancellationToken)
    {
        var stopwatch = Stopwatch.StartNew();
        HealthCheckResult result;

        try
        {
            // Apply timeout
            using var timeoutCts = CancellationTokenSource.CreateLinkedTokenSource(cancellationToken);
            timeoutCts.CancelAfter(context.Options.Timeout);

            result = await context.HealthCheck(timeoutCts.Token);
            stopwatch.Stop();
            
            // Ensure response time is recorded
            if (result.ResponseTime == TimeSpan.Zero)
            {
                result = new HealthCheckResult
                {
                    ServiceName = result.ServiceName,
                    Status = result.Status,
                    Description = result.Description,
                    ResponseTime = stopwatch.Elapsed,
                    Timestamp = result.Timestamp,
                    Data = result.Data,
                    Exception = result.Exception
                };
            }
        }
        catch (OperationCanceledException) when (cancellationToken.IsCancellationRequested)
        {
            stopwatch.Stop();
            result = HealthCheckResult.Unhealthy(context.ServiceName, "Health check was cancelled", responseTime: stopwatch.Elapsed);
        }
        catch (OperationCanceledException)
        {
            stopwatch.Stop();
            result = HealthCheckResult.Unhealthy(context.ServiceName, $"Health check timed out after {context.Options.Timeout}", responseTime: stopwatch.Elapsed);
        }
        catch (Exception ex)
        {
            stopwatch.Stop();
            result = HealthCheckResult.Unhealthy(context.ServiceName, "Health check threw an exception", ex, stopwatch.Elapsed);
        }

        // Update context and history
        UpdateServiceContext(context, result);

        return result;
    }

    private void UpdateServiceContext(ServiceHealthContext context, HealthCheckResult result)
    {
        lock (context.Lock)
        {
            var previousStatus = context.CurrentStatus;
            
            // Update consecutive counters
            if (result.Status == HealthStatus.Healthy)
            {
                context.ConsecutiveSuccesses++;
                context.ConsecutiveFailures = 0;
            }
            else
            {
                context.ConsecutiveFailures++;
                context.ConsecutiveSuccesses = 0;
            }

            // Determine new status based on thresholds
            var newStatus = DetermineServiceStatus(context, result.Status);
            
            // Update context
            context.CurrentStatus = newStatus;
            context.LastCheckTime = DateTime.UtcNow;
            
            // Add to history (keep last 100 entries)
            context.History.Enqueue(result);
            while (context.History.Count > 100)
            {
                context.History.Dequeue();
            }

            // Fire event if status changed
            if (previousStatus != newStatus)
            {
                OnHealthStatusChanged(new HealthStatusChangedEventArgs
                {
                    ServiceName = context.ServiceName,
                    PreviousStatus = previousStatus,
                    NewStatus = newStatus,
                    Result = result
                });
            }
        }
    }

    private HealthStatus DetermineServiceStatus(ServiceHealthContext context, HealthStatus checkResult)
    {
        // If currently healthy and we get a failure, need multiple failures to mark as unhealthy
        if (context.CurrentStatus == HealthStatus.Healthy && checkResult != HealthStatus.Healthy)
        {
            return context.ConsecutiveFailures >= context.Options.FailureThreshold ? checkResult : HealthStatus.Healthy;
        }

        // If currently unhealthy and we get successes, need multiple successes to mark as healthy
        if (context.CurrentStatus != HealthStatus.Healthy && checkResult == HealthStatus.Healthy)
        {
            return context.ConsecutiveSuccesses >= context.Options.SuccessThreshold ? HealthStatus.Healthy : context.CurrentStatus;
        }

        // Otherwise, use the check result
        return checkResult;
    }

    private static HealthStatus DetermineOverallStatus(IEnumerable<HealthCheckResult> results)
    {
        var resultList = results.ToList();
        
        if (!resultList.Any())
            return HealthStatus.Healthy;

        if (resultList.Any(r => r.Status == HealthStatus.Unhealthy))
            return HealthStatus.Unhealthy;

        if (resultList.Any(r => r.Status == HealthStatus.Degraded))
            return HealthStatus.Degraded;

        return HealthStatus.Healthy;
    }

    private async void PerformScheduledHealthChecks(object? state)
    {
        if (!_isMonitoring || _disposed)
            return;

        try
        {
            var servicesToCheck = _services.Values
                .Where(context => DateTime.UtcNow - context.LastCheckTime >= context.Options.CheckInterval)
                .ToList();

            if (servicesToCheck.Any())
            {
                _logger.LogDebug("Performing scheduled health checks for {ServiceCount} services", servicesToCheck.Count);
                
                var tasks = servicesToCheck.Select(context => PerformHealthCheckAsync(context, CancellationToken.None));
                await Task.WhenAll(tasks);
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error during scheduled health checks");
        }
    }

    private void OnHealthStatusChanged(HealthStatusChangedEventArgs args)
    {
        try
        {
            _logger.LogInformation("Health status changed for {ServiceName}: {PreviousStatus} -> {NewStatus}",
                args.ServiceName, args.PreviousStatus, args.NewStatus);
            
            HealthStatusChanged?.Invoke(this, args);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error firing health status changed event for {ServiceName}", args.ServiceName);
        }
    }

    public void Dispose()
    {
        if (_disposed)
            return;

        _disposed = true;
        _monitoringTimer?.Dispose();
        _monitoringSemaphore?.Dispose();
    }
}
