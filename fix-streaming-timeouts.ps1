#!/usr/bin/env pwsh

# Fix streaming test timeouts - replace TestTimeouts.Streaming with TestTimeouts.Unit for unit tests

Write-Host "Fixing streaming test timeouts..." -ForegroundColor Cyan

$testFile = "SmaTrendFollower.Tests/Services/StreamingDataServiceTests.cs"

if (Test-Path $testFile) {
    $content = Get-Content $testFile -Raw
    
    # Replace TestTimeouts.Streaming with TestTimeouts.Unit for unit tests
    $updatedContent = $content -replace '\[TestTimeout\(TestTimeouts\.Streaming\)\]\s*\[Trait\("Category", TestCategories\.Unit\)\]', '[TestTimeout(TestTimeouts.Unit)]`n    [Trait("Category", TestCategories.Unit)]'
    
    # Write back to file
    Set-Content $testFile -Value $updatedContent -NoNewline
    
    Write-Host "✅ Fixed streaming test timeouts in $testFile" -ForegroundColor Green
} else {
    Write-Host "❌ File not found: $testFile" -ForegroundColor Red
}

Write-Host "Done!" -ForegroundColor Green
