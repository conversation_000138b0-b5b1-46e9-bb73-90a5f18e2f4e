using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using SmaTrendFollower.Data;
using SmaTrendFollower.Models;
using SmaTrendFollower.Services;
using System.Collections.Concurrent;
using System.Diagnostics;

namespace SmaTrendFollower.Services;

/// <summary>
/// Service for optimized bulk database operations with performance monitoring
/// </summary>
public interface IOptimizedBulkOperationsService
{
    /// <summary>
    /// Performs bulk insert of bars with optimized batching
    /// </summary>
    Task<BulkOperationResult> BulkInsertBarsAsync(
        IDictionary<string, IDictionary<string, IEnumerable<Alpaca.Markets.IBar>>> symbolTimeFrameBars,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Performs bulk update of cache metadata
    /// </summary>
    Task<BulkOperationResult> BulkUpdateMetadataAsync(
        IEnumerable<StockCacheMetadata> metadata,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Performs optimized bulk cleanup of old data
    /// </summary>
    Task<BulkOperationResult> BulkCleanupOldDataAsync(
        int retainDays = 365,
        int batchSize = 10000,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Gets performance metrics for bulk operations
    /// </summary>
    BulkOperationMetrics GetPerformanceMetrics();
}

/// <summary>
/// Implementation of optimized bulk operations service
/// </summary>
public sealed class OptimizedBulkOperationsService : IOptimizedBulkOperationsService
{
    private readonly StockBarCacheDbContext _dbContext;
    private readonly ILogger<OptimizedBulkOperationsService> _logger;
    private readonly ConcurrentDictionary<string, BulkOperationStats> _operationStats;

    public OptimizedBulkOperationsService(
        StockBarCacheDbContext dbContext,
        ILogger<OptimizedBulkOperationsService> logger)
    {
        _dbContext = dbContext;
        _logger = logger;
        _operationStats = new ConcurrentDictionary<string, BulkOperationStats>();
    }

    public async Task<BulkOperationResult> BulkInsertBarsAsync(
        IDictionary<string, IDictionary<string, IEnumerable<Alpaca.Markets.IBar>>> symbolTimeFrameBars,
        CancellationToken cancellationToken = default)
    {
        var stopwatch = Stopwatch.StartNew();
        var operationName = "BulkInsertBars";
        var totalBars = 0;
        var processedSymbols = 0;

        try
        {
            _logger.LogInformation("Starting bulk insert for {SymbolCount} symbols", symbolTimeFrameBars.Count);

            // Optimize batch size based on available memory and data size
            const int optimalBatchSize = 5000;
            var allBars = new List<CachedStockBar>();

            // Pre-process all bars to minimize memory allocations
            foreach (var symbolEntry in symbolTimeFrameBars)
            {
                var symbol = symbolEntry.Key;
                foreach (var timeFrameEntry in symbolEntry.Value)
                {
                    var timeFrame = timeFrameEntry.Key;
                    var bars = timeFrameEntry.Value;

                    var cachedBars = bars.Select(bar => CachedStockBar.FromIBar(symbol, timeFrame, bar)).ToList();
                    allBars.AddRange(cachedBars);
                    totalBars += cachedBars.Count;
                }
                processedSymbols++;
            }

            // Process in optimized batches
            var batches = allBars.Chunk(optimalBatchSize);
            var batchCount = 0;

            foreach (var batch in batches)
            {
                cancellationToken.ThrowIfCancellationRequested();

                using var transaction = await _dbContext.Database.BeginTransactionAsync(cancellationToken);
                try
                {
                    // Use bulk operations for better performance
                    await _dbContext.CachedStockBars.AddRangeAsync(batch, cancellationToken);
                    await _dbContext.SaveChangesAsync(cancellationToken);
                    await transaction.CommitAsync(cancellationToken);

                    batchCount++;
                    if (batchCount % 10 == 0)
                    {
                        _logger.LogDebug("Processed {BatchCount} batches, {TotalBars} bars", batchCount, batchCount * optimalBatchSize);
                    }
                }
                catch
                {
                    await transaction.RollbackAsync(cancellationToken);
                    throw;
                }
            }

            stopwatch.Stop();
            RecordOperationStats(operationName, stopwatch.Elapsed, totalBars, true);

            _logger.LogInformation("Bulk insert completed: {TotalBars} bars, {ProcessedSymbols} symbols in {ElapsedMs}ms",
                totalBars, processedSymbols, stopwatch.ElapsedMilliseconds);

            return new BulkOperationResult(
                true,
                totalBars,
                processedSymbols,
                stopwatch.Elapsed,
                null);
        }
        catch (Exception ex)
        {
            stopwatch.Stop();
            RecordOperationStats(operationName, stopwatch.Elapsed, totalBars, false);

            _logger.LogError(ex, "Bulk insert failed after processing {ProcessedSymbols} symbols", processedSymbols);
            return new BulkOperationResult(
                false,
                totalBars,
                processedSymbols,
                stopwatch.Elapsed,
                ex.Message);
        }
    }

    public async Task<BulkOperationResult> BulkUpdateMetadataAsync(
        IEnumerable<StockCacheMetadata> metadata,
        CancellationToken cancellationToken = default)
    {
        var stopwatch = Stopwatch.StartNew();
        var operationName = "BulkUpdateMetadata";
        var metadataList = metadata.ToList();
        var processedCount = 0;

        try
        {
            _logger.LogDebug("Starting bulk metadata update for {Count} entries", metadataList.Count);

            using var transaction = await _dbContext.Database.BeginTransactionAsync(cancellationToken);
            try
            {
                // Use bulk operations for metadata updates
                _dbContext.StockCacheMetadata.UpdateRange(metadataList);
                await _dbContext.SaveChangesAsync(cancellationToken);
                await transaction.CommitAsync(cancellationToken);

                processedCount = metadataList.Count;
            }
            catch
            {
                await transaction.RollbackAsync(cancellationToken);
                throw;
            }

            stopwatch.Stop();
            RecordOperationStats(operationName, stopwatch.Elapsed, processedCount, true);

            _logger.LogDebug("Bulk metadata update completed: {ProcessedCount} entries in {ElapsedMs}ms",
                processedCount, stopwatch.ElapsedMilliseconds);

            return new BulkOperationResult(
                true,
                processedCount,
                processedCount,
                stopwatch.Elapsed,
                null);
        }
        catch (Exception ex)
        {
            stopwatch.Stop();
            RecordOperationStats(operationName, stopwatch.Elapsed, processedCount, false);

            _logger.LogError(ex, "Bulk metadata update failed");
            return new BulkOperationResult(
                false,
                processedCount,
                processedCount,
                stopwatch.Elapsed,
                ex.Message);
        }
    }

    public async Task<BulkOperationResult> BulkCleanupOldDataAsync(
        int retainDays = 365,
        int batchSize = 10000,
        CancellationToken cancellationToken = default)
    {
        var stopwatch = Stopwatch.StartNew();
        var operationName = "BulkCleanupOldData";
        var totalDeleted = 0;

        try
        {
            var cutoffDate = DateTime.UtcNow.AddDays(-retainDays);
            _logger.LogInformation("Starting bulk cleanup of data older than {CutoffDate}", cutoffDate);

            // Use ExecuteDeleteAsync for optimal performance (EF Core 7+)
            var deletedCount = await _dbContext.CachedStockBars
                .Where(b => b.TimeUtc < cutoffDate)
                .ExecuteDeleteAsync(cancellationToken);

            totalDeleted = deletedCount;

            stopwatch.Stop();
            RecordOperationStats(operationName, stopwatch.Elapsed, totalDeleted, true);

            _logger.LogInformation("Bulk cleanup completed: {DeletedCount} bars removed in {ElapsedMs}ms",
                deletedCount, stopwatch.ElapsedMilliseconds);

            return new BulkOperationResult(
                true,
                totalDeleted,
                1,
                stopwatch.Elapsed,
                null);
        }
        catch (Exception ex)
        {
            stopwatch.Stop();
            RecordOperationStats(operationName, stopwatch.Elapsed, totalDeleted, false);

            _logger.LogError(ex, "Bulk cleanup failed");
            return new BulkOperationResult(
                false,
                totalDeleted,
                1,
                stopwatch.Elapsed,
                ex.Message);
        }
    }

    public BulkOperationMetrics GetPerformanceMetrics()
    {
        var allStats = _operationStats.Values.ToList();
        
        return new BulkOperationMetrics(
            allStats.Sum(s => s.TotalOperations),
            allStats.Sum(s => s.TotalRecordsProcessed),
            allStats.Where(s => s.TotalOperations > 0).Average(s => s.AverageLatencyMs),
            allStats.Sum(s => s.SuccessfulOperations) / (double)Math.Max(1, allStats.Sum(s => s.TotalOperations)),
            allStats.GroupBy(s => s.OperationName).ToDictionary(g => g.Key, g => g.First())
        );
    }

    private void RecordOperationStats(string operationName, TimeSpan elapsed, int recordsProcessed, bool success)
    {
        _operationStats.AddOrUpdate(operationName,
            new BulkOperationStats(operationName, 1, success ? 1 : 0, recordsProcessed, elapsed.TotalMilliseconds),
            (key, existing) => new BulkOperationStats(
                operationName,
                existing.TotalOperations + 1,
                existing.SuccessfulOperations + (success ? 1 : 0),
                existing.TotalRecordsProcessed + recordsProcessed,
                (existing.AverageLatencyMs * existing.TotalOperations + elapsed.TotalMilliseconds) / (existing.TotalOperations + 1)
            ));
    }
}

/// <summary>
/// Result of a bulk operation
/// </summary>
public readonly record struct BulkOperationResult(
    bool Success,
    int RecordsProcessed,
    int EntitiesProcessed,
    TimeSpan Duration,
    string? ErrorMessage
);

/// <summary>
/// Statistics for bulk operations
/// </summary>
public readonly record struct BulkOperationStats(
    string OperationName,
    int TotalOperations,
    int SuccessfulOperations,
    int TotalRecordsProcessed,
    double AverageLatencyMs
);

/// <summary>
/// Performance metrics for bulk operations
/// </summary>
public readonly record struct BulkOperationMetrics(
    int TotalOperations,
    int TotalRecordsProcessed,
    double AverageLatencyMs,
    double SuccessRate,
    IDictionary<string, BulkOperationStats> OperationBreakdown
);
