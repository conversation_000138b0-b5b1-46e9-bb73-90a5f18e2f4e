# Test Data Factory Usage Guide

## Overview

The `TestDataFactory` provides fast, lightweight test data generation optimized for speed over realism. Use this instead of creating hundreds of mock objects in loops.

## Key Principles

1. **Minimal Data**: Generate only what's needed for each test
2. **Fast Execution**: Prefer 10 bars over 250 bars when possible
3. **Deterministic**: Use fixed seeds for reproducible tests
4. **Purpose-Built**: Different methods for different test scenarios

## Common Usage Patterns

### Basic Bar Generation

```csharp
// Instead of creating 250 bars in a loop:
var bars = TestDataFactory.CreateMinimalBars("AAPL", count: 10, basePrice: 150m);

// For trending data:
var upTrendBars = TestDataFactory.CreateMinimalBars("AAPL", count: 10, basePrice: 100m, trend: 1.1m);
var downTrendBars = TestDataFactory.CreateMinimalBars("AAPL", count: 10, basePrice: 100m, trend: 0.9m);
```

### SMA Testing

```csharp
// Optimized for SMA calculations - generates exactly what's needed:
var bars = TestDataFactory.CreateSmaOptimizedBars("SPY", currentPrice: 450m, smaValue: 400m, smaLength: 200);

// Creates 200 bars where most are at 400m, last is at 450m
// This ensures SMA200 ≈ 400m for testing
```

### Return-Based Testing

```csharp
// For testing 6-month returns:
var bars = TestDataFactory.CreateReturnBars("AAPL", startPrice: 100m, endPrice: 125m, count: 10);
// Creates linear progression from 100 to 125 (25% return)
```

### Volatility Testing

```csharp
// For ATR calculations:
var bars = TestDataFactory.CreateVolatilityBars("AAPL", basePrice: 150m, atrValue: 3m, count: 14);
// Creates 14 bars with consistent ATR around 3.0
```

### Mock API Responses

```csharp
// Instead of manually creating Mock<IPage<IBar>>:
var response = TestDataFactory.CreateMockBarPage("AAPL", count: 10);

// Or with existing bars:
var bars = TestDataFactory.CreateMinimalBars("AAPL");
var response = TestDataFactory.CreateMockBarPage(bars);
```

### Filter Testing

```csharp
// Bars that pass common filters (price > $10, volume > 1M):
var passingBars = TestDataFactory.CreateFilterPassingBars("AAPL");

// Bars that fail filters:
var failingBars = TestDataFactory.CreateFilterFailingBars("PENNY");
```

### Multi-Symbol Testing

```csharp
// Generate data for multiple symbols efficiently:
var symbols = new[] { "AAPL", "MSFT", "GOOGL" };
var multiSymbolData = TestDataFactory.CreateMultiSymbolBars(symbols, barsPerSymbol: 10);

foreach (var kvp in multiSymbolData)
{
    var symbol = kvp.Key;
    var bars = kvp.Value;
    // Setup mocks for each symbol
}
```

### Account and Position Mocks

```csharp
// Quick account creation:
var account = TestDataFactory.CreateMockAccount(equity: 100000m);

// Quick position creation:
var position = TestDataFactory.CreateMockPosition("AAPL", quantity: 100, marketValue: 15000m, entryPrice: 150m);

// Quick order creation:
var order = TestDataFactory.CreateMockOrder("AAPL", quantity: 100, price: 150m);
```

## Constants

Use `TestConstants` to avoid magic numbers:

```csharp
// Instead of hardcoded values:
var bars = TestDataFactory.CreateMinimalBars("AAPL", TestConstants.MinimalBarCount);
var smaData = TestDataFactory.CreateSmaOptimizedBars("SPY", 450m, 400m, TestConstants.LongSmaBarCount);

// Test symbols:
foreach (var symbol in TestConstants.SmallTestSymbols) // ["TEST1", "TEST2"]
{
    // Fast tests with minimal symbols
}
```

## Performance Guidelines

### ✅ Do This (Fast)

```csharp
// Use minimal data:
var bars = TestDataFactory.CreateMinimalBars("AAPL", 10);

// Use constants:
var count = TestConstants.MinimalBarCount;

// Use purpose-built methods:
var smaData = TestDataFactory.CreateSmaOptimizedBars("SPY", 450m, 400m, 200);
```

### ❌ Don't Do This (Slow)

```csharp
// Don't create hundreds of mocks in loops:
for (int i = 0; i < 250; i++)
{
    var mockBar = new Mock<IBar>();
    // ... lots of setup
}

// Don't use realistic but unnecessary complexity:
var complexBars = CreateRealisticBarsWithVolatilityAndTrends(symbol, 250);
```

## Migration Examples

### Before (Slow)

```csharp
private List<IBar> CreateMockBars(int count)
{
    var bars = new List<IBar>();
    for (int i = 0; i < count; i++)
    {
        var mockBar = new Mock<IBar>();
        mockBar.Setup(x => x.Close).Returns(100m + i);
        mockBar.Setup(x => x.Open).Returns(99m + i);
        // ... more setup
        bars.Add(mockBar.Object);
    }
    return bars;
}
```

### After (Fast)

```csharp
private List<IBar> CreateMockBars(int count)
{
    return TestDataFactory.CreateMinimalBars("TEST", Math.Min(count, TestConstants.MinimalBarCount));
}
```

## Test Categories

- **Unit Tests**: Use `TestConstants.MinimalBarCount` (10 bars)
- **SMA Tests**: Use `TestConstants.SmaBarCount` (50) or `TestConstants.LongSmaBarCount` (200)
- **Integration Tests**: Can use larger counts if needed, but prefer mocking external calls

## Best Practices

1. **Start Small**: Use 10 bars unless you specifically need more
2. **Purpose-Built**: Use the right factory method for your test scenario
3. **Constants**: Use `TestConstants` instead of magic numbers
4. **Deterministic**: Factory uses fixed seeds for reproducible tests
5. **Fast First**: Optimize for test speed, add complexity only when needed
