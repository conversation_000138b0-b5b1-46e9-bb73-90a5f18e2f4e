using SmaTrendFollower.Models;
using Microsoft.Extensions.Logging;

namespace SmaTrendFollower.Services;

/// <summary>
/// Safe trade executor that wraps the original executor with comprehensive safety checks
/// </summary>
public sealed class SafeTradeExecutor : ITradeExecutor
{
    private readonly ITradeExecutor _innerExecutor;
    private readonly ITradingSafetyGuard _safetyGuard;
    private readonly ILogger<SafeTradeExecutor> _logger;

    public SafeTradeExecutor(
        ITradeExecutor innerExecutor, 
        ITradingSafetyGuard safetyGuard, 
        ILogger<SafeTradeExecutor> logger)
    {
        _innerExecutor = innerExecutor;
        _safetyGuard = safetyGuard;
        _logger = logger;
    }

    public async Task ExecuteTradeAsync(TradingSignal signal, decimal quantity)
    {
        _logger.LogInformation("Starting safety validation for {Symbol} trade", signal.Symbol);

        // Perform comprehensive safety validation
        var safetyResult = await _safetyGuard.ValidateTradeAsync(signal, quantity);

        if (!safetyResult.IsAllowed)
        {
            var logLevel = safetyResult.Level switch
            {
                SafetyLevel.Critical => LogLevel.Critical,
                SafetyLevel.Error => LogLevel.Error,
                SafetyLevel.Warning => LogLevel.Warning,
                _ => LogLevel.Information
            };

            _logger.Log(logLevel, "Trade blocked by safety guard for {Symbol}: {Reason}", 
                signal.Symbol, safetyResult.Reason);
            return;
        }

        _logger.LogInformation("Safety validation passed for {Symbol}: {Reason}", 
            signal.Symbol, safetyResult.Reason);

        // Safety checks passed, execute the trade
        try
        {
            await _innerExecutor.ExecuteTradeAsync(signal, quantity);
            _logger.LogInformation("Trade executed successfully for {Symbol}", signal.Symbol);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error executing trade for {Symbol} after safety validation passed", signal.Symbol);
            throw;
        }
    }
}
