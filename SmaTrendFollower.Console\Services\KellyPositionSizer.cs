using Microsoft.Extensions.Logging;
using SmaTrendFollower.Models;

namespace SmaTrendFollower.Services;

/// <summary>
/// Interface for Kelly criterion position sizing
/// </summary>
public interface IKellyPositionSizer
{
    /// <summary>
    /// Calculates optimal position size using Kelly criterion
    /// </summary>
    Task<decimal> CalculateOptimalPositionAsync(string symbol, decimal accountEquity, decimal winRate, decimal avgWinLoss);

    /// <summary>
    /// Calculates Kelly position with enhanced signal data
    /// </summary>
    Task<KellyPositionResult> CalculateKellyPositionAsync(TradingSignal signal, decimal accountEquity);

    /// <summary>
    /// Calculates volatility-adjusted position size
    /// </summary>
    Task<KellyPositionResult> CalculateVolatilityAdjustedPositionAsync(
        TradingSignal signal, 
        decimal accountEquity, 
        decimal vixLevel);

    /// <summary>
    /// Calculates fractional Kelly position (more conservative)
    /// </summary>
    Task<KellyPositionResult> CalculateFractionalKellyAsync(
        TradingSignal signal, 
        decimal accountEquity, 
        decimal kellyFraction = 0.25m);

    /// <summary>
    /// Analyzes historical performance to estimate Kelly parameters
    /// </summary>
    Task<KellyParameters> EstimateKellyParametersAsync(string symbol, int lookbackDays = 252);
}

/// <summary>
/// Kelly criterion position sizing service for optimal capital allocation
/// </summary>
public sealed class KellyPositionSizer : IKellyPositionSizer
{
    private readonly ILogger<KellyPositionSizer> _logger;
    private readonly IMarketDataService _marketDataService;
    private readonly Random _random;

    public KellyPositionSizer(
        ILogger<KellyPositionSizer> logger,
        IMarketDataService marketDataService)
    {
        _logger = logger;
        _marketDataService = marketDataService;
        _random = new Random(42); // Fixed seed for reproducibility
    }

    public async Task<decimal> CalculateOptimalPositionAsync(string symbol, decimal accountEquity, decimal winRate, decimal avgWinLoss)
    {
        _logger.LogInformation("Calculating optimal Kelly position for {Symbol}, equity: ${Equity:N0}", 
            symbol, accountEquity);

        try
        {
            await Task.Delay(100); // Simulate computation time

            // Kelly formula: f = (bp - q) / b
            // where f = fraction of capital to wager
            //       b = odds received on the wager (avg win / avg loss)
            //       p = probability of winning
            //       q = probability of losing (1 - p)

            var p = (double)winRate;
            var q = 1 - p;
            var b = (double)avgWinLoss;

            var kellyFraction = (b * p - q) / b;
            
            // Cap Kelly fraction at 25% for risk management
            kellyFraction = Math.Max(0, Math.Min(0.25, kellyFraction));

            var optimalPosition = accountEquity * (decimal)kellyFraction;

            _logger.LogInformation("Kelly calculation: WinRate={WinRate:P1}, AvgWinLoss={Ratio:F2}, " +
                                 "Kelly%={Kelly:P1}, Position=${Position:N0}",
                winRate, avgWinLoss, (decimal)kellyFraction, optimalPosition);

            return optimalPosition;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Kelly position calculation failed for {Symbol}", symbol);
            throw;
        }
    }

    public async Task<KellyPositionResult> CalculateKellyPositionAsync(TradingSignal signal, decimal accountEquity)
    {
        _logger.LogInformation("Calculating Kelly position for {Symbol} signal", signal.Symbol);

        try
        {
            // Estimate Kelly parameters from signal data
            var parameters = await EstimateKellyParametersFromSignal(signal);
            
            var kellyFraction = CalculateKellyFraction(parameters.WinRate, parameters.AvgWinLoss);
            var positionValue = accountEquity * kellyFraction;
            var quantity = Math.Floor(positionValue / signal.Price);
            var stopLossPrice = signal.Price - (2 * signal.Atr); // 2x ATR stop

            var result = new KellyPositionResult(
                signal.Symbol,
                quantity,
                signal.Price,
                positionValue,
                kellyFraction,
                stopLossPrice,
                parameters,
                DateTime.UtcNow
            );

            _logger.LogInformation("Kelly position calculated: {Qty} shares at ${Price:F2}, " +
                                 "Kelly fraction: {Fraction:P1}",
                quantity, signal.Price, kellyFraction);

            return result;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Kelly position calculation failed for {Symbol}", signal.Symbol);
            throw;
        }
    }

    public async Task<KellyPositionResult> CalculateVolatilityAdjustedPositionAsync(
        TradingSignal signal, 
        decimal accountEquity, 
        decimal vixLevel)
    {
        _logger.LogInformation("Calculating volatility-adjusted Kelly position for {Symbol}, VIX: {VIX:F1}", 
            signal.Symbol, vixLevel);

        try
        {
            var baseResult = await CalculateKellyPositionAsync(signal, accountEquity);
            
            // Adjust position size based on VIX level
            var volatilityAdjustment = CalculateVolatilityAdjustment(vixLevel);
            var adjustedFraction = baseResult.KellyFraction * volatilityAdjustment;
            var adjustedValue = accountEquity * adjustedFraction;
            var adjustedQuantity = Math.Floor(adjustedValue / signal.Price);

            var adjustedResult = baseResult with
            {
                Quantity = adjustedQuantity,
                PositionValue = adjustedValue,
                KellyFraction = adjustedFraction
            };

            _logger.LogInformation("Volatility-adjusted position: {Qty} shares (adjustment: {Adj:P1})",
                adjustedQuantity, volatilityAdjustment);

            return adjustedResult;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Volatility-adjusted Kelly calculation failed for {Symbol}", signal.Symbol);
            throw;
        }
    }

    public async Task<KellyPositionResult> CalculateFractionalKellyAsync(
        TradingSignal signal, 
        decimal accountEquity, 
        decimal kellyFraction = 0.25m)
    {
        _logger.LogInformation("Calculating fractional Kelly position for {Symbol}, fraction: {Fraction:P1}", 
            signal.Symbol, kellyFraction);

        try
        {
            var fullKellyResult = await CalculateKellyPositionAsync(signal, accountEquity);
            
            // Apply fractional Kelly (typically 25% of full Kelly)
            var fractionalKellyFraction = fullKellyResult.KellyFraction * kellyFraction;
            var fractionalValue = accountEquity * fractionalKellyFraction;
            var fractionalQuantity = Math.Floor(fractionalValue / signal.Price);

            var fractionalResult = fullKellyResult with
            {
                Quantity = fractionalQuantity,
                PositionValue = fractionalValue,
                KellyFraction = fractionalKellyFraction
            };

            _logger.LogInformation("Fractional Kelly position: {Qty} shares ({Fraction:P1} of full Kelly)",
                fractionalQuantity, kellyFraction);

            return fractionalResult;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Fractional Kelly calculation failed for {Symbol}", signal.Symbol);
            throw;
        }
    }

    public async Task<KellyParameters> EstimateKellyParametersAsync(string symbol, int lookbackDays = 252)
    {
        _logger.LogInformation("Estimating Kelly parameters for {Symbol} over {Days} days", 
            symbol, lookbackDays);

        try
        {
            await Task.Delay(200); // Simulate data retrieval and analysis

            // Simulate historical analysis
            var totalTrades = 50 + _random.Next(100); // 50-150 trades
            var winningTrades = (int)(totalTrades * (0.45 + _random.NextDouble() * 0.2)); // 45-65% win rate
            var losingTrades = totalTrades - winningTrades;

            var winRate = (decimal)winningTrades / totalTrades;
            
            // Simulate average win/loss ratio
            var avgWin = 0.08m + (decimal)(_random.NextDouble() * 0.12); // 8-20% average win
            var avgLoss = 0.04m + (decimal)(_random.NextDouble() * 0.06); // 4-10% average loss
            var avgWinLoss = avgWin / avgLoss;

            // Calculate other statistics
            var maxWin = avgWin * (1.5m + (decimal)(_random.NextDouble() * 1.0)); // 1.5-2.5x avg win
            var maxLoss = avgLoss * (1.2m + (decimal)(_random.NextDouble() * 0.8)); // 1.2-2.0x avg loss
            var sharpeRatio = 0.8m + (decimal)(_random.NextDouble() * 1.2); // 0.8-2.0 Sharpe

            var parameters = new KellyParameters(
                winRate,
                avgWinLoss,
                avgWin,
                avgLoss,
                maxWin,
                maxLoss,
                totalTrades,
                sharpeRatio,
                DateTime.UtcNow
            );

            _logger.LogInformation("Kelly parameters estimated: WinRate={WinRate:P1}, " +
                                 "AvgWinLoss={Ratio:F2}, Trades={Trades}",
                winRate, avgWinLoss, totalTrades);

            return parameters;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Kelly parameters estimation failed for {Symbol}", symbol);
            throw;
        }
    }

    private async Task<KellyParameters> EstimateKellyParametersFromSignal(TradingSignal signal)
    {
        // Use signal data to estimate Kelly parameters
        var estimatedWinRate = 0.55m + (signal.SixMonthReturn * 0.1m); // Higher returns suggest higher win rate
        estimatedWinRate = Math.Max(0.35m, Math.Min(0.75m, estimatedWinRate)); // Cap between 35-75%

        var volatility = signal.Atr / signal.Price;
        var avgWinLoss = 1.5m + (1 / Math.Max(0.01m, volatility * 10)); // Lower volatility = better win/loss ratio

        return new KellyParameters(
            estimatedWinRate,
            avgWinLoss,
            0.12m, // Estimated 12% average win
            0.06m, // Estimated 6% average loss
            0.25m, // Estimated 25% max win
            0.15m, // Estimated 15% max loss
            100,   // Estimated sample size
            1.2m,  // Estimated Sharpe ratio
            DateTime.UtcNow
        );
    }

    private decimal CalculateKellyFraction(decimal winRate, decimal avgWinLoss)
    {
        var p = (double)winRate;
        var q = 1 - p;
        var b = (double)avgWinLoss;

        var kellyFraction = (b * p - q) / b;
        
        // Cap at 25% for risk management and ensure non-negative
        return (decimal)Math.Max(0, Math.Min(0.25, kellyFraction));
    }

    private decimal CalculateVolatilityAdjustment(decimal vixLevel)
    {
        // Reduce position size when VIX is high
        if (vixLevel > 30) return 0.5m;  // 50% of normal size when VIX > 30
        if (vixLevel > 25) return 0.7m;  // 70% of normal size when VIX > 25
        if (vixLevel > 20) return 0.85m; // 85% of normal size when VIX > 20
        
        return 1.0m; // Normal size when VIX <= 20
    }
}

/// <summary>
/// Kelly position sizing result
/// </summary>
public record KellyPositionResult(
    string Symbol,
    decimal Quantity,
    decimal Price,
    decimal PositionValue,
    decimal KellyFraction,
    decimal StopLossPrice,
    KellyParameters Parameters,
    DateTime CalculatedAt
);

/// <summary>
/// Kelly criterion parameters
/// </summary>
public record KellyParameters(
    decimal WinRate,
    decimal AvgWinLoss,
    decimal AvgWin,
    decimal AvgLoss,
    decimal MaxWin,
    decimal MaxLoss,
    int SampleSize,
    decimal SharpeRatio,
    DateTime EstimatedAt
);
