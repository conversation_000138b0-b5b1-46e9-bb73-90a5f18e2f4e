namespace SmaTrendFollower.Services.ErrorHandling;

/// <summary>
/// Circuit breaker service for managing external service availability
/// </summary>
public interface ICircuitBreakerService
{
    /// <summary>
    /// Execute an operation with circuit breaker protection
    /// </summary>
    /// <typeparam name="T">Return type</typeparam>
    /// <param name="serviceName">Name of the external service</param>
    /// <param name="operation">Operation to execute</param>
    /// <param name="fallback">Optional fallback operation</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Result of the operation or fallback</returns>
    Task<T> ExecuteAsync<T>(
        string serviceName,
        Func<Task<T>> operation,
        Func<Task<T>>? fallback = null,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Execute an operation with circuit breaker protection (no return value)
    /// </summary>
    /// <param name="serviceName">Name of the external service</param>
    /// <param name="operation">Operation to execute</param>
    /// <param name="fallback">Optional fallback operation</param>
    /// <param name="cancellationToken">Cancellation token</param>
    Task ExecuteAsync(
        string serviceName,
        Func<Task> operation,
        Func<Task>? fallback = null,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Check if circuit breaker is open for a service
    /// </summary>
    /// <param name="serviceName">Name of the service</param>
    /// <returns>True if circuit breaker is open</returns>
    bool IsCircuitOpen(string serviceName);

    /// <summary>
    /// Get circuit breaker state for a service
    /// </summary>
    /// <param name="serviceName">Name of the service</param>
    /// <returns>Circuit breaker state information</returns>
    CircuitBreakerInfo GetCircuitState(string serviceName);

    /// <summary>
    /// Manually open circuit breaker for a service
    /// </summary>
    /// <param name="serviceName">Name of the service</param>
    /// <param name="reason">Reason for opening the circuit</param>
    void OpenCircuit(string serviceName, string reason);

    /// <summary>
    /// Manually close circuit breaker for a service
    /// </summary>
    /// <param name="serviceName">Name of the service</param>
    void CloseCircuit(string serviceName);

    /// <summary>
    /// Reset circuit breaker statistics for a service
    /// </summary>
    /// <param name="serviceName">Name of the service</param>
    void ResetCircuit(string serviceName);

    /// <summary>
    /// Get all circuit breaker states
    /// </summary>
    /// <returns>Dictionary of service names and their circuit states</returns>
    Dictionary<string, CircuitBreakerInfo> GetAllCircuitStates();
}

/// <summary>
/// Circuit breaker configuration options
/// </summary>
public sealed class CircuitBreakerOptions
{
    /// <summary>
    /// Number of consecutive failures before opening the circuit
    /// </summary>
    public int FailureThreshold { get; set; } = 5;

    /// <summary>
    /// Duration to keep circuit open before allowing test requests
    /// </summary>
    public TimeSpan OpenTimeout { get; set; } = TimeSpan.FromMinutes(1);

    /// <summary>
    /// Number of successful test requests required to close the circuit
    /// </summary>
    public int SuccessThreshold { get; set; } = 3;

    /// <summary>
    /// Time window for counting failures
    /// </summary>
    public TimeSpan FailureWindow { get; set; } = TimeSpan.FromMinutes(5);

    /// <summary>
    /// Whether to enable automatic recovery attempts
    /// </summary>
    public bool EnableAutoRecovery { get; set; } = true;

    /// <summary>
    /// Minimum time between recovery attempts
    /// </summary>
    public TimeSpan RecoveryInterval { get; set; } = TimeSpan.FromSeconds(30);

    /// <summary>
    /// Maximum number of concurrent test requests in half-open state
    /// </summary>
    public int MaxConcurrentTests { get; set; } = 1;

    /// <summary>
    /// Service-specific configurations
    /// </summary>
    public Dictionary<string, CircuitBreakerServiceConfig> ServiceConfigs { get; set; } = new();
}

/// <summary>
/// Service-specific circuit breaker configuration
/// </summary>
public sealed class CircuitBreakerServiceConfig
{
    public int? FailureThreshold { get; set; }
    public TimeSpan? OpenTimeout { get; set; }
    public int? SuccessThreshold { get; set; }
    public TimeSpan? FailureWindow { get; set; }
    public bool? EnableAutoRecovery { get; set; }
    public TimeSpan? RecoveryInterval { get; set; }
    public int? MaxConcurrentTests { get; set; }
}

/// <summary>
/// Circuit breaker state information
/// </summary>
public sealed class CircuitBreakerInfo
{
    public string ServiceName { get; init; } = string.Empty;
    public CircuitBreakerState State { get; init; }
    public int FailureCount { get; init; }
    public int SuccessCount { get; init; }
    public DateTime LastFailureTime { get; init; }
    public DateTime LastSuccessTime { get; init; }
    public DateTime? OpenedAt { get; init; }
    public DateTime? NextRetryAt { get; init; }
    public string? LastError { get; init; }
    public TimeSpan? AverageResponseTime { get; init; }
    public double SuccessRate { get; init; }
    public int TotalRequests { get; init; }
    public bool IsHealthy => State == CircuitBreakerState.Closed && SuccessRate > 0.8;
}

/// <summary>
/// Circuit breaker states
/// </summary>
public enum CircuitBreakerState
{
    /// <summary>
    /// Circuit is closed - requests are allowed through
    /// </summary>
    Closed,

    /// <summary>
    /// Circuit is open - requests are blocked
    /// </summary>
    Open,

    /// <summary>
    /// Circuit is half-open - limited test requests are allowed
    /// </summary>
    HalfOpen
}

/// <summary>
/// Circuit breaker event arguments
/// </summary>
public sealed class CircuitBreakerEventArgs : EventArgs
{
    public string ServiceName { get; init; } = string.Empty;
    public CircuitBreakerState PreviousState { get; init; }
    public CircuitBreakerState NewState { get; init; }
    public string? Reason { get; init; }
    public DateTime Timestamp { get; init; } = DateTime.UtcNow;
    public Exception? Exception { get; init; }
    public TimeSpan? Duration { get; init; }
}
