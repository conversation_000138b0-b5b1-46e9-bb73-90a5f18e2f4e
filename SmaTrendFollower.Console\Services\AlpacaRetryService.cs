using Microsoft.Extensions.Logging;

namespace SmaTrendFollower.Services;

/// <summary>
/// Specialized retry service for Alpaca API with optimized rate limiting (200 req/min)
/// </summary>
public sealed class AlpacaRetryService : RetryService, IAlpacaRetryService
{
    public AlpacaRetryService(ILogger<AlpacaRetryService> logger) 
        : base(logger, RetryConfiguration.ForAlpaca)
    {
    }

    public async Task<T> ExecuteAlpacaAsync<T>(Func<Task<T>> operation, string operationName = "Unknown", CancellationToken cancellationToken = default)
    {
        // Use the base implementation with Alpaca-specific configuration
        return await ExecuteAsync(operation, $"Alpaca-{operationName}", cancellationToken);
    }
}

/// <summary>
/// Specialized retry service for Polygon API with optimized rate limiting (5 req/sec)
/// </summary>
public sealed class PolygonRetryService : RetryService, IPolygonRetryService
{
    public PolygonRetryService(ILogger<PolygonRetryService> logger) 
        : base(logger, RetryConfiguration.ForPolygon)
    {
    }

    public async Task<T> ExecutePolygonAsync<T>(Func<Task<T>> operation, string operationName = "Unknown", CancellationToken cancellationToken = default)
    {
        // Use the base implementation with Polygon-specific configuration
        return await ExecuteAsync(operation, $"Polygon-{operationName}", cancellationToken);
    }
}
