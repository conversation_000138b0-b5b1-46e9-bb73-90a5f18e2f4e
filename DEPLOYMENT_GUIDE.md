# 🚀 **SmaTrendFollower - Production Deployment Guide**

## **📋 DEPLOYMENT STATUS: READY FOR LIVE TRADING**

The SmaTrendFollower system has been thoroughly tested and optimized for production deployment. This guide provides step-by-step instructions for deploying the system in a live trading environment.

---

## 🔧 **PREREQUISITES**

### **System Requirements**
- **.NET 8 Runtime** or later
- **Windows 10/11** or **Linux** (Ubuntu 20.04+)
- **4GB RAM** minimum (8GB recommended)
- **1GB disk space** for application and data cache
- **Stable internet connection** for real-time market data

### **Required Accounts & API Keys**
1. **Alpaca Markets Account** (Paper or Live)
   - API Key and Secret Key
   - Account with sufficient buying power
   - Market data subscription (Level 1 minimum)

2. **Polygon.io Account** (Optional but recommended)
   - API Key for enhanced market data
   - Indices Starter subscription for SPY/VIX data

3. **Discord Bot** (Optional for notifications)
   - Bot token for trade alerts
   - Channel ID for notifications

4. **Redis Server** (Optional for caching)
   - Local or remote Redis instance
   - Improves performance for frequent operations

---

## 📦 **INSTALLATION STEPS**

### **Step 1: Download and Extract**
```bash
# Clone the repository
git clone https://github.com/patco1/SmaTrendFollower.git
cd SmaTrendFollower

# Or download and extract the release package
# Extract SmaTrendFollower-v1.0.zip to desired directory
```

### **Step 2: Install Dependencies**
```bash
# Restore NuGet packages
dotnet restore

# Build the application
dotnet build --configuration Release
```

### **Step 3: Configuration Setup**
```bash
# Copy the environment template
cp .env.example .env

# Edit the configuration file
nano .env  # or use your preferred editor
```

### **Step 4: Environment Configuration**
Edit the `.env` file with your credentials:

```env
# === REQUIRED CONFIGURATION ===
# Alpaca Trading Account
ALPACA_API_KEY=your_alpaca_api_key_here
ALPACA_SECRET_KEY=your_alpaca_secret_key_here
ALPACA_ENVIRONMENT=paper  # Change to 'live' for production

# === OPTIONAL CONFIGURATION ===
# Polygon Market Data (recommended)
POLYGON_API_KEY=your_polygon_api_key_here

# Redis Caching (improves performance)
REDIS_CONNECTION_STRING=localhost:6379

# Discord Notifications
DISCORD_BOT_TOKEN=your_discord_bot_token_here
DISCORD_CHANNEL_ID=your_discord_channel_id_here

# === TRADING PARAMETERS ===
# Risk Management
MAX_POSITIONS=4                    # Maximum concurrent positions
RISK_PER_TRADE=0.01               # 1% risk per trade
MAX_DAILY_LOSS=0.015              # 1.5% maximum daily loss

# Signal Generation
UNIVERSE_SIZE=500                  # Number of stocks to screen
MIN_PRICE=10.00                   # Minimum stock price
MIN_VOLUME=1000000                # Minimum daily volume
MAX_VOLATILITY=0.03               # Maximum volatility (3%)

# Performance
ENABLE_CACHING=true               # Enable SQLite/Redis caching
CACHE_EXPIRY_HOURS=24             # Cache expiration time
```

---

## 🎯 **DEPLOYMENT MODES**

### **1. Paper Trading Mode (Recommended for Testing)**
```bash
# Set environment to paper trading
ALPACA_ENVIRONMENT=paper

# Run with dry-run for initial testing
dotnet run --project SmaTrendFollower.Console --dry-run

# Run paper trading
dotnet run --project SmaTrendFollower.Console
```

### **2. Live Trading Mode (Production)**
```bash
# Set environment to live trading
ALPACA_ENVIRONMENT=live

# Final verification run
dotnet run --project SmaTrendFollower.Console --verify-signals

# Deploy for live trading
dotnet run --project SmaTrendFollower.Console
```

### **3. Scheduled Execution (Recommended)**
Set up automated execution using system scheduler:

**Windows (Task Scheduler):**
```batch
# Create batch file: run_trading.bat
@echo off
cd /d "C:\path\to\SmaTrendFollower"
dotnet run --project SmaTrendFollower.Console
```

**Linux (Cron):**
```bash
# Add to crontab (run at 9:35 AM EST weekdays)
35 9 * * 1-5 cd /path/to/SmaTrendFollower && dotnet run --project SmaTrendFollower.Console
```

---

## 🛡️ **SAFETY CHECKLIST**

### **Pre-Deployment Validation**
- [ ] **Account Verification**: Confirm Alpaca account has sufficient buying power
- [ ] **API Key Testing**: Verify all API keys work correctly
- [ ] **Paper Trading**: Run in paper mode for at least 1 week
- [ ] **Risk Parameters**: Confirm risk settings match your tolerance
- [ ] **Market Hours**: Verify trading only occurs during market hours
- [ ] **Position Limits**: Test maximum position and loss limits
- [ ] **Notification Setup**: Confirm Discord alerts are working

### **Live Trading Checklist**
- [ ] **Final Build**: Ensure latest code is compiled in Release mode
- [ ] **Environment Variables**: Double-check all configuration settings
- [ ] **Account Balance**: Verify sufficient funds for intended trading
- [ ] **Market Conditions**: Confirm SPY > SMA200 for initial deployment
- [ ] **Monitoring Setup**: Prepare to monitor first few trades closely
- [ ] **Stop Loss**: Verify stop-loss orders are being placed correctly
- [ ] **Emergency Stop**: Know how to quickly halt the system if needed

---

## 📊 **MONITORING & MAINTENANCE**

### **Real-Time Monitoring**
```bash
# Check system status
dotnet run --project SmaTrendFollower.Console --status

# View recent performance
dotnet run --project SmaTrendFollower.Console --performance

# Monitor logs
tail -f logs/trading_$(date +%Y%m%d).log
```

### **Daily Maintenance Tasks**
1. **Review Trading Results**: Check overnight performance and P&L
2. **Verify Positions**: Confirm all positions have proper stop-losses
3. **Check System Health**: Monitor for any errors or warnings
4. **Update Market Data**: Ensure data feeds are current
5. **Backup Configuration**: Save current settings and performance data

### **Weekly Maintenance Tasks**
1. **Performance Analysis**: Review weekly returns and drawdown
2. **Risk Assessment**: Evaluate if risk parameters need adjustment
3. **System Updates**: Check for software updates and security patches
4. **Database Cleanup**: Clear old cache data and logs
5. **Strategy Review**: Analyze signal quality and execution efficiency

---

## 🚨 **EMERGENCY PROCEDURES**

### **Immediate Stop Trading**
```bash
# Emergency stop - cancels all orders and closes positions
dotnet run --project SmaTrendFollower.Console --emergency-stop

# Or manually via Alpaca dashboard:
# 1. Log into Alpaca account
# 2. Cancel all open orders
# 3. Close all positions
```

### **Common Issues & Solutions**

**Issue: API Rate Limits**
```bash
# Solution: Enable caching and reduce API calls
ENABLE_CACHING=true
CACHE_EXPIRY_HOURS=24
```

**Issue: Network Connectivity**
```bash
# Solution: Implement retry logic (already built-in)
# Monitor logs for connection errors
# Consider backup internet connection
```

**Issue: Unexpected Losses**
```bash
# Solution: Review risk parameters
MAX_DAILY_LOSS=0.01  # Reduce to 1% if needed
RISK_PER_TRADE=0.005  # Reduce position sizes
```

---

## 📈 **PERFORMANCE OPTIMIZATION**

### **Recommended Settings for Different Account Sizes**

**Small Account ($1,000 - $10,000):**
```env
MAX_POSITIONS=2
RISK_PER_TRADE=0.005
MAX_DAILY_LOSS=0.01
UNIVERSE_SIZE=100
```

**Medium Account ($10,000 - $100,000):**
```env
MAX_POSITIONS=4
RISK_PER_TRADE=0.01
MAX_DAILY_LOSS=0.015
UNIVERSE_SIZE=300
```

**Large Account ($100,000+):**
```env
MAX_POSITIONS=6
RISK_PER_TRADE=0.01
MAX_DAILY_LOSS=0.02
UNIVERSE_SIZE=500
```

---

## 🎉 **DEPLOYMENT COMPLETE**

Your SmaTrendFollower system is now ready for production trading! 

### **Next Steps:**
1. **Monitor First Week**: Watch the system closely during initial deployment
2. **Adjust Parameters**: Fine-tune settings based on performance
3. **Scale Gradually**: Increase position sizes as confidence grows
4. **Stay Informed**: Keep up with market conditions and system updates

### **Support & Updates:**
- **Documentation**: Refer to SYSTEM_OVERVIEW_2024.md for detailed information
- **Troubleshooting**: Check logs and error messages for issues
- **Updates**: Monitor the repository for system improvements

**Happy Trading! 🚀📈**
