using Serilog;

namespace SmaTrendFollower.Services;

public sealed class FileUniverseProvider : IUniverseProvider
{
    private const string UniverseFile = "universe.csv";

    public async Task<IEnumerable<string>> GetSymbolsAsync()
    {
        if (File.Exists(UniverseFile))
        {
            var lines = await File.ReadAllLinesAsync(UniverseFile);
            var symbols = lines
                .Select(line => line.Trim().ToUpperInvariant())
                .Where(s => !string.IsNullOrWhiteSpace(s));
            return symbols;
        }

        Log.Warning("{File} not found. Using default universe.", UniverseFile);
        return new[] { "SPY", "QQQ", "AAPL", "MSFT", "NVDA" };
    }
}
