using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Configuration;
using System.Text.Json;
using SmaTrendFollower.Services.ErrorHandling;

namespace SmaTrendFollower.Services;

/// <summary>
/// Enhanced Retry Service - Intelligent retry queue processor with exponential backoff
/// Processes failed operations with smart retry logic and circuit breaker patterns
/// </summary>
public sealed class EnhancedRetryService : BackgroundService, ErrorHandling.IEnhancedRetryService
{
    private readonly ILogger<EnhancedRetryService> _logger;
    private readonly ILiveStateStore _liveStateStore;
    private readonly IServiceProvider _serviceProvider;
    private readonly EnhancedRetryConfig _config;
    private readonly Dictionary<string, RetryHandler> _retryHandlers;
    private readonly Dictionary<string, RetryCircuitBreakerState> _circuitBreakers;

    public EnhancedRetryService(
        ILogger<EnhancedRetryService> logger,
        ILiveStateStore liveStateStore,
        IServiceProvider serviceProvider,
        IConfiguration configuration)
    {
        _logger = logger;
        _liveStateStore = liveStateStore;
        _serviceProvider = serviceProvider;
        _config = new EnhancedRetryConfig
        {
            ProcessingInterval = TimeSpan.FromSeconds(configuration.GetValue("RETRY_PROCESSING_INTERVAL_SECONDS", 30)),
            MaxRetryAttempts = configuration.GetValue("RETRY_MAX_ATTEMPTS", 5),
            BaseDelaySeconds = configuration.GetValue("RETRY_BASE_DELAY_SECONDS", 60),
            MaxDelaySeconds = configuration.GetValue("RETRY_MAX_DELAY_SECONDS", 3600),
            CircuitBreakerThreshold = configuration.GetValue("RETRY_CIRCUIT_BREAKER_THRESHOLD", 5),
            CircuitBreakerTimeoutMinutes = configuration.GetValue("RETRY_CIRCUIT_BREAKER_TIMEOUT_MINUTES", 10)
        };

        _retryHandlers = new Dictionary<string, RetryHandler>();
        _circuitBreakers = new Dictionary<string, RetryCircuitBreakerState>();

        RegisterDefaultHandlers();
    }

    /// <summary>
    /// Background service execution
    /// </summary>
    protected override async Task ExecuteAsync(CancellationToken stoppingToken)
    {
        _logger.LogInformation("Enhanced Retry Service starting with {Interval}s processing interval", 
            _config.ProcessingInterval.TotalSeconds);

        while (!stoppingToken.IsCancellationRequested)
        {
            try
            {
                await ProcessRetryQueueAsync(stoppingToken);
                await Task.Delay(_config.ProcessingInterval, stoppingToken);
            }
            catch (OperationCanceledException)
            {
                break;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error in retry service processing");
                await Task.Delay(TimeSpan.FromSeconds(10), stoppingToken);
            }
        }

        _logger.LogInformation("Enhanced Retry Service stopped");
    }

    /// <summary>
    /// Processes the retry queue
    /// </summary>
    private async Task ProcessRetryQueueAsync(CancellationToken cancellationToken)
    {
        var queueLength = await _liveStateStore.GetRetryQueueLengthAsync(cancellationToken);
        if (queueLength == 0)
        {
            return;
        }

        _logger.LogDebug("Processing retry queue with {Count} items", queueLength);

        var processedCount = 0;
        var maxProcessPerCycle = Math.Min(queueLength, 50); // Limit processing per cycle

        for (int i = 0; i < maxProcessPerCycle && !cancellationToken.IsCancellationRequested; i++)
        {
            var item = await _liveStateStore.DequeueRetryAsync(cancellationToken);
            if (item == null)
            {
                break;
            }

            var shouldRetry = await ProcessRetryItemAsync(item, cancellationToken);
            if (shouldRetry)
            {
                // Re-enqueue with updated retry time
                var updatedItem = item with 
                { 
                    AttemptCount = item.AttemptCount + 1,
                    NextRetryTime = CalculateNextRetryTime(item.AttemptCount + 1, item.Operation)
                };
                await _liveStateStore.EnqueueRetryAsync(updatedItem, cancellationToken);
            }

            processedCount++;
        }

        if (processedCount > 0)
        {
            _logger.LogDebug("Processed {Count} retry items", processedCount);
        }
    }

    /// <summary>
    /// Processes a single retry item
    /// </summary>
    private async Task<bool> ProcessRetryItemAsync(RetryItem item, CancellationToken cancellationToken)
    {
        try
        {
            // Check if it's time to retry
            if (DateTime.UtcNow < item.NextRetryTime)
            {
                // Re-enqueue without incrementing attempt count
                await _liveStateStore.EnqueueRetryAsync(item, cancellationToken);
                return false;
            }

            // Check circuit breaker
            if (IsCircuitBreakerOpen(item.Operation))
            {
                _logger.LogWarning("Circuit breaker open for operation {Operation}, skipping retry", item.Operation);
                return ShouldRetryAfterCircuitBreaker(item);
            }

            // Check max attempts
            if (item.AttemptCount >= _config.MaxRetryAttempts)
            {
                _logger.LogWarning("Max retry attempts reached for {Operation} (ID: {Id})", 
                    item.Operation, item.Id);
                await HandleMaxAttemptsReached(item);
                return false;
            }

            // Get handler for this operation
            if (!_retryHandlers.TryGetValue(item.Operation, out var handler))
            {
                _logger.LogWarning("No handler found for operation {Operation}, discarding item", item.Operation);
                return false;
            }

            // Execute the retry
            _logger.LogDebug("Retrying operation {Operation} (attempt {Attempt}/{Max})", 
                item.Operation, item.AttemptCount + 1, _config.MaxRetryAttempts);

            var success = await handler(item, _serviceProvider, cancellationToken);

            if (success)
            {
                _logger.LogInformation("Retry successful for {Operation} (ID: {Id}) after {Attempts} attempts", 
                    item.Operation, item.Id, item.AttemptCount + 1);
                ResetCircuitBreaker(item.Operation);
                return false; // Don't retry again
            }
            else
            {
                _logger.LogWarning("Retry failed for {Operation} (ID: {Id}), attempt {Attempt}", 
                    item.Operation, item.Id, item.AttemptCount + 1);
                IncrementCircuitBreakerFailures(item.Operation);
                return true; // Retry again
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error processing retry item {Operation} (ID: {Id})", 
                item.Operation, item.Id);
            IncrementCircuitBreakerFailures(item.Operation);
            return true; // Retry again
        }
    }

    /// <summary>
    /// Calculates next retry time with exponential backoff
    /// </summary>
    private DateTime CalculateNextRetryTime(int attemptCount, string operation)
    {
        var baseDelay = TimeSpan.FromSeconds(_config.BaseDelaySeconds);
        var maxDelay = TimeSpan.FromSeconds(_config.MaxDelaySeconds);

        // Exponential backoff: baseDelay * 2^(attemptCount-1)
        var delay = TimeSpan.FromSeconds(baseDelay.TotalSeconds * Math.Pow(2, attemptCount - 1));
        
        // Cap at max delay
        if (delay > maxDelay)
        {
            delay = maxDelay;
        }

        // Add jitter (±25%)
        var jitter = Random.Shared.NextDouble() * 0.5 - 0.25; // -0.25 to +0.25
        delay = TimeSpan.FromSeconds(delay.TotalSeconds * (1 + jitter));

        return DateTime.UtcNow.Add(delay);
    }

    /// <summary>
    /// Registers default retry handlers
    /// </summary>
    private void RegisterDefaultHandlers()
    {
        // Order placement retry
        RegisterHandler("PlaceOrder", (item, services, ct) =>
        {
            try
            {
                var orderData = JsonSerializer.Deserialize<OrderRetryData>(item.Data);
                if (orderData == null) return Task.FromResult(false);

                // Get trading service and retry order placement
                // This would integrate with your existing order placement logic
                _logger.LogInformation("Retrying order placement for {Symbol}", orderData.Symbol);
                
                // Placeholder for actual order placement logic
                // In real implementation, you'd inject ITradingService or similar
                return Task.FromResult(true); // Simulate success for now
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to retry order placement");
                return Task.FromResult(false);
            }
        });

        // Market data fetch retry
        RegisterHandler("FetchMarketData", (item, services, ct) =>
        {
            try
            {
                var fetchData = JsonSerializer.Deserialize<MarketDataRetryData>(item.Data);
                if (fetchData == null) return Task.FromResult(false);

                _logger.LogInformation("Retrying market data fetch for {Symbol}", fetchData.Symbol);
                
                // Placeholder for actual market data fetch logic
                return Task.FromResult(true); // Simulate success for now
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to retry market data fetch");
                return Task.FromResult(false);
            }
        });

        // Notification retry
        RegisterHandler("SendNotification", (item, services, ct) =>
        {
            try
            {
                var notificationData = JsonSerializer.Deserialize<NotificationRetryData>(item.Data);
                if (notificationData == null) return Task.FromResult(false);

                _logger.LogInformation("Retrying notification: {Type}", notificationData.Type);
                
                // Placeholder for actual notification logic
                return Task.FromResult(true); // Simulate success for now
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to retry notification");
                return Task.FromResult(false);
            }
        });
    }

    /// <summary>
    /// Registers a retry handler for an operation type
    /// </summary>
    public void RegisterHandler(string operationType, RetryHandler handler)
    {
        _retryHandlers[operationType] = handler;
        _logger.LogDebug("Registered retry handler for operation: {Operation}", operationType);
    }

    /// <summary>
    /// Enqueues an operation for retry
    /// </summary>
    public async Task EnqueueRetryAsync(string operation, object data, CancellationToken cancellationToken = default)
    {
        var retryItem = new RetryItem(
            Guid.NewGuid().ToString(),
            operation,
            JsonSerializer.Serialize(data),
            0,
            DateTime.UtcNow.AddSeconds(_config.BaseDelaySeconds),
            DateTime.UtcNow
        );

        await _liveStateStore.EnqueueRetryAsync(retryItem, cancellationToken);
        _logger.LogDebug("Enqueued retry for operation: {Operation}", operation);
    }

    /// <summary>
    /// Gets retry queue statistics
    /// </summary>
    public async Task<RetryQueueStats> GetStatsAsync(CancellationToken cancellationToken = default)
    {
        var queueLength = await _liveStateStore.GetRetryQueueLengthAsync(cancellationToken);
        
        return new RetryQueueStats(
            queueLength,
            _retryHandlers.Count,
            _circuitBreakers.Count(cb => cb.Value.IsOpen),
            DateTime.UtcNow
        );
    }

    // Circuit breaker methods
    private bool IsCircuitBreakerOpen(string operation)
    {
        if (!_circuitBreakers.TryGetValue(operation, out var state))
        {
            return false;
        }

        if (state.IsOpen && DateTime.UtcNow > state.OpenUntil)
        {
            // Reset circuit breaker
            _circuitBreakers[operation] = state with { IsOpen = false, FailureCount = 0 };
            return false;
        }

        return state.IsOpen;
    }

    private void IncrementCircuitBreakerFailures(string operation)
    {
        var state = _circuitBreakers.GetValueOrDefault(operation, new RetryCircuitBreakerState());
        var newFailureCount = state.FailureCount + 1;

        if (newFailureCount >= _config.CircuitBreakerThreshold)
        {
            var openUntil = DateTime.UtcNow.AddMinutes(_config.CircuitBreakerTimeoutMinutes);
            _circuitBreakers[operation] = state with 
            { 
                IsOpen = true, 
                FailureCount = newFailureCount,
                OpenUntil = openUntil
            };
            _logger.LogWarning("Circuit breaker opened for operation {Operation} until {Until}", 
                operation, openUntil);
        }
        else
        {
            _circuitBreakers[operation] = state with { FailureCount = newFailureCount };
        }
    }

    private void ResetCircuitBreaker(string operation)
    {
        if (_circuitBreakers.ContainsKey(operation))
        {
            _circuitBreakers[operation] = new RetryCircuitBreakerState();
        }
    }

    private bool ShouldRetryAfterCircuitBreaker(RetryItem item)
    {
        // For circuit breaker scenarios, retry with longer delay
        return item.AttemptCount < _config.MaxRetryAttempts;
    }

    private Task HandleMaxAttemptsReached(RetryItem item)
    {
        _logger.LogError("Retry item {Operation} (ID: {Id}) exceeded max attempts and will be discarded", 
            item.Operation, item.Id);
        
        // Could implement dead letter queue here
        // For now, just log the failure

        return Task.CompletedTask;
    }

    // Implementation of ErrorHandling.IEnhancedRetryService interface
    public async Task<T> ExecuteAsync<T>(
        Func<Task<T>> operation,
        string operationName,
        string? serviceName = null,
        CancellationToken cancellationToken = default)
    {
        try
        {
            return await operation();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Operation {Operation} failed, enqueueing for retry", operationName);

            // For now, just rethrow. In a full implementation, you'd enqueue for retry
            // and return a default value or handle differently based on the operation type
            throw;
        }
    }

    public async Task ExecuteAsync(
        Func<Task> operation,
        string operationName,
        string? serviceName = null,
        CancellationToken cancellationToken = default)
    {
        try
        {
            await operation();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Operation {Operation} failed, enqueueing for retry", operationName);
            throw;
        }
    }

    public async Task<T> ExecuteWithPolicyAsync<T>(
        Func<Task<T>> operation,
        RetryPolicy retryPolicy,
        string operationName,
        CancellationToken cancellationToken = default)
    {
        // Simple implementation - in a full version, this would use the retry policy
        return await ExecuteAsync(operation, operationName, null, cancellationToken);
    }

    public async Task<HttpResponseMessage> ExecuteHttpAsync(
        Func<Task<HttpResponseMessage>> httpOperation,
        string operationName,
        string? serviceName = null,
        CancellationToken cancellationToken = default)
    {
        return await ExecuteAsync(httpOperation, operationName, serviceName, cancellationToken);
    }

    public RetryStatistics GetStatistics()
    {
        return new RetryStatistics
        {
            TotalOperations = 0,
            SuccessfulOperations = 0,
            FailedOperations = 0,
            TotalRetries = 0,
            LastResetTime = DateTime.UtcNow
        };
    }

    public void ResetStatistics()
    {
        // Implementation would reset internal statistics
    }
}



/// <summary>
/// Retry handler delegate
/// </summary>
public delegate Task<bool> RetryHandler(RetryItem item, IServiceProvider services, CancellationToken cancellationToken);

/// <summary>
/// Configuration for enhanced retry service
/// </summary>
public record EnhancedRetryConfig
{
    public TimeSpan ProcessingInterval { get; init; } = TimeSpan.FromSeconds(30);
    public int MaxRetryAttempts { get; init; } = 5;
    public int BaseDelaySeconds { get; init; } = 60;
    public int MaxDelaySeconds { get; init; } = 3600;
    public int CircuitBreakerThreshold { get; init; } = 5;
    public int CircuitBreakerTimeoutMinutes { get; init; } = 10;
}

/// <summary>
/// Circuit breaker state for retry service
/// </summary>
public record RetryCircuitBreakerState(
    bool IsOpen = false,
    int FailureCount = 0,
    DateTime OpenUntil = default
);

/// <summary>
/// Retry queue statistics
/// </summary>
public record RetryQueueStats(
    int QueueLength,
    int RegisteredHandlers,
    int OpenCircuitBreakers,
    DateTime GeneratedAt
);

// Retry data models
public record OrderRetryData(string Symbol, decimal Price, int Quantity, string Side);
public record MarketDataRetryData(string Symbol, string DataType);
public record NotificationRetryData(string Type, string Message, string Recipient);
