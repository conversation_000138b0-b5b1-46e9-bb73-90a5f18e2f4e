using System.Collections.Concurrent;
using System.Text.Json;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;

namespace SmaTrendFollower.Services;

/// <summary>
/// Service for tracking and reporting cache performance metrics with thread-safe operations
/// Provides comprehensive monitoring of cache effectiveness and API usage patterns
/// </summary>
public sealed class CacheMetricsService : ICacheMetricsService
{
    private readonly ILogger<CacheMetricsService> _logger;
    private readonly CacheMetricsConfig _config;

    // Thread-safe collections for metrics
    private readonly ConcurrentDictionary<string, SymbolMetricsData> _symbolMetrics = new();
    private readonly ConcurrentQueue<CacheOperationEvent> _recentEvents = new();
    private readonly object _metricsLock = new();

    // Aggregate metrics
    private long _totalRequests;
    private long _cacheHits;
    private long _cacheMisses;
    private long _apiThrottleEvents;
    private long _totalApiCalls;
    private readonly ConcurrentDictionary<string, List<double>> _operationTimings = new();
    private DateTime _metricsStartTime;

    public CacheMetricsService(ILogger<CacheMetricsService> logger, IOptions<CacheMetricsConfig>? config = null)
    {
        _logger = logger;
        _config = config?.Value ?? CacheMetricsConfig.Default;
        _metricsStartTime = DateTime.UtcNow;
    }

    public void RecordCacheHit(string symbol, string timeFrame, double responseTimeMs)
    {
        Interlocked.Increment(ref _totalRequests);
        Interlocked.Increment(ref _cacheHits);

        UpdateSymbolMetrics(symbol, true, responseTimeMs);
        RecordOperationTiming("cache_read", responseTimeMs);

        if (_config.EnableRealTimeEvents)
        {
            var eventData = new Dictionary<string, object>
            {
                ["hit_type"] = "cache",
                ["response_time_ms"] = responseTimeMs
            };

            AddEvent(new CacheOperationEvent(
                DateTime.UtcNow,
                "hit",
                symbol,
                timeFrame,
                responseTimeMs,
                1,
                eventData
            ));
        }

        _logger.LogDebug("Cache hit for {Symbol} {TimeFrame} in {ResponseTime:F2}ms", 
            symbol, timeFrame, responseTimeMs);
    }

    public void RecordCacheMiss(string symbol, string timeFrame, double responseTimeMs, double apiCallTimeMs)
    {
        Interlocked.Increment(ref _totalRequests);
        Interlocked.Increment(ref _cacheMisses);
        Interlocked.Increment(ref _totalApiCalls);

        UpdateSymbolMetrics(symbol, false, responseTimeMs);
        RecordOperationTiming("api_call", apiCallTimeMs);
        RecordOperationTiming("total_response", responseTimeMs);

        if (_config.EnableRealTimeEvents)
        {
            var eventData = new Dictionary<string, object>
            {
                ["hit_type"] = "miss",
                ["response_time_ms"] = responseTimeMs,
                ["api_call_time_ms"] = apiCallTimeMs
            };

            AddEvent(new CacheOperationEvent(
                DateTime.UtcNow,
                "miss",
                symbol,
                timeFrame,
                responseTimeMs,
                1,
                eventData
            ));
        }

        _logger.LogDebug("Cache miss for {Symbol} {TimeFrame}: total {ResponseTime:F2}ms (API: {ApiTime:F2}ms)", 
            symbol, timeFrame, responseTimeMs, apiCallTimeMs);
    }

    public void RecordApiThrottle(string provider, string symbol)
    {
        Interlocked.Increment(ref _apiThrottleEvents);

        if (_config.EnableRealTimeEvents)
        {
            var eventData = new Dictionary<string, object>
            {
                ["provider"] = provider,
                ["throttle_type"] = "rate_limit"
            };

            AddEvent(new CacheOperationEvent(
                DateTime.UtcNow,
                "throttle",
                symbol,
                "",
                0,
                0,
                eventData
            ));
        }

        _logger.LogWarning("API throttle event: {Provider} for {Symbol}", provider, symbol);
    }

    public void RecordCacheOperation(string operation, double durationMs, int recordCount = 1)
    {
        RecordOperationTiming(operation, durationMs);

        if (_config.EnableRealTimeEvents)
        {
            var eventData = new Dictionary<string, object>
            {
                ["operation_type"] = operation,
                ["record_count"] = recordCount
            };

            AddEvent(new CacheOperationEvent(
                DateTime.UtcNow,
                operation,
                "",
                "",
                durationMs,
                recordCount,
                eventData
            ));
        }

        _logger.LogDebug("Cache operation {Operation}: {Duration:F2}ms for {RecordCount} records", 
            operation, durationMs, recordCount);
    }

    public CachePerformanceMetrics GetCurrentMetrics()
    {
        return GetMetricsForPeriod(_metricsStartTime, DateTime.UtcNow);
    }

    public CachePerformanceMetrics GetMetricsForPeriod(DateTime startTime, DateTime endTime)
    {
        var totalRequests = Interlocked.Read(ref _totalRequests);
        var cacheHits = Interlocked.Read(ref _cacheHits);
        var cacheMisses = Interlocked.Read(ref _cacheMisses);
        var apiThrottles = Interlocked.Read(ref _apiThrottleEvents);
        var totalApiCalls = Interlocked.Read(ref _totalApiCalls);

        var hitRatio = totalRequests > 0 ? (double)cacheHits / totalRequests : 0.0;
        var apiSavingsRatio = totalRequests > 0 ? 1.0 - ((double)totalApiCalls / totalRequests) : 0.0;

        var operationTimings = new Dictionary<string, double>();
        foreach (var kvp in _operationTimings)
        {
            var timings = kvp.Value.ToArray(); // Thread-safe copy
            operationTimings[kvp.Key] = timings.Length > 0 ? timings.Average() : 0.0;
        }

        var avgCacheTime = operationTimings.GetValueOrDefault("cache_read", 0.0);
        var avgApiTime = operationTimings.GetValueOrDefault("api_call", 0.0);

        return new CachePerformanceMetrics(
            totalRequests,
            cacheHits,
            cacheMisses,
            hitRatio,
            avgCacheTime,
            avgApiTime,
            apiThrottles,
            totalApiCalls,
            apiSavingsRatio,
            startTime,
            endTime,
            operationTimings
        );
    }

    public void ResetMetrics()
    {
        lock (_metricsLock)
        {
            _totalRequests = 0;
            _cacheHits = 0;
            _cacheMisses = 0;
            _apiThrottleEvents = 0;
            _totalApiCalls = 0;
            _symbolMetrics.Clear();
            _operationTimings.Clear();
            _metricsStartTime = DateTime.UtcNow;

            // Clear recent events
            while (_recentEvents.TryDequeue(out _)) { }
        }

        _logger.LogInformation("Cache metrics reset");
    }

    public IDictionary<string, SymbolMetrics> GetSymbolMetrics()
    {
        var result = new Dictionary<string, SymbolMetrics>();

        foreach (var kvp in _symbolMetrics)
        {
            var data = kvp.Value;
            var hitRatio = data.TotalRequests > 0 ? (double)data.CacheHits / data.TotalRequests : 0.0;
            var avgResponseTime = data.ResponseTimes.Count > 0 ? data.ResponseTimes.Average() : 0.0;

            result[kvp.Key] = new SymbolMetrics(
                kvp.Key,
                data.TotalRequests,
                data.CacheHits,
                data.TotalRequests - data.CacheHits,
                hitRatio,
                avgResponseTime,
                data.LastAccessed,
                data.TotalBarsServed
            );
        }

        return result;
    }

    public string ExportMetricsAsJson()
    {
        var metrics = GetCurrentMetrics();
        var symbolMetrics = GetSymbolMetrics();

        var exportData = new
        {
            Timestamp = DateTime.UtcNow,
            OverallMetrics = metrics,
            SymbolMetrics = symbolMetrics,
            RecentEvents = GetRecentEvents(100)
        };

        return JsonSerializer.Serialize(exportData, new JsonSerializerOptions
        {
            WriteIndented = true,
            PropertyNamingPolicy = JsonNamingPolicy.CamelCase
        });
    }

    private void UpdateSymbolMetrics(string symbol, bool wasHit, double responseTimeMs)
    {
        _symbolMetrics.AddOrUpdate(symbol,
            new SymbolMetricsData
            {
                TotalRequests = 1,
                CacheHits = wasHit ? 1 : 0,
                LastAccessed = DateTime.UtcNow,
                ResponseTimes = new List<double> { responseTimeMs },
                TotalBarsServed = 1
            },
            (key, existing) =>
            {
                existing.TotalRequests++;
                if (wasHit) existing.CacheHits++;
                existing.LastAccessed = DateTime.UtcNow;
                existing.ResponseTimes.Add(responseTimeMs);
                existing.TotalBarsServed++;

                // Keep only recent response times to prevent memory growth
                if (existing.ResponseTimes.Count > 1000)
                {
                    existing.ResponseTimes.RemoveRange(0, 500);
                }

                return existing;
            });
    }

    private void RecordOperationTiming(string operation, double durationMs)
    {
        _operationTimings.AddOrUpdate(operation,
            new List<double> { durationMs },
            (key, existing) =>
            {
                existing.Add(durationMs);
                
                // Keep only recent timings to prevent memory growth
                if (existing.Count > 1000)
                {
                    existing.RemoveRange(0, 500);
                }
                
                return existing;
            });
    }

    private void AddEvent(CacheOperationEvent eventData)
    {
        _recentEvents.Enqueue(eventData);

        // Keep only recent events to prevent memory growth
        while (_recentEvents.Count > 10000)
        {
            _recentEvents.TryDequeue(out _);
        }
    }

    private CacheOperationEvent[] GetRecentEvents(int count)
    {
        return _recentEvents.ToArray().TakeLast(count).ToArray();
    }

    private class SymbolMetricsData
    {
        public long TotalRequests { get; set; }
        public long CacheHits { get; set; }
        public DateTime LastAccessed { get; set; }
        public List<double> ResponseTimes { get; set; } = new();
        public long TotalBarsServed { get; set; }
    }
}
