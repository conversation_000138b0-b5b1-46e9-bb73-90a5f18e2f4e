using SmaTrendFollower.Models;

namespace SmaTrendFollower.Services;

/// <summary>
/// Interface for managing live trading state in Redis.
/// Handles fast, transient data like trailing stops, signal flags, and retry queues.
/// </summary>
public interface ILiveStateStore
{
    // Trailing Stop Management
    Task SetTrailingStopAsync(string symbol, decimal stopPrice, CancellationToken cancellationToken = default);
    Task<decimal?> GetTrailingStopAsync(string symbol, CancellationToken cancellationToken = default);
    Task RemoveTrailingStopAsync(string symbol, CancellationToken cancellationToken = default);
    Task<Dictionary<string, decimal>> GetAllTrailingStopsAsync(CancellationToken cancellationToken = default);

    // Signal Deduplication
    Task FlagSignalAsync(string symbol, DateTime date, CancellationToken cancellationToken = default);
    Task<bool> WasSignaledAsync(string symbol, DateTime date, CancellationToken cancellationToken = default);
    Task ClearSignalFlagsAsync(DateTime olderThan, CancellationToken cancellationToken = default);

    // Position State Tracking
    Task SetPositionStateAsync(string symbol, PositionState state, CancellationToken cancellationToken = default);
    Task<PositionState?> GetPositionStateAsync(string symbol, CancellationToken cancellationToken = default);
    Task RemovePositionStateAsync(string symbol, CancellationToken cancellationToken = default);

    // Error Retry Queue
    Task EnqueueRetryAsync(RetryItem item, CancellationToken cancellationToken = default);
    Task<RetryItem?> DequeueRetryAsync(CancellationToken cancellationToken = default);
    Task<int> GetRetryQueueLengthAsync(CancellationToken cancellationToken = default);

    // Market State Caching
    Task SetMarketStateAsync(string key, object value, TimeSpan expiry, CancellationToken cancellationToken = default);
    Task<T?> GetMarketStateAsync<T>(string key, CancellationToken cancellationToken = default) where T : class;
    Task RemoveMarketStateAsync(string key, CancellationToken cancellationToken = default);

    // Health and Diagnostics
    Task<bool> IsHealthyAsync(CancellationToken cancellationToken = default);
    Task<LiveStateStats> GetStatsAsync(CancellationToken cancellationToken = default);
    Task FlushAllAsync(CancellationToken cancellationToken = default);
}

/// <summary>
/// Represents the current state of a position
/// </summary>
public record PositionState(
    string Symbol,
    decimal EntryPrice,
    decimal CurrentPrice,
    decimal StopPrice,
    int Quantity,
    DateTime EntryTime,
    DateTime LastUpdated
);

/// <summary>
/// Represents an item in the retry queue
/// </summary>
public record RetryItem(
    string Id,
    string Operation,
    string Data,
    int AttemptCount,
    DateTime NextRetryTime,
    DateTime CreatedAt
);

/// <summary>
/// Statistics about the live state store
/// </summary>
public record LiveStateStats(
    int TrailingStopCount,
    int SignalFlagCount,
    int PositionStateCount,
    int RetryQueueLength,
    int MarketStateCacheCount,
    DateTime LastHealthCheck
);
