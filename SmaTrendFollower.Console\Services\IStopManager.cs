namespace SmaTrendFollower.Services;

/// <summary>
/// Manages trailing stop-loss orders for capital preservation
/// </summary>
public interface IStopManager
{
    /// <summary>
    /// Updates trailing stops for all open positions based on current market data
    /// </summary>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Task representing the async operation</returns>
    Task UpdateTrailingStopsAsync(CancellationToken cancellationToken = default);

    /// <summary>
    /// Sets initial stop-loss order for a new position
    /// </summary>
    /// <param name="symbol">Symbol to set stop for</param>
    /// <param name="entryPrice">Entry price of the position</param>
    /// <param name="atr">Current ATR value</param>
    /// <param name="quantity">Position quantity</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Task representing the async operation</returns>
    Task SetInitialStopAsync(string symbol, decimal entryPrice, decimal atr, decimal quantity, CancellationToken cancellationToken = default);

    /// <summary>
    /// Removes stop-loss orders for a symbol (when position is closed)
    /// </summary>
    /// <param name="symbol">Symbol to remove stops for</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Task representing the async operation</returns>
    Task RemoveStopsAsync(string symbol, CancellationToken cancellationToken = default);
}
