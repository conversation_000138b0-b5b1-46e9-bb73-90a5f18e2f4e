#!/usr/bin/env pwsh

<#
.SYNOPSIS
    Run SmaTrendFollower tests with different configurations
.DESCRIPTION
    This script provides different test execution modes:
    - Unit tests only (fast)
    - Integration tests (requires API keys)
    - All tests with performance monitoring
.PARAMETER Mode
    Test execution mode: Unit, Integration, All, Performance
.PARAMETER Timeout
    Test timeout in seconds (default: 300)
.PARAMETER Parallel
    Enable parallel test execution (default: true)
.EXAMPLE
    ./run-tests.ps1 -Mode Unit
    ./run-tests.ps1 -Mode Integration -Timeout 600
#>

param(
    [Parameter(Mandatory=$false)]
    [ValidateSet("Unit", "Integration", "All", "Performance")]
    [string]$Mode = "Unit",
    
    [Parameter(Mandatory=$false)]
    [int]$Timeout = 300,
    
    [Parameter(Mandatory=$false)]
    [bool]$Parallel = $true
)

# Set error action preference
$ErrorActionPreference = "Stop"

# Get script directory
$ScriptDir = Split-Path -Parent $MyInvocation.MyCommand.Path
$ProjectDir = Split-Path -Parent $ScriptDir

Write-Host "🧪 Running SmaTrendFollower Tests" -ForegroundColor Cyan
Write-Host "Mode: $Mode" -ForegroundColor Yellow
Write-Host "Timeout: $Timeout seconds" -ForegroundColor Yellow
Write-Host "Parallel: $Parallel" -ForegroundColor Yellow
Write-Host ""

# Build the project first
Write-Host "🔨 Building project..." -ForegroundColor Green
dotnet build $ProjectDir --configuration Release --no-restore
if ($LASTEXITCODE -ne 0) {
    Write-Error "Build failed"
    exit 1
}

# Prepare test arguments
$testArgs = @(
    "test"
    "$ProjectDir/SmaTrendFollower.Tests"
    "--configuration", "Release"
    "--no-build"
    "--verbosity", "normal"
    "--logger", "console;verbosity=detailed"
    "--collect:XPlat Code Coverage"
    "--results-directory", "TestResults"
    "--settings", "$ScriptDir/TestFilters.runsettings"
)

# Add timeout
$testArgs += "--blame-hang-timeout", "${Timeout}s"

# Configure test filters based on mode
switch ($Mode) {
    "Unit" {
        Write-Host "🏃‍♂️ Running Unit Tests Only" -ForegroundColor Green
        $testArgs += "--filter", "Category!=Integration"
    }
    "Integration" {
        Write-Host "🔗 Running Integration Tests" -ForegroundColor Green
        $testArgs += "--filter", "Category=Integration"
    }
    "Performance" {
        Write-Host "⚡ Running Performance Tests" -ForegroundColor Green
        $testArgs += "--filter", "Category=Performance"
    }
    "All" {
        Write-Host "🎯 Running All Tests" -ForegroundColor Green
        # No filter - run everything
    }
}

# Configure parallelization
if (-not $Parallel) {
    $testArgs += "--parallel", "false"
}

# Run tests
Write-Host "Executing: dotnet $($testArgs -join ' ')" -ForegroundColor Gray
Write-Host ""

$stopwatch = [System.Diagnostics.Stopwatch]::StartNew()

try {
    & dotnet $testArgs
    $exitCode = $LASTEXITCODE
}
catch {
    Write-Error "Test execution failed: $_"
    exit 1
}

$stopwatch.Stop()
$elapsed = $stopwatch.Elapsed

Write-Host ""
Write-Host "⏱️  Test execution completed in $($elapsed.ToString('mm\:ss\.fff'))" -ForegroundColor Cyan

if ($exitCode -eq 0) {
    Write-Host "✅ All tests passed!" -ForegroundColor Green
} else {
    Write-Host "❌ Some tests failed (exit code: $exitCode)" -ForegroundColor Red
}

exit $exitCode
