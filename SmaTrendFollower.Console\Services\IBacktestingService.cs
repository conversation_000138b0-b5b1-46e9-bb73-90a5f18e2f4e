using Alpaca.Markets;

namespace SmaTrendFollower.Services;

/// <summary>
/// Interface for backtesting trading strategies with historical data
/// </summary>
public interface IBacktestingService
{
    /// <summary>
    /// Runs a backtest for a trading strategy over a specified period
    /// </summary>
    /// <param name="configuration">Backtest configuration parameters</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Comprehensive backtest results</returns>
    Task<BacktestResult> RunBacktestAsync(BacktestConfiguration configuration, CancellationToken cancellationToken = default);

    /// <summary>
    /// Validates backtest configuration before execution
    /// </summary>
    /// <param name="configuration">Configuration to validate</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Validation result with any issues found</returns>
    Task<BacktestValidationResult> ValidateConfigurationAsync(BacktestConfiguration configuration, CancellationToken cancellationToken = default);

    /// <summary>
    /// Gets available historical data range for backtesting
    /// </summary>
    /// <param name="symbols">Symbols to check data availability for</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Data availability information</returns>
    Task<DataAvailabilityResult> GetDataAvailabilityAsync(IEnumerable<string> symbols, CancellationToken cancellationToken = default);

    /// <summary>
    /// Exports backtest results to various formats
    /// </summary>
    /// <param name="result">Backtest result to export</param>
    /// <param name="exportOptions">Export configuration</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Export operation result</returns>
    Task<BacktestExportResult> ExportResultsAsync(BacktestResult result, BacktestExportOptions exportOptions, CancellationToken cancellationToken = default);

    /// <summary>
    /// Compares multiple backtest results for strategy optimization
    /// </summary>
    /// <param name="results">Backtest results to compare</param>
    /// <param name="comparisonMetrics">Metrics to use for comparison</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Comparison analysis</returns>
    Task<BacktestComparisonResult> CompareResultsAsync(IEnumerable<BacktestResult> results, BacktestComparisonMetrics comparisonMetrics, CancellationToken cancellationToken = default);

    /// <summary>
    /// Gets current backtesting progress for long-running operations
    /// </summary>
    /// <param name="backtestId">Unique identifier for the backtest</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Current progress information</returns>
    Task<BacktestProgress> GetProgressAsync(Guid backtestId, CancellationToken cancellationToken = default);

    /// <summary>
    /// Cancels a running backtest
    /// </summary>
    /// <param name="backtestId">Unique identifier for the backtest to cancel</param>
    /// <param name="cancellationToken">Cancellation token</param>
    Task CancelBacktestAsync(Guid backtestId, CancellationToken cancellationToken = default);
}

/// <summary>
/// Configuration for running a backtest
/// </summary>
public readonly record struct BacktestConfiguration(
    DateTime StartDate,
    DateTime EndDate,
    decimal InitialCapital,
    IReadOnlyList<string> Symbols,
    BacktestStrategyType StrategyType,
    BacktestParameters Parameters,
    BacktestDataSource DataSource = BacktestDataSource.Cache,
    TimeSpan BarInterval = default,
    decimal CommissionPerTrade = 0.0m,
    decimal SlippageBps = 1.0m,
    bool UseMarketHours = true,
    BacktestRiskParameters RiskParameters = default
)
{
    public static BacktestConfiguration Default => new(
        StartDate: DateTime.UtcNow.AddYears(-1),
        EndDate: DateTime.UtcNow,
        InitialCapital: 100_000m,
        Symbols: new[] { "SPY" },
        StrategyType: BacktestStrategyType.SmaTrendFollowing,
        Parameters: BacktestParameters.Default,
        DataSource: BacktestDataSource.Cache,
        BarInterval: TimeSpan.FromDays(1),
        CommissionPerTrade: 1.0m,
        SlippageBps: 2.0m,
        UseMarketHours: true,
        RiskParameters: BacktestRiskParameters.Default
    );
}

/// <summary>
/// Strategy-specific parameters for backtesting
/// </summary>
public readonly record struct BacktestParameters(
    int SmaPeriod50 = 50,
    int SmaPeriod200 = 200,
    int AtrPeriod = 14,
    decimal AtrVolatilityThreshold = 0.03m,
    decimal RiskPercentage = 0.01m,
    decimal MaxRiskPerTrade = 1000m,
    decimal StopLossMultiplier = 2.0m,
    decimal LimitOrderOffset = 0.002m,
    bool UseVolatilityFilter = true,
    bool UseSpyFilter = true
)
{
    public static BacktestParameters Default => new();
}

/// <summary>
/// Risk management parameters for backtesting
/// </summary>
public readonly record struct BacktestRiskParameters(
    decimal MaxDrawdownPercent = 0.20m,
    decimal MaxPositionSize = 0.10m,
    decimal MaxDailyLoss = 0.02m,
    int MaxConcurrentPositions = 10,
    bool UsePositionSizing = true,
    bool UseStopLoss = true,
    bool UseTrailingStop = false
)
{
    public static BacktestRiskParameters Default => new();
}

/// <summary>
/// Comprehensive results from a backtest run
/// </summary>
public readonly record struct BacktestResult(
    Guid BacktestId,
    BacktestConfiguration Configuration,
    DateTime StartTime,
    DateTime EndTime,
    TimeSpan Duration,
    BacktestPerformanceMetrics Performance,
    IReadOnlyList<BacktestTrade> Trades,
    IReadOnlyList<BacktestEquityCurvePoint> EquityCurve,
    BacktestRiskMetrics RiskMetrics,
    BacktestStatistics Statistics,
    string[] Warnings,
    string[] Errors
);

/// <summary>
/// Performance metrics from backtest
/// </summary>
public readonly record struct BacktestPerformanceMetrics(
    decimal TotalReturn,
    decimal AnnualizedReturn,
    decimal SharpeRatio,
    decimal SortinoRatio,
    decimal MaxDrawdown,
    decimal MaxDrawdownDuration,
    decimal WinRate,
    decimal ProfitFactor,
    decimal AverageWin,
    decimal AverageLoss,
    decimal LargestWin,
    decimal LargestLoss,
    int TotalTrades,
    int WinningTrades,
    int LosingTrades
);

/// <summary>
/// Risk metrics from backtest
/// </summary>
public readonly record struct BacktestRiskMetrics(
    decimal VaR95,
    decimal VaR99,
    decimal ConditionalVaR,
    decimal Beta,
    decimal Alpha,
    decimal VolatilityAnnualized,
    decimal DownsideDeviation,
    decimal UpsideDeviation,
    decimal CalmarRatio,
    decimal SterlingRatio
);

/// <summary>
/// Individual trade from backtest
/// </summary>
public readonly record struct BacktestTrade(
    DateTime EntryTime,
    DateTime ExitTime,
    string Symbol,
    decimal Quantity,
    decimal EntryPrice,
    decimal ExitPrice,
    decimal PnL,
    decimal PnLPercent,
    TimeSpan HoldingPeriod,
    BacktestTradeReason EntryReason,
    BacktestTradeReason ExitReason,
    decimal Commission,
    decimal Slippage
);

/// <summary>
/// Point on the equity curve
/// </summary>
public readonly record struct BacktestEquityCurvePoint(
    DateTime Time,
    decimal Equity,
    decimal DrawdownPercent,
    decimal DailyReturn,
    int OpenPositions
);

/// <summary>
/// Overall backtest statistics
/// </summary>
public readonly record struct BacktestStatistics(
    int TotalBars,
    int TradingDays,
    decimal AverageDailyVolume,
    decimal AverageSpread,
    decimal DataQualityScore,
    TimeSpan AverageExecutionTime,
    int CacheHits,
    int CacheMisses
);

/// <summary>
/// Validation result for backtest configuration
/// </summary>
public readonly record struct BacktestValidationResult(
    bool IsValid,
    string[] Errors,
    string[] Warnings,
    string[] Recommendations
);

/// <summary>
/// Data availability information
/// </summary>
public readonly record struct DataAvailabilityResult(
    IReadOnlyDictionary<string, DateRange> SymbolDataRanges,
    DateRange OverallRange,
    string[] MissingSymbols,
    string[] PartialDataSymbols
);

/// <summary>
/// Date range for data availability
/// </summary>
public readonly record struct DateRange(
    DateTime StartDate,
    DateTime EndDate,
    int TotalBars,
    decimal DataQualityPercent
);

/// <summary>
/// Export options for backtest results
/// </summary>
public readonly record struct BacktestExportOptions(
    BacktestExportFormat Format,
    string OutputPath,
    bool IncludeTrades = true,
    bool IncludeEquityCurve = true,
    bool IncludeMetrics = true,
    bool IncludeCharts = false
);

/// <summary>
/// Comparison result for multiple backtests
/// </summary>
public readonly record struct BacktestComparisonResult(
    IReadOnlyList<BacktestResult> Results,
    BacktestResult BestPerforming,
    BacktestResult LowestRisk,
    BacktestResult HighestSharpe,
    BacktestComparisonMetrics Metrics
);

/// <summary>
/// Metrics for comparing backtests
/// </summary>
public readonly record struct BacktestComparisonMetrics(
    string PrimaryMetric = "SharpeRatio",
    bool IncludeRiskAdjusted = true,
    bool IncludeDrawdown = true,
    decimal MinimumTrades = 10
);

/// <summary>
/// Progress information for running backtest
/// </summary>
public readonly record struct BacktestProgress(
    Guid BacktestId,
    decimal PercentComplete,
    DateTime CurrentDate,
    int TradesExecuted,
    string CurrentStatus,
    TimeSpan EstimatedTimeRemaining
);

/// <summary>
/// Export result information
/// </summary>
public readonly record struct BacktestExportResult(
    bool Success,
    string OutputPath,
    long FileSizeBytes,
    TimeSpan ExportDuration,
    string[] GeneratedFiles,
    string[] Errors
);

/// <summary>
/// Strategy types available for backtesting
/// </summary>
public enum BacktestStrategyType
{
    SmaTrendFollowing,
    MeanReversion,
    Momentum,
    Custom
}

/// <summary>
/// Data sources for backtesting
/// </summary>
public enum BacktestDataSource
{
    Cache,
    Alpaca,
    Polygon,
    Mixed
}

/// <summary>
/// Export formats for backtest results
/// </summary>
public enum BacktestExportFormat
{
    Json,
    Csv,
    Excel,
    Html,
    Pdf
}

/// <summary>
/// Reasons for trade entry/exit
/// </summary>
public enum BacktestTradeReason
{
    Signal,
    StopLoss,
    TakeProfit,
    TimeExit,
    RiskManagement,
    EndOfPeriod
}
