# Enum Conflict Resolution Strategy

## Overview

This document outlines the comprehensive enum conflict resolution strategy implemented in the SmaTrendFollower project to prevent naming conflicts between internal enums and external SDK enums (such as Alpaca.Markets, Polygon.io, etc.).

## Problem Statement

When integrating multiple external SDKs, enum naming conflicts can occur when:
- Multiple SDKs define enums with the same name (e.g., `OrderType`, `EventType`)
- Internal project enums conflict with external SDK enums
- SEC filing types and market event filters have ambiguous naming

## Solution Architecture

### 1. **Prefixed Internal Enums**

All SmaTrendFollower-specific enums are prefixed with `Sma` to ensure uniqueness:

```csharp
// Instead of: OrderSide
public enum SmaOrderSide { Buy, Sell }

// Instead of: EventType  
public enum SmaMarketEventType { EarningsAnnouncement, FdaApproval, ... }

// Instead of: FilingType
public enum SmaSecFilingType { Form10K, Form8K, ... }
```

### 2. **Namespace Organization**

All conflict-resolution enums and models are organized in the `SmaTrendFollower.Models` namespace:

```csharp
namespace SmaTrendFollower.Models;

public enum SmaSecFilingType { ... }
public enum SmaMarketEventType { ... }
public enum SmaEventFilterType { ... }
public enum SmaEventImpactLevel { ... }
public enum SmaEventTradingAction { ... }
public enum SmaEventTimePeriod { ... }
public enum SmaOrderSide { ... }
public enum SmaOrderType { ... }
```

### 3. **Extension Methods for Conversion**

Safe conversion methods between internal and external SDK enums:

```csharp
// Convert internal enum to Alpaca SDK enum
SmaOrderSide.Buy.ToAlpacaOrderSide() // Returns OrderSide.Buy

// Convert Alpaca SDK enum to internal enum  
OrderSide.Buy.ToSmaOrderSide() // Returns SmaOrderSide.Buy
```

### 4. **Rich Enum Metadata**

All enums include descriptive attributes and conversion methods:

```csharp
public enum SmaEventImpactLevel
{
    [Description("Low Impact")]
    Low = 1,
    
    [Description("High Impact")]  
    High = 3
}

// Usage
SmaEventImpactLevel.High.GetDescription() // Returns "High Impact"
SmaEventImpactLevel.High.ToPositionSizeMultiplier() // Returns 0.5m
```

## Implementation Details

### Core Enums

#### SEC Filing Types
- `SmaSecFilingType`: 10-K, 10-Q, 8-K, Proxy, 13F, Forms 3/4/5, S-1, 424B
- Impact level mapping: 8-K and S-1 are High impact, 10-K/10-Q are Medium
- Immediate attention flags for critical filings

#### Market Event Types  
- `SmaMarketEventType`: Earnings, M&A, FDA approvals, dividends, splits, etc.
- Volatility impact mapping: Earnings and FDA approvals cause high volatility
- Impact level classification from Low to Critical

#### Event Filtering
- `SmaEventFilterType`: Include/exclude patterns for different event types
- `SmaEventTradingAction`: Recommended actions based on events
- `SmaEventTimePeriod`: Time ranges for event analysis

#### Trading Enums
- `SmaOrderSide`: Buy/Sell with conversion to Alpaca enums
- `SmaOrderType`: Market/Limit/Stop/StopLimit/TrailingStop

### Data Models

#### MarketEvent
```csharp
public class MarketEvent
{
    public string Symbol { get; set; }
    public SmaMarketEventType EventType { get; set; }
    public DateTime EventDate { get; set; }
    public SmaEventImpactLevel ImpactLevel { get; set; }
    public string Description { get; set; }
    public Dictionary<string, object> Metadata { get; set; }
}
```

#### SecFiling
```csharp
public class SecFiling  
{
    public string Symbol { get; set; }
    public SmaSecFilingType FilingType { get; set; }
    public DateTime FilingDate { get; set; }
    public string Url { get; set; }
}
```

#### EventTradingRecommendation
```csharp
public class EventTradingRecommendation
{
    public string Symbol { get; set; }
    public SmaEventTradingAction Action { get; set; }
    public decimal Confidence { get; set; }
    public decimal RecommendedPositionSizeMultiplier { get; set; }
    public SmaEventFilterType FilterType { get; set; }
}
```

### Extension Methods

#### Conversion Extensions
- `ToAlpacaOrderSide()` / `ToSmaOrderSide()`
- `ToAlpacaOrderType()` / `ToSmaOrderType()`
- `ToDisplayString()` for OrderStatus

#### Business Logic Extensions
- `ToPositionSizeMultiplier()` - Convert impact level to position sizing
- `AllowsNewPositions()` - Check if trading action permits new trades
- `ToStopLossMultiplier()` - Adjust stop-loss based on events
- `CausesHighVolatility()` - Identify high-volatility events
- `RequiresImmediateAttention()` - Flag critical SEC filings

#### Utility Extensions
- `GetDescription()` - Get enum description attribute
- `ParseFromDescription<T>()` - Parse enum from description
- `GetEnumDescriptions<T>()` - Get all enum values with descriptions
- `IsValidEnumValue<T>()` - Validate enum value
- `GetSafeEnumValue<T>()` - Get safe enum with fallback

## Usage Examples

### Event-Based Trading Decisions

```csharp
var eventService = serviceProvider.GetService<ISecEventFilterService>();

// Check for high-impact events
var hasHighImpact = await eventService.HasHighImpactEventsAsync("AAPL", SmaEventTimePeriod.Next3Days);

// Get trading recommendation
var recommendation = await eventService.GetTradingRecommendationAsync("AAPL", SmaEventFilterType.ExcludeEarnings);

if (!recommendation.Action.AllowsNewPositions())
{
    // Skip trading this symbol
    return;
}

// Adjust position size based on events
var baseQuantity = 100m;
var adjustedQuantity = baseQuantity * recommendation.RecommendedPositionSizeMultiplier;
```

### Safe Enum Conversion

```csharp
// Convert internal order to Alpaca order
var smaOrderSide = SmaOrderSide.Buy;
var alpacaOrderSide = smaOrderSide.ToAlpacaOrderSide();

var buyOrder = new NewOrderRequest(symbol, quantity, alpacaOrderSide, OrderType.Limit, TimeInForce.Day);
```

### Event Impact Analysis

```csharp
// Analyze SEC filing impact
var filing = new SecFiling { FilingType = SmaSecFilingType.Form8K };
var impactLevel = filing.FilingType.ToImpactLevel(); // Returns SmaEventImpactLevel.High
var needsAttention = filing.FilingType.RequiresImmediateAttention(); // Returns true

// Adjust position sizing
var positionMultiplier = impactLevel.ToPositionSizeMultiplier(); // Returns 0.5m for High impact
```

## Best Practices

### 1. **Always Use Prefixed Enums**
- Use `SmaOrderSide` instead of `OrderSide` in internal code
- Convert to external SDK enums only at API boundaries

### 2. **Leverage Extension Methods**
- Use business logic extensions for consistent behavior
- Prefer `ToPositionSizeMultiplier()` over manual calculations

### 3. **Validate Enum Values**
- Use `IsValidEnumValue()` when parsing external data
- Use `GetSafeEnumValue()` with appropriate defaults

### 4. **Document Enum Meanings**
- Always include `[Description]` attributes
- Use `GetDescription()` for user-facing displays

### 5. **Test Enum Conversions**
- Verify bidirectional conversion accuracy
- Test edge cases and invalid values

## Testing Strategy

The enum conflict resolution system includes comprehensive tests:

- **Conversion Tests**: Verify accurate bidirectional enum conversion
- **Business Logic Tests**: Validate extension method behavior
- **Data Model Tests**: Ensure proper initialization and usage
- **Service Integration Tests**: Test event filtering service functionality

All tests are located in `SmaTrendFollower.Tests/Models/EnumConflictResolutionTests.cs`.

## Future Considerations

### Extensibility
- New event types can be added to `SmaMarketEventType`
- Additional SEC filing types can be added to `SmaSecFilingType`
- New trading actions can be added to `SmaEventTradingAction`

### Performance
- Enum conversions are compile-time safe and runtime efficient
- Extension methods use switch expressions for optimal performance
- Caching is implemented in the event filtering service

### Maintenance
- Regular review of external SDK enum changes
- Update conversion methods when SDKs add new enum values
- Monitor for new potential conflicts as dependencies are added

This enum conflict resolution strategy ensures type safety, prevents naming conflicts, and provides a robust foundation for integrating multiple financial data SDKs while maintaining clean, maintainable code.
