#!/usr/bin/env pwsh

# Fix invalid backtick characters in test files

Write-Host "🔧 Fixing invalid backtick characters in test files..." -ForegroundColor Cyan

$testFile = "SmaTrendFollower.Tests\Services\StreamingDataServiceTests.cs"

if (Test-Path $testFile) {
    Write-Host "Processing: $testFile" -ForegroundColor Yellow

    $content = Get-Content $testFile -Raw
    $originalContent = $content

    # Replace all backtick-n sequences with proper newlines
    $content = $content -replace '`n', "`r`n"

    if ($content -ne $originalContent) {
        Set-Content -Path $testFile -Value $content -NoNewline
        Write-Host "✅ Fixed: $testFile" -ForegroundColor Green
    } else {
        Write-Host "ℹ️ No changes needed: $testFile" -ForegroundColor Gray
    }
} else {
    Write-Host "❌ File not found: $testFile" -ForegroundColor Red
}

Write-Host "🎉 Backtick fix complete!" -ForegroundColor Green
