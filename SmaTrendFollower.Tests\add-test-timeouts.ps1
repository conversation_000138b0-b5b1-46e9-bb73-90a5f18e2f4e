#!/usr/bin/env pwsh

<#
.SYNOPSIS
    Adds TestTimeout attributes to test methods that are missing them
.DESCRIPTION
    This script scans all test files and adds appropriate [TestTimeout] attributes
    based on test categories and patterns.
.EXAMPLE
    ./add-test-timeouts.ps1
#>

param(
    [switch]$DryRun = $false,
    [switch]$Verbose = $false
)

# Define timeout mappings based on test patterns
$TimeoutMappings = @{
    # Integration tests
    'Integration' = 'TestTimeouts.Integration'
    'Streaming' = 'TestTimeouts.Streaming'
    'Network' = 'TestTimeouts.Network'
    'Database' = 'TestTimeouts.Database'
    'Performance' = 'TestTimeouts.Performance'
    
    # Service-specific patterns
    'StreamingDataService' = 'TestTimeouts.Streaming'
    'MarketDataService' = 'TestTimeouts.Network'
    'PolygonApi' = 'TestTimeouts.Network'
    'AlpacaClient' = 'TestTimeouts.Network'
    'StockBarCache' = 'TestTimeouts.Database'
    'RedisWarming' = 'TestTimeouts.Database'
    'MarketRegime' = 'TestTimeouts.Unit'
    'TradingMetrics' = 'TestTimeouts.Performance'
    
    # Default for unit tests
    'Default' = 'TestTimeouts.Unit'
}

function Get-TimeoutForTest {
    param(
        [string]$FileName,
        [string]$TestContent
    )
    
    # Check for explicit category traits
    if ($TestContent -match '\[Trait\("Category",\s*"Integration"\)\]') {
        return $TimeoutMappings['Integration']
    }
    
    if ($TestContent -match '\[Trait\("Category",\s*"Performance"\)\]') {
        return $TimeoutMappings['Performance']
    }
    
    # Check filename patterns
    foreach ($pattern in $TimeoutMappings.Keys) {
        if ($pattern -ne 'Default' -and $FileName -like "*$pattern*") {
            return $TimeoutMappings[$pattern]
        }
    }
    
    # Check for specific test patterns
    if ($TestContent -match 'async.*Task.*\b(Connect|Stream|Subscribe|Network|Api|Http)\b') {
        return $TimeoutMappings['Network']
    }
    
    if ($TestContent -match 'async.*Task.*\b(Cache|Database|Sql|Redis)\b') {
        return $TimeoutMappings['Database']
    }
    
    if ($TestContent -match 'async.*Task.*\b(Performance|Benchmark|Load|Stress)\b') {
        return $TimeoutMappings['Performance']
    }
    
    return $TimeoutMappings['Default']
}

function Add-TimeoutToTest {
    param(
        [string]$FilePath,
        [string]$Content
    )
    
    $fileName = Split-Path $FilePath -Leaf
    $modified = $false
    $lines = $Content -split "`n"
    $newLines = @()
    
    for ($i = 0; $i -lt $lines.Count; $i++) {
        $line = $lines[$i]
        $newLines += $line
        
        # Look for [Fact] or [Theory] attributes that don't already have [TestTimeout]
        if ($line -match '^\s*\[(Fact|Theory)\]' -and $i -gt 0) {
            # Check if TestTimeout already exists in the previous few lines
            $hasTimeout = $false
            for ($j = [Math]::Max(0, $i - 3); $j -lt $i; $j++) {
                if ($lines[$j] -match '\[TestTimeout\(') {
                    $hasTimeout = $true
                    break
                }
            }
            
            if (-not $hasTimeout) {
                # Get the test method content for analysis
                $testContent = ""
                for ($k = $i; $k -lt [Math]::Min($lines.Count, $i + 10); $k++) {
                    $testContent += $lines[$k] + "`n"
                }
                
                $timeout = Get-TimeoutForTest -FileName $fileName -TestContent $testContent
                $indent = if ($line -match '^(\s*)') { $matches[1] } else { "    " }
                $timeoutLine = "$indent[TestTimeout($timeout)]"
                
                # Insert timeout attribute before the [Fact] or [Theory]
                $newLines[$newLines.Count - 1] = $timeoutLine
                $newLines += $line
                $modified = $true
                
                if ($Verbose) {
                    Write-Host "  Added $timeout to test at line $($i + 1)" -ForegroundColor Green
                }
            }
        }
    }
    
    if ($modified) {
        return ($newLines -join "`n")
    }
    
    return $null
}

# Get all test files
$testFiles = Get-ChildItem -Path "." -Recurse -Filter "*Tests.cs" | Where-Object { 
    $_.FullName -notlike "*\bin\*" -and $_.FullName -notlike "*\obj\*" 
}

Write-Host "Found $($testFiles.Count) test files to process" -ForegroundColor Cyan

$processedCount = 0
$modifiedCount = 0

foreach ($file in $testFiles) {
    $processedCount++
    Write-Host "[$processedCount/$($testFiles.Count)] Processing: $($file.Name)" -ForegroundColor Yellow
    
    try {
        $content = Get-Content -Path $file.FullName -Raw -Encoding UTF8
        
        # Skip if file already has TestTimeout imports
        if ($content -match 'using.*TestTimeout' -or $content -match '\[TestTimeout\(') {
            if ($Verbose) {
                Write-Host "  Skipping - already has timeout attributes" -ForegroundColor Gray
            }
            continue
        }
        
        $newContent = Add-TimeoutToTest -FilePath $file.FullName -Content $content
        
        if ($newContent) {
            if (-not $DryRun) {
                Set-Content -Path $file.FullName -Value $newContent -Encoding UTF8 -NoNewline
                Write-Host "  Modified: $($file.Name)" -ForegroundColor Green
            } else {
                Write-Host "  Would modify: $($file.Name)" -ForegroundColor Magenta
            }
            $modifiedCount++
        } else {
            if ($Verbose) {
                Write-Host "  No changes needed" -ForegroundColor Gray
            }
        }
    }
    catch {
        Write-Host "  Error processing $($file.Name): $($_.Exception.Message)" -ForegroundColor Red
    }
}

Write-Host "`nSummary:" -ForegroundColor Cyan
Write-Host "  Processed: $processedCount files" -ForegroundColor White
Write-Host "  Modified: $modifiedCount files" -ForegroundColor Green

if ($DryRun) {
    Write-Host "  (Dry run - no files were actually modified)" -ForegroundColor Yellow
}
