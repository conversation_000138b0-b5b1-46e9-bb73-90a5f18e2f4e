using Alpaca.Markets;
using Microsoft.Extensions.Logging;
using Polly;

namespace SmaTrendFollower.Services;

/// <summary>
/// Interface for Alpaca rate limiting helper
/// Extends IRetryService for backward compatibility
/// </summary>
public interface IAlpacaRateLimitHelper : IRetryService
{
    /// <summary>
    /// Executes an Alpaca API call with rate limiting and retry logic
    /// </summary>
    Task<T> ExecuteAsync<T>(Func<Task<T>> apiCall, string operationName = "Unknown");
}

/// <summary>
/// Rate limiting helper for Alpaca API calls with exponential back-off
/// Handles 200 requests/minute limit with intelligent retry strategies
/// Now implements IRetryService for unified retry interface
/// </summary>
public sealed class AlpacaRateLimitHelper : IAlpacaRateLimitHelper
{
    private readonly ILogger<AlpacaRateLimitHelper> _logger;
    private readonly IAsyncPolicy _retryPolicy;
    private readonly SemaphoreSlim _semaphore;
    private readonly Timer _resetTimer;
    private readonly IAlpacaRetryService _retryService;
    private int _requestCount;
    private DateTime _windowStart;

    public AlpacaRateLimitHelper(ILogger<AlpacaRateLimitHelper> logger, IAlpacaRetryService? retryService = null)
    {
        _logger = logger;
        _semaphore = new SemaphoreSlim(1, 1);
        _requestCount = 0;
        _windowStart = DateTime.UtcNow;

        // Use injected retry service or create a new one
        _retryService = retryService ?? CreateDefaultAlpacaRetryService(logger);

        // Reset counter every minute
        _resetTimer = new Timer(ResetCounter, null, TimeSpan.FromMinutes(1), TimeSpan.FromMinutes(1));

        // Exponential back-off retry policy (kept for backward compatibility)
        _retryPolicy = Policy
            .Handle<Exception>(ex =>
                ex.Message.Contains("TooManyRequests") ||
                ex.Message.Contains("429") ||
                ex.Message.Contains("rate limit", StringComparison.OrdinalIgnoreCase))
            .WaitAndRetryAsync(
                retryCount: 5,
                sleepDurationProvider: retryAttempt => TimeSpan.FromSeconds(Math.Pow(2, retryAttempt)) + TimeSpan.FromMilliseconds(Random.Shared.Next(0, 1000)),
                onRetry: (exception, timespan, retryCount, context) =>
                {
                    _logger.LogWarning("Alpaca rate limit retry {RetryCount}/5 after {Delay}ms. Exception: {Exception}",
                        retryCount, timespan.TotalMilliseconds, exception.Message);
                });
    }

    /// <summary>
    /// Executes an Alpaca API call with rate limiting and retry logic
    /// </summary>
    public async Task<T> ExecuteAsync<T>(Func<Task<T>> apiCall, string operationName = "Unknown")
    {
        await _semaphore.WaitAsync();
        try
        {
            // Check if we need to wait due to rate limiting
            await WaitIfNecessary();

            // Increment request counter
            Interlocked.Increment(ref _requestCount);

            _logger.LogDebug("Executing Alpaca API call: {Operation} (Request {Count}/200 in current window)",
                operationName, _requestCount);

            // Execute with retry policy
            return await _retryPolicy.ExecuteAsync(async () =>
            {
                try
                {
                    return await apiCall();
                }
                catch (Exception ex) when (IsRateLimitException(ex))
                {
                    _logger.LogWarning("Alpaca rate limit hit for {Operation}, will retry with exponential back-off", operationName);
                    throw;
                }
            });
        }
        finally
        {
            _semaphore.Release();
        }
    }

    /// <summary>
    /// Executes an Alpaca API call without return value with rate limiting and retry logic
    /// </summary>
    public async Task ExecuteAsync(Func<Task> apiCall, string operationName = "Unknown")
    {
        await ExecuteAsync(async () =>
        {
            await apiCall();
            return true; // Dummy return value
        }, operationName);
    }

    private async Task WaitIfNecessary()
    {
        // If we're approaching the limit (190/200), add a small delay
        if (_requestCount >= 190)
        {
            var delay = TimeSpan.FromMilliseconds(300 + Random.Shared.Next(0, 200));
            _logger.LogDebug("Approaching Alpaca rate limit ({Count}/200), adding {Delay}ms delay", _requestCount, delay.TotalMilliseconds);
            await Task.Delay(delay);
        }
    }

    private void ResetCounter(object? state)
    {
        var oldCount = Interlocked.Exchange(ref _requestCount, 0);
        _windowStart = DateTime.UtcNow;
        
        if (oldCount > 0)
        {
            _logger.LogDebug("Reset Alpaca rate limit counter. Previous window: {Count}/200 requests", oldCount);
        }
    }

    private static bool IsRateLimitException(Exception ex)
    {
        return ex.Message.Contains("TooManyRequests") ||
               ex.Message.Contains("429") ||
               ex.Message.Contains("rate limit", StringComparison.OrdinalIgnoreCase) ||
               ex.Message.Contains("throttle", StringComparison.OrdinalIgnoreCase);
    }

    // IRetryService implementation - delegate to the retry service
    public async Task<T> ExecuteAsync<T>(Func<Task<T>> operation, string operationName = "Unknown", CancellationToken cancellationToken = default)
    {
        return await _retryService.ExecuteAsync(operation, operationName, cancellationToken);
    }

    public async Task ExecuteAsync(Func<Task> operation, string operationName = "Unknown", CancellationToken cancellationToken = default)
    {
        await _retryService.ExecuteAsync(operation, operationName, cancellationToken);
    }

    public async Task<HttpResponseMessage> ExecuteHttpAsync(Func<Task<HttpResponseMessage>> httpOperation, string operationName = "Unknown", CancellationToken cancellationToken = default)
    {
        return await _retryService.ExecuteHttpAsync(httpOperation, operationName, cancellationToken);
    }

    public RetryConfiguration GetConfiguration() => _retryService.GetConfiguration();

    public void UpdateConfiguration(RetryConfiguration configuration) => _retryService.UpdateConfiguration(configuration);

    public RateLimitStatistics GetStatistics() => _retryService.GetStatistics();

    public void ResetStatistics() => _retryService.ResetStatistics();

    private static IAlpacaRetryService CreateDefaultAlpacaRetryService(ILogger logger)
    {
        // Create a logger adapter for the retry service
        var loggerFactory = LoggerFactory.Create(builder => builder.AddConsole());
        var retryLogger = loggerFactory.CreateLogger<AlpacaRetryService>();
        return new AlpacaRetryService(retryLogger);
    }

    public void Dispose()
    {
        _resetTimer?.Dispose();
        _semaphore?.Dispose();
        if (_retryService is IDisposable disposableRetryService)
        {
            disposableRetryService.Dispose();
        }
    }
}
