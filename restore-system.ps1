# SmaTrendFollower System Recovery Script
# Comprehensive recovery solution for disaster recovery scenarios

param(
    [Parameter(Mandatory=$true)]
    [string]$BackupPath,
    [switch]$ConfigOnly,
    [switch]$DatabaseOnly,
    [switch]$StateOnly,
    [switch]$DryRun,
    [switch]$Force,
    [switch]$Verbose
)

# Color output functions
function Write-Info { param($Message) Write-Host $Message -ForegroundColor Cyan }
function Write-Success { param($Message) Write-Host $Message -ForegroundColor Green }
function Write-Warning { param($Message) Write-Host $Message -ForegroundColor Yellow }
function Write-Error { param($Message) Write-Host $Message -ForegroundColor Red }

$startTime = Get-Date

Write-Success "🔄 Starting SmaTrendFollower System Recovery"
Write-Info "Recovery source: $BackupPath"

if ($DryRun) {
    Write-Warning "🧪 DRY RUN MODE - No actual changes will be made"
}

try {
    # 1. Validate Backup
    Write-Info "🔍 Validating backup..."
    
    # Check if backup path exists
    if (-not (Test-Path $BackupPath)) {
        throw "Backup path not found: $BackupPath"
    }
    
    # Handle compressed backups
    $isCompressed = $BackupPath.EndsWith('.zip')
    $workingPath = $BackupPath
    
    if ($isCompressed) {
        Write-Info "📦 Extracting compressed backup..."
        $extractPath = "$env:TEMP\SmaTrendFollower_Recovery_$(Get-Date -Format 'yyyyMMdd_HHmmss')"
        
        if (-not $DryRun) {
            Expand-Archive -Path $BackupPath -DestinationPath $extractPath -Force
            $workingPath = $extractPath
        }
        Write-Success "✅ Backup extracted to temporary location"
    }
    
    # Verify backup manifest
    $manifestPath = "$workingPath\backup_manifest.json"
    if (Test-Path $manifestPath) {
        $manifest = Get-Content $manifestPath | ConvertFrom-Json
        Write-Success "✅ Backup manifest found"
        Write-Info "  📅 Backup Date: $($manifest.BackupInfo.BackupDate)"
        Write-Info "  📊 Total Files: $($manifest.BackupInfo.TotalFiles)"
        Write-Info "  💾 Total Size: $($manifest.BackupInfo.TotalSizeMB) MB"
        Write-Info "  🖥️ Source Machine: $($manifest.SystemInfo.MachineName)"
    } else {
        Write-Warning "⚠️ Backup manifest not found - proceeding with basic recovery"
    }

    # 2. Pre-Recovery Safety Checks
    Write-Info "🛡️ Performing safety checks..."
    
    if (-not $Force) {
        # Check if system is currently running
        $runningProcesses = Get-Process | Where-Object { $_.ProcessName -like "*SmaTrendFollower*" }
        if ($runningProcesses) {
            Write-Warning "⚠️ SmaTrendFollower processes are currently running:"
            $runningProcesses | ForEach-Object { Write-Warning "  - PID $($_.Id): $($_.ProcessName)" }
            
            if (-not $DryRun) {
                $response = Read-Host "Stop these processes and continue? (y/N)"
                if ($response -ne 'y' -and $response -ne 'Y') {
                    throw "Recovery cancelled by user"
                }
                
                Write-Info "🛑 Stopping SmaTrendFollower processes..."
                $runningProcesses | Stop-Process -Force
                Start-Sleep -Seconds 3
                Write-Success "✅ Processes stopped"
            }
        }
        
        # Backup current state before recovery
        if (-not $ConfigOnly -and -not $DatabaseOnly -and -not $StateOnly) {
            Write-Info "💾 Creating pre-recovery backup..."
            $preRecoveryBackup = ".\backups\pre_recovery_$(Get-Date -Format 'yyyyMMdd_HHmmss')"
            
            if (-not $DryRun) {
                & .\backup-system.ps1 -BackupPath $preRecoveryBackup -Verbose:$false
                Write-Success "✅ Pre-recovery backup created: $preRecoveryBackup"
            }
        }
    }

    # 3. Configuration Recovery
    if (-not $DatabaseOnly -and -not $StateOnly) {
        Write-Info "⚙️ Recovering configuration files..."
        
        $configFiles = @(
            @{Source = "$workingPath\config\.env.backup"; Dest = ".env"},
            @{Source = "$workingPath\config\.env.live.backup"; Dest = ".env.live"},
            @{Source = "$workingPath\config\appsettings.backup.json"; Dest = "appsettings.json"},
            @{Source = "$workingPath\config\appsettings.Development.backup.json"; Dest = "appsettings.Development.json"},
            @{Source = "$workingPath\config\console.appsettings.backup.json"; Dest = "SmaTrendFollower.Console\appsettings.json"}
        )
        
        $configCount = 0
        foreach ($file in $configFiles) {
            if (Test-Path $file.Source) {
                if (-not $DryRun) {
                    # Backup existing file
                    if (Test-Path $file.Dest) {
                        $backupName = "$($file.Dest).pre_recovery.$(Get-Date -Format 'yyyyMMdd_HHmmss')"
                        Copy-Item $file.Dest $backupName
                    }
                    
                    Copy-Item $file.Source $file.Dest -Force
                }
                $configCount++
                if ($Verbose) { Write-Info "  ✓ $($file.Dest)" }
            }
        }
        Write-Success "✅ Configuration recovery complete ($configCount files)"
        
        # Validate configuration
        if (-not $DryRun) {
            Write-Info "🔍 Validating recovered configuration..."
            try {
                $validateResult = dotnet run --project SmaTrendFollower.Console -- validate 2>&1
                if ($LASTEXITCODE -eq 0) {
                    Write-Success "✅ Configuration validation passed"
                } else {
                    Write-Warning "⚠️ Configuration validation failed - manual review required"
                    if ($Verbose) { Write-Warning $validateResult }
                }
            } catch {
                Write-Warning "⚠️ Could not validate configuration: $($_.Exception.Message)"
            }
        }
    }

    # 4. Database Recovery
    if (-not $ConfigOnly -and -not $StateOnly) {
        Write-Info "🗄️ Recovering databases..."
        
        $dbFiles = @(
            @{Source = "$workingPath\database\index_cache.backup.db"; Dest = "index_cache.db"},
            @{Source = "$workingPath\database\stock_cache.backup.db"; Dest = "stock_cache.db"}
        )
        
        $dbCount = 0
        foreach ($db in $dbFiles) {
            if (Test-Path $db.Source) {
                if (-not $DryRun) {
                    # Backup existing database
                    if (Test-Path $db.Dest) {
                        $backupName = "$($db.Dest).pre_recovery.$(Get-Date -Format 'yyyyMMdd_HHmmss')"
                        Copy-Item $db.Dest $backupName
                    }
                    
                    Copy-Item $db.Source $db.Dest -Force
                }
                $dbCount++
                if ($Verbose) { Write-Info "  ✓ $($db.Dest)" }
            }
        }
        Write-Success "✅ Database recovery complete ($dbCount files)"
        
        # Validate database integrity
        if (-not $DryRun) {
            Write-Info "🔍 Validating database integrity..."
            foreach ($db in $dbFiles) {
                if (Test-Path $db.Dest) {
                    try {
                        # Basic SQLite integrity check
                        $integrityCheck = sqlite3 $db.Dest "PRAGMA integrity_check;" 2>$null
                        if ($integrityCheck -eq "ok") {
                            Write-Success "  ✅ $($db.Dest) integrity OK"
                        } else {
                            Write-Warning "  ⚠️ $($db.Dest) integrity check failed"
                        }
                    } catch {
                        Write-Warning "  ⚠️ Could not check $($db.Dest) integrity"
                    }
                }
            }
        }
    }

    # 5. State Recovery
    if (-not $ConfigOnly -and -not $DatabaseOnly) {
        Write-Info "📊 Recovering trading state..."
        
        # Recover Redis state
        if (Test-Path "$workingPath\state\redis.backup.rdb") {
            Write-Info "🔄 Recovering Redis state..."
            if (-not $DryRun) {
                try {
                    # Test Redis connectivity
                    $redisTest = redis-cli ping 2>$null
                    if ($redisTest -eq "PONG") {
                        # Stop Redis, restore backup, restart Redis
                        Write-Info "  🛑 Stopping Redis..."
                        redis-cli shutdown 2>$null
                        Start-Sleep -Seconds 2
                        
                        # Copy backup file to Redis data directory
                        # Note: This path may need adjustment based on Redis installation
                        $redisDataDir = "C:\Program Files\Redis"
                        if (Test-Path $redisDataDir) {
                            Copy-Item "$workingPath\state\redis.backup.rdb" "$redisDataDir\dump.rdb" -Force
                        }
                        
                        # Restart Redis
                        Write-Info "  🚀 Starting Redis..."
                        Start-Process "redis-server" -WindowStyle Hidden
                        Start-Sleep -Seconds 3
                        
                        Write-Success "  ✅ Redis state recovered"
                    } else {
                        Write-Warning "  ⚠️ Redis not responding - manual Redis recovery required"
                    }
                } catch {
                    Write-Warning "  ⚠️ Redis recovery failed: $($_.Exception.Message)"
                }
            }
        }
        
        # Recover trading state
        if (Test-Path "$workingPath\state\trading_state.backup.json") {
            Write-Info "💹 Recovering trading state..."
            if (-not $DryRun) {
                try {
                    # Ensure Data directory exists
                    if (-not (Test-Path "Data")) {
                        New-Item -ItemType Directory -Path "Data" -Force | Out-Null
                    }
                    
                    Copy-Item "$workingPath\state\trading_state.backup.json" "Data\state_backup.json" -Force
                    Write-Success "  ✅ Trading state file recovered"
                } catch {
                    Write-Warning "  ⚠️ Trading state recovery failed: $($_.Exception.Message)"
                }
            }
        }
        
        # Recover general state backup
        if (Test-Path "$workingPath\state\state_backup.json") {
            Write-Info "📋 Recovering general state backup..."
            if (-not $DryRun) {
                try {
                    if (-not (Test-Path "Data")) {
                        New-Item -ItemType Directory -Path "Data" -Force | Out-Null
                    }
                    
                    Copy-Item "$workingPath\state\state_backup.json" "Data\state_backup.json" -Force
                    Write-Success "  ✅ General state backup recovered"
                } catch {
                    Write-Warning "  ⚠️ General state recovery failed: $($_.Exception.Message)"
                }
            }
        }
        
        Write-Success "✅ State recovery complete"
    }

    # 6. Cache Recovery
    if (-not $ConfigOnly -and -not $DatabaseOnly -and -not $StateOnly) {
        Write-Info "🗂️ Recovering cache data..."
        
        if (Test-Path "$workingPath\cache") {
            $cacheFiles = Get-ChildItem "$workingPath\cache" -File
            
            if (-not $DryRun) {
                if (-not (Test-Path "cache")) {
                    New-Item -ItemType Directory -Path "cache" -Force | Out-Null
                }
                
                foreach ($file in $cacheFiles) {
                    Copy-Item $file.FullName "cache\$($file.Name)" -Force
                }
            }
            
            Write-Success "✅ Cache recovery complete ($($cacheFiles.Count) files)"
        } else {
            Write-Info "ℹ️ No cache data found in backup"
        }
    }

    # 7. Post-Recovery Validation
    if (-not $DryRun) {
        Write-Info "🔍 Performing post-recovery validation..."
        
        # Test system startup
        Write-Info "🧪 Testing system startup..."
        try {
            $testResult = dotnet run --project SmaTrendFollower.Console -- health 2>&1
            if ($LASTEXITCODE -eq 0) {
                Write-Success "✅ System health check passed"
            } else {
                Write-Warning "⚠️ System health check failed - manual review required"
                if ($Verbose) { Write-Warning $testResult }
            }
        } catch {
            Write-Warning "⚠️ Could not perform health check: $($_.Exception.Message)"
        }
        
        # Test configuration
        Write-Info "🧪 Testing configuration..."
        try {
            $configTest = dotnet run --project SmaTrendFollower.Console -- validate 2>&1
            if ($LASTEXITCODE -eq 0) {
                Write-Success "✅ Configuration validation passed"
            } else {
                Write-Warning "⚠️ Configuration validation failed"
                if ($Verbose) { Write-Warning $configTest }
            }
        } catch {
            Write-Warning "⚠️ Could not validate configuration: $($_.Exception.Message)"
        }
    }

    # 8. Cleanup
    if ($isCompressed -and (Test-Path $extractPath)) {
        Write-Info "🧹 Cleaning up temporary files..."
        if (-not $DryRun) {
            Remove-Item $extractPath -Recurse -Force
        }
        Write-Success "✅ Cleanup complete"
    }

    # Final Summary
    Write-Success "`n🎉 RECOVERY COMPLETED SUCCESSFULLY!"
    Write-Info "📍 Recovery Source: $BackupPath"
    Write-Info "⏱️ Recovery Duration: $((Get-Date) - $startTime)"
    
    if ($DryRun) {
        Write-Warning "🧪 This was a DRY RUN - no actual changes were made"
        Write-Info "Run without -DryRun to perform actual recovery"
    } else {
        Write-Info "`n📋 Next Steps:"
        Write-Info "1. Review system logs for any errors"
        Write-Info "2. Test system functionality with dry run mode"
        Write-Info "3. Verify trading state and positions"
        Write-Info "4. Resume normal operations when satisfied"
        
        Write-Warning "`n⚠️ IMPORTANT: Test thoroughly before resuming live trading!"
    }

} catch {
    Write-Error "❌ RECOVERY FAILED: $($_.Exception.Message)"
    Write-Error "Stack Trace: $($_.Exception.StackTrace)"
    
    if ($isCompressed -and (Test-Path $extractPath)) {
        Remove-Item $extractPath -Recurse -Force -ErrorAction SilentlyContinue
    }
    
    exit 1
} finally {
    $endTime = Get-Date
    $duration = $endTime - $startTime
    Write-Info "⏱️ Total execution time: $($duration.ToString('mm\:ss'))"
}
