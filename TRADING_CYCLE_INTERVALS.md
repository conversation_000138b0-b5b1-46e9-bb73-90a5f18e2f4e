# 🕒 **TRADING CYCLE INTERVALS CONFIGURATION**

## **Overview**

The SmaTrendFollower now supports **dynamic, configurable trading cycle intervals** that automatically adjust based on market conditions, replacing the previous hardcoded 30-minute interval.

## **🚀 Key Features**

- **Environment Variable Configuration**: Set intervals via `.env` files
- **Command Line Overrides**: Use `--cycle-interval` for quick adjustments
- **VIX-Based Dynamic Adjustment**: Faster cycles during high volatility
- **Market Session Awareness**: Different intervals for regular/extended/overnight hours
- **Safety Limits**: Configurable min/max bounds to prevent extreme values

---

## **⚙️ Configuration Options**

### **Environment Variables**

Add these to your `.env` file:

```bash
# ----- Trading Cycle Intervals -----
# Default trading cycle interval in minutes (replaces hardcoded 30 minutes)
TRADING_CYCLE_INTERVAL_MINUTES=5

# High volatility cycle interval (when VIX > threshold)
TRADING_CYCLE_HIGH_VIX_MINUTES=2

# Normal volatility cycle interval
TRADING_CYCLE_NORMAL_VIX_MINUTES=5

# Low volatility cycle interval (when VIX < threshold)
TRADING_CYCLE_LOW_VIX_MINUTES=10

# Extended hours cycle interval (pre/post market)
TRADING_CYCLE_EXTENDED_HOURS_MINUTES=15

# Overnight cycle interval (market closed)
TRADING_CYCLE_OVERNIGHT_MINUTES=30

# VIX threshold for high volatility (above this = high vol interval)
TRADING_CYCLE_HIGH_VIX_THRESHOLD=25.0

# VIX threshold for low volatility (below this = low vol interval)
TRADING_CYCLE_LOW_VIX_THRESHOLD=15.0

# Enable dynamic VIX-based interval adjustment
TRADING_CYCLE_ENABLE_VIX_ADJUSTMENT=true

# Enable different intervals for extended hours
TRADING_CYCLE_ENABLE_EXTENDED_HOURS_ADJUSTMENT=true

# Minimum allowed cycle interval (safety limit)
TRADING_CYCLE_MIN_INTERVAL_MINUTES=1

# Maximum allowed cycle interval (safety limit)
TRADING_CYCLE_MAX_INTERVAL_MINUTES=60
```

### **Command Line Arguments**

Override any configuration with command line arguments:

```bash
# Set specific cycle interval (overrides all other settings)
dotnet run --cycle-interval 3

# Combine with other options
dotnet run --cycle-interval 2 --dry-run

# Show current configuration
dotnet run --show-safety
```

---

## **📊 Dynamic Interval Logic**

The system automatically selects intervals based on this priority order:

1. **Command Line Override** (highest priority)
2. **Market Closed** → Use `OVERNIGHT_MINUTES`
3. **Extended Hours** → Use `EXTENDED_HOURS_MINUTES`
4. **VIX-Based Selection** (if enabled):
   - VIX ≥ High Threshold → `HIGH_VIX_MINUTES`
   - VIX ≤ Low Threshold → `LOW_VIX_MINUTES`
   - Otherwise → `NORMAL_VIX_MINUTES`
5. **Default** → `INTERVAL_MINUTES`

### **Example Scenarios**

| Market Condition | VIX Level | Selected Interval | Reason |
|------------------|-----------|-------------------|---------|
| Regular Hours | VIX 30.5 | 2 minutes | High volatility (VIX ≥ 25) |
| Regular Hours | VIX 20.0 | 5 minutes | Normal volatility |
| Regular Hours | VIX 12.0 | 10 minutes | Low volatility (VIX ≤ 15) |
| Pre-Market | Any VIX | 15 minutes | Extended hours |
| Market Closed | Any VIX | 30 minutes | Overnight |
| Any Time | Any VIX | 3 minutes | Command line: `--cycle-interval 3` |

---

## **🎯 Recommended Configurations**

### **Conservative Trading**
```bash
TRADING_CYCLE_INTERVAL_MINUTES=10
TRADING_CYCLE_HIGH_VIX_MINUTES=5
TRADING_CYCLE_NORMAL_VIX_MINUTES=10
TRADING_CYCLE_LOW_VIX_MINUTES=15
```

### **Balanced Trading (Default)**
```bash
TRADING_CYCLE_INTERVAL_MINUTES=5
TRADING_CYCLE_HIGH_VIX_MINUTES=2
TRADING_CYCLE_NORMAL_VIX_MINUTES=5
TRADING_CYCLE_LOW_VIX_MINUTES=10
```

### **Aggressive Trading (Live Trading)**
```bash
TRADING_CYCLE_INTERVAL_MINUTES=3
TRADING_CYCLE_HIGH_VIX_MINUTES=1
TRADING_CYCLE_NORMAL_VIX_MINUTES=3
TRADING_CYCLE_LOW_VIX_MINUTES=5
TRADING_CYCLE_HIGH_VIX_THRESHOLD=22.0
TRADING_CYCLE_LOW_VIX_THRESHOLD=18.0
```

---

## **🔧 Usage Examples**

### **Quick Testing with Fast Cycles**
```bash
# Test with 1-minute cycles
dotnet run --cycle-interval 1 --dry-run

# Test with 30-second cycles (minimum)
dotnet run --cycle-interval 1 --dry-run
```

### **Production Live Trading**
```bash
# Use environment configuration (recommended)
dotnet run

# Override for specific market conditions
dotnet run --cycle-interval 2  # During earnings season
```

### **Development and Debugging**
```bash
# Slower cycles for debugging
dotnet run --cycle-interval 15 --dry-run

# Single cycle for testing
dotnet run --single-cycle --dry-run
```

---

## **📈 Performance Impact**

### **Faster Cycles (1-3 minutes)**
- ✅ **Pros**: Quick response to momentum changes, better entry/exit timing
- ⚠️ **Cons**: Higher API usage, more frequent position adjustments

### **Moderate Cycles (5-10 minutes)**
- ✅ **Pros**: Balanced responsiveness and efficiency
- ✅ **Cons**: Good for most market conditions

### **Slower Cycles (15+ minutes)**
- ✅ **Pros**: Lower API usage, reduced noise
- ⚠️ **Cons**: May miss short-term opportunities

---

## **🛡️ Safety Features**

### **Automatic Validation**
- All intervals are clamped between `MIN_INTERVAL_MINUTES` and `MAX_INTERVAL_MINUTES`
- VIX thresholds are validated to ensure logical ordering
- Invalid configurations are automatically corrected with warnings

### **Fallback Behavior**
- If VIX data is unavailable → Use default interval
- If market session detection fails → Assume market is open
- If any error occurs → Fall back to default interval

### **Logging**
The system provides comprehensive logging:
```
=== TRADING CYCLE CONFIGURATION ===
Default Interval: 5 minutes
High Volatility Interval: 2 minutes (VIX >= 25.0)
Normal Volatility Interval: 5 minutes
Low Volatility Interval: 10 minutes (VIX <= 15.0)
Extended Hours Interval: 15 minutes
Overnight Interval: 30 minutes
VIX-Based Adjustment: True
Extended Hours Adjustment: True
=====================================

⏱️ Next cycle in 2 minutes (High volatility (VIX 28.5 >= 25.0): 2 minutes)
```

---

## **🔄 Migration from 30-Minute Cycles**

The old hardcoded 30-minute interval has been completely replaced. To maintain the old behavior:

```bash
# Set all intervals to 30 minutes
TRADING_CYCLE_INTERVAL_MINUTES=30
TRADING_CYCLE_HIGH_VIX_MINUTES=30
TRADING_CYCLE_NORMAL_VIX_MINUTES=30
TRADING_CYCLE_LOW_VIX_MINUTES=30
TRADING_CYCLE_ENABLE_VIX_ADJUSTMENT=false
```

However, we **strongly recommend** using the new dynamic system for better performance!
