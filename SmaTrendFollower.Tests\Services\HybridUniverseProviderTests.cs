﻿using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using Moq;
using Xunit;
using FluentAssertions;
using SmaTrendFollower.Services;
using SmaTrendFollower.Models;

namespace SmaTrendFollower.Tests.Services;

public class HybridUniverseProviderTests
{
    private readonly Mock<IDynamicUniverseProvider> _mockDynamicProvider;
    private readonly Mock<ILogger<HybridUniverseProvider>> _mockLogger;
    private readonly IConfiguration _configuration;

    public HybridUniverseProviderTests()
    {
        _mockDynamicProvider = new Mock<IDynamicUniverseProvider>();
        _mockLogger = new Mock<ILogger<HybridUniverseProvider>>();
    }

    [TestTimeout(TestTimeouts.Unit)]
    [Trait("Category", TestCategories.Database)]
    [Fact]
    public async Task GetSymbolsAsync_WithDynamicModeEnabled_ShouldUseDynamicProvider()
    {
        // Arrange
        var configData = new Dictionary<string, string>
        {
            {"USE_DYNAMIC_UNIVERSE", "true"}
        };
        var configuration = new ConfigurationBuilder()
            .AddInMemoryCollection(configData!)
            .Build();

        var expectedSymbols = new[] { "AAPL", "MSFT", "GOOGL" };
        _mockDynamicProvider.Setup(x => x.GetCachedUniverseAsync(It.IsAny<CancellationToken>()))
            .ReturnsAsync(expectedSymbols);

        var provider = new HybridUniverseProvider(_mockDynamicProvider.Object, configuration, _mockLogger.Object);

        // Act
        var result = await provider.GetSymbolsAsync();

        // Assert
        result.Should().BeEquivalentTo(expectedSymbols);
        _mockDynamicProvider.Verify(x => x.GetCachedUniverseAsync(It.IsAny<CancellationToken>()), Times.Once);
        provider.IsUsingDynamicUniverse.Should().BeTrue();
    }

    [TestTimeout(TestTimeouts.Unit)]
    [Trait("Category", TestCategories.Database)]
    [Fact]
    public async Task GetSymbolsAsync_WithDynamicModeDisabled_ShouldUseFileProvider()
    {
        // Arrange
        var configData = new Dictionary<string, string>
        {
            {"USE_DYNAMIC_UNIVERSE", "false"}
        };
        var configuration = new ConfigurationBuilder()
            .AddInMemoryCollection(configData!)
            .Build();

        var provider = new HybridUniverseProvider(_mockDynamicProvider.Object, configuration, _mockLogger.Object);

        // Act
        var result = await provider.GetSymbolsAsync();

        // Assert
        result.Should().NotBeEmpty(); // Should return default symbols from FileUniverseProvider
        _mockDynamicProvider.Verify(x => x.GetCachedUniverseAsync(It.IsAny<CancellationToken>()), Times.Never);
        provider.IsUsingDynamicUniverse.Should().BeFalse();
    }

    [TestTimeout(TestTimeouts.Unit)]
    [Trait("Category", TestCategories.Database)]
    [Fact]
    public async Task GetSymbolsAsync_WithDynamicProviderError_ShouldFallbackToFileProvider()
    {
        // Arrange
        var configData = new Dictionary<string, string>
        {
            {"USE_DYNAMIC_UNIVERSE", "true"}
        };
        var configuration = new ConfigurationBuilder()
            .AddInMemoryCollection(configData!)
            .Build();

        _mockDynamicProvider.Setup(x => x.GetCachedUniverseAsync(It.IsAny<CancellationToken>()))
            .ThrowsAsync(new Exception("Dynamic provider error"));

        var provider = new HybridUniverseProvider(_mockDynamicProvider.Object, configuration, _mockLogger.Object);

        // Act
        var result = await provider.GetSymbolsAsync();

        // Assert
        result.Should().NotBeEmpty(); // Should fallback to file provider
        _mockDynamicProvider.Verify(x => x.GetCachedUniverseAsync(It.IsAny<CancellationToken>()), Times.Once);
    }

    [TestTimeout(TestTimeouts.Unit)]
    [Trait("Category", TestCategories.Database)]
    [Fact]
    public async Task RefreshUniverseAsync_WithDynamicModeEnabled_ShouldCallDynamicProvider()
    {
        // Arrange
        var configData = new Dictionary<string, string>
        {
            {"USE_DYNAMIC_UNIVERSE", "true"}
        };
        var configuration = new ConfigurationBuilder()
            .AddInMemoryCollection(configData!)
            .Build();

        var expectedSymbols = new[] { "AAPL", "MSFT", "GOOGL" };
        _mockDynamicProvider.Setup(x => x.RefreshUniverseAsync(It.IsAny<IEnumerable<string>>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(expectedSymbols);

        var provider = new HybridUniverseProvider(_mockDynamicProvider.Object, configuration, _mockLogger.Object);

        // Act
        var result = await provider.RefreshUniverseAsync();

        // Assert
        result.Should().BeEquivalentTo(expectedSymbols);
        _mockDynamicProvider.Verify(x => x.RefreshUniverseAsync(It.IsAny<IEnumerable<string>>(), It.IsAny<CancellationToken>()), Times.Once);
    }

    [TestTimeout(TestTimeouts.Unit)]
    [Trait("Category", TestCategories.Database)]
    [Fact]
    public async Task RefreshUniverseAsync_WithDynamicModeDisabled_ShouldReturnCurrentSymbols()
    {
        // Arrange
        var configData = new Dictionary<string, string>
        {
            {"USE_DYNAMIC_UNIVERSE", "false"}
        };
        var configuration = new ConfigurationBuilder()
            .AddInMemoryCollection(configData!)
            .Build();

        var provider = new HybridUniverseProvider(_mockDynamicProvider.Object, configuration, _mockLogger.Object);

        // Act
        var result = await provider.RefreshUniverseAsync();

        // Assert
        result.Should().NotBeEmpty();
        _mockDynamicProvider.Verify(x => x.RefreshUniverseAsync(It.IsAny<IEnumerable<string>>(), It.IsAny<CancellationToken>()), Times.Never);
    }

    [TestTimeout(TestTimeouts.Unit)]
    [Trait("Category", TestCategories.Database)]
    [Fact]
    public async Task GetUniverseDetailsAsync_WithDynamicModeEnabled_ShouldReturnDetails()
    {
        // Arrange
        var configData = new Dictionary<string, string>
        {
            {"USE_DYNAMIC_UNIVERSE", "true"}
        };
        var configuration = new ConfigurationBuilder()
            .AddInMemoryCollection(configData!)
            .Build();

        var expectedDetails = new RedisUniverse
        {
            Symbols = new List<string> { "AAPL", "MSFT" },
            GeneratedAt = DateTime.UtcNow,
            CandidateCount = 10,
            QualifiedCount = 2
        };

        _mockDynamicProvider.Setup(x => x.GetUniverseDetailsAsync(It.IsAny<CancellationToken>()))
            .ReturnsAsync(expectedDetails);

        var provider = new HybridUniverseProvider(_mockDynamicProvider.Object, configuration, _mockLogger.Object);

        // Act
        var result = await provider.GetUniverseDetailsAsync();

        // Assert
        result.Should().NotBeNull();
        result!.Symbols.Should().HaveCount(2);
        result.CandidateCount.Should().Be(10);
        result.QualifiedCount.Should().Be(2);
        _mockDynamicProvider.Verify(x => x.GetUniverseDetailsAsync(It.IsAny<CancellationToken>()), Times.Once);
    }

    [TestTimeout(TestTimeouts.Unit)]
    [Trait("Category", TestCategories.Database)]
    [Fact]
    public async Task GetUniverseDetailsAsync_WithDynamicModeDisabled_ShouldReturnNull()
    {
        // Arrange
        var configData = new Dictionary<string, string>
        {
            {"USE_DYNAMIC_UNIVERSE", "false"}
        };
        var configuration = new ConfigurationBuilder()
            .AddInMemoryCollection(configData!)
            .Build();

        var provider = new HybridUniverseProvider(_mockDynamicProvider.Object, configuration, _mockLogger.Object);

        // Act
        var result = await provider.GetUniverseDetailsAsync();

        // Assert
        result.Should().BeNull();
        _mockDynamicProvider.Verify(x => x.GetUniverseDetailsAsync(It.IsAny<CancellationToken>()), Times.Never);
    }

    [TestTimeout(TestTimeouts.Unit)]
    [Trait("Category", TestCategories.Database)]
    [Fact]
    public async Task IsCacheValidAsync_WithDynamicModeEnabled_ShouldReturnValidityStatus()
    {
        // Arrange
        var configData = new Dictionary<string, string>
        {
            {"USE_DYNAMIC_UNIVERSE", "true"}
        };
        var configuration = new ConfigurationBuilder()
            .AddInMemoryCollection(configData!)
            .Build();

        _mockDynamicProvider.Setup(x => x.IsCacheValidAsync(It.IsAny<CancellationToken>()))
            .ReturnsAsync(true);

        var provider = new HybridUniverseProvider(_mockDynamicProvider.Object, configuration, _mockLogger.Object);

        // Act
        var result = await provider.IsCacheValidAsync();

        // Assert
        result.Should().BeTrue();
        _mockDynamicProvider.Verify(x => x.IsCacheValidAsync(It.IsAny<CancellationToken>()), Times.Once);
    }

    [TestTimeout(TestTimeouts.Unit)]
    [Trait("Category", TestCategories.Database)]
    [Fact]
    public async Task IsCacheValidAsync_WithDynamicModeDisabled_ShouldReturnTrue()
    {
        // Arrange
        var configData = new Dictionary<string, string>
        {
            {"USE_DYNAMIC_UNIVERSE", "false"}
        };
        var configuration = new ConfigurationBuilder()
            .AddInMemoryCollection(configData!)
            .Build();

        var provider = new HybridUniverseProvider(_mockDynamicProvider.Object, configuration, _mockLogger.Object);

        // Act
        var result = await provider.IsCacheValidAsync();

        // Assert
        result.Should().BeTrue(); // File-based universe is always "valid"
        _mockDynamicProvider.Verify(x => x.IsCacheValidAsync(It.IsAny<CancellationToken>()), Times.Never);
    }

    [TestTimeout(TestTimeouts.Unit)]
    [Trait("Category", TestCategories.Database)]
    [Theory]
    [InlineData("true", true)]
    [InlineData("false", false)]
    [InlineData("", true)] // Default should be true
    public void IsUsingDynamicUniverse_ShouldReflectConfiguration(string configValue, bool expectedValue)
    {
        // Arrange
        var configData = new Dictionary<string, string>();
        if (!string.IsNullOrEmpty(configValue))
        {
            configData["USE_DYNAMIC_UNIVERSE"] = configValue;
        }

        var configuration = new ConfigurationBuilder()
            .AddInMemoryCollection(configData!)
            .Build();

        var provider = new HybridUniverseProvider(_mockDynamicProvider.Object, configuration, _mockLogger.Object);

        // Act & Assert
        provider.IsUsingDynamicUniverse.Should().Be(expectedValue);
    }
}
