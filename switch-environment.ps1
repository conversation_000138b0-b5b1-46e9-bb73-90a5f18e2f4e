# SmaTrendFollower Environment Switcher
# Usage: .\switch-environment.ps1 [live|paper]

param(
    [Parameter(Mandatory=$true)]
    [ValidateSet("live", "paper")]
    [string]$Environment
)

$envFile = ".env"

if (-not (Test-Path $envFile)) {
    Write-Error "Environment file .env not found!"
    exit 1
}

$content = Get-Content $envFile

if ($Environment -eq "live") {
    Write-Host "Switching to LIVE TRADING environment..." -ForegroundColor Red
    Write-Host "WARNING: This will use REAL MONEY!" -ForegroundColor Yellow
    
    # Enable live credentials
    $content = $content -replace "^# (APCA_API_KEY_ID=AKGBPW5HD8LVI5C6NJUJ)", '$1'
    $content = $content -replace "^# (APCA_API_SECRET_KEY=MdrZZtKbFtLQWNuggQMDqN12loiOheHWjSiWN2CM)", '$1'
    $content = $content -replace "^# (APCA_API_ENV=live)", '$1'
    
    # Disable paper credentials
    $content = $content -replace "^(APCA_API_KEY_ID=PK0AM3WB1CES3YBQPGR0)", '# $1'
    $content = $content -replace "^(APCA_API_SECRET_KEY=2O4bJsHpGjyYU6FvQ956kIJaLePdBihZveKutbtf)", '# $1'
    $content = $content -replace "^(APCA_API_ENV=paper)", '# $1'
    
    # Set safety environment
    $content = $content -replace "^SAFETY_ALLOWED_ENVIRONMENT=.*", "SAFETY_ALLOWED_ENVIRONMENT=Live"
    $content = $content -replace "^SAFETY_REQUIRE_CONFIRMATION=.*", "SAFETY_REQUIRE_CONFIRMATION=true"
    
} elseif ($Environment -eq "paper") {
    Write-Host "Switching to PAPER TRADING environment..." -ForegroundColor Green
    Write-Host "Safe mode: No real money will be used" -ForegroundColor Green
    
    # Disable live credentials
    $content = $content -replace "^(APCA_API_KEY_ID=AKGBPW5HD8LVI5C6NJUJ)", '# $1'
    $content = $content -replace "^(APCA_API_SECRET_KEY=MdrZZtKbFtLQWNuggQMDqN12loiOheHWjSiWN2CM)", '# $1'
    $content = $content -replace "^(APCA_API_ENV=live)", '# $1'
    
    # Enable paper credentials
    $content = $content -replace "^# (APCA_API_KEY_ID=PK0AM3WB1CES3YBQPGR0)", '$1'
    $content = $content -replace "^# (APCA_API_SECRET_KEY=2O4bJsHpGjyYU6FvQ956kIJaLePdBihZveKutbtf)", '$1'
    $content = $content -replace "^# (APCA_API_ENV=paper)", '$1'
    
    # Set safety environment
    $content = $content -replace "^SAFETY_ALLOWED_ENVIRONMENT=.*", "SAFETY_ALLOWED_ENVIRONMENT=Paper"
    $content = $content -replace "^SAFETY_REQUIRE_CONFIRMATION=.*", "SAFETY_REQUIRE_CONFIRMATION=false"
}

# Write back to file
$content | Set-Content $envFile

Write-Host "Environment switched to: $Environment" -ForegroundColor Cyan
Write-Host ""
Write-Host "Current configuration:" -ForegroundColor White
Write-Host "  Environment: $Environment" -ForegroundColor White

if ($Environment -eq "live") {
    Write-Host "  API Key: AKGBPW5HD8LVI5C6NJUJ" -ForegroundColor White
    Write-Host "  Confirmation Required: Yes" -ForegroundColor White
    Write-Host ""
    Write-Host "REMINDER: Use --confirm flag for live trades" -ForegroundColor Yellow
} else {
    Write-Host "  API Key: PK0AM3WB1CES3YBQPGR0" -ForegroundColor White
    Write-Host "  Confirmation Required: No" -ForegroundColor White
    Write-Host ""
    Write-Host "Paper trading mode - safe for testing" -ForegroundColor Green
}
